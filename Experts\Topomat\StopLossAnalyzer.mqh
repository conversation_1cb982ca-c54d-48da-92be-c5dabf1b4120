//+------------------------------------------------------------------+
//| StopLossAnalyzer.mqh                                             |
//| Sistema de Análise Automática de Stop Loss Otimizado v4.3.2     |
//| Análise de Cenários Múltiplos com Otimização Multi-Objetivo     |
//+------------------------------------------------------------------+

#property copyright "Topomat EA"
#property version   "4.3.2"

//+------------------------------------------------------------------+
//| Estruturas de dados para análise de Stop Loss                   |
//+------------------------------------------------------------------+

//--- Trade histórico para análise
struct HistoricalTrade
{
    datetime entry_time;              // Horário de entrada
    double entry_price;               // Preço de entrada
    double exit_price;                // Preço de saída real
    bool is_buy;                      // Se é compra (true) ou venda (false)
    double volume;                    // Volume do trade
    string entry_method;              // Método de entrada (Candle, Volume, RSI)
    double actual_profit_points;      // Lucro real em pontos
    double max_adverse_excursion;     // Máximo movimento contra (em pontos)
    double max_favorable_excursion;   // Máximo movimento a favor (em pontos)
    int duration_minutes;             // Duração em minutos
    double volatility_atr;            // ATR no momento da entrada
    double spread_entry;              // Spread na entrada
    int hour_entry;                   // Hora da entrada (0-23)
};

//--- Cenário de Stop Loss simulado
struct SLScenario
{
    int sl_points;                    // Nível de SL em pontos
    double profit_factor;             // Profit Factor (Lucro Total / Perda Total)
    double win_rate;                  // Taxa de acerto (%)
    double expectancy;                // Expectativa por trade em pontos
    double max_drawdown_points;       // Máximo drawdown em pontos
    double recovery_factor;           // Recovery Factor (Lucro Líquido / Max DD)
    double balanced_score;            // Score balanceado (0-10)
    int total_trades;                 // Total de trades
    int winning_trades;               // Trades vencedores
    int losing_trades;                // Trades perdedores
    double total_profit_points;       // Lucro total em pontos
    double total_loss_points;         // Perda total em pontos
    double net_profit_points;         // Lucro líquido em pontos
    double avg_win_points;            // Média de ganho por trade vencedor
    double avg_loss_points;           // Média de perda por trade perdedor
    double largest_win;               // Maior ganho individual
    double largest_loss;              // Maior perda individual
    int consecutive_wins_max;         // Máximo de vitórias consecutivas
    int consecutive_losses_max;       // Máximo de perdas consecutivas
};

//--- Resultado da otimização
struct OptimizationResult
{
    int recommended_sl;               // SL recomendado em pontos
    double confidence_score;          // Confiança da recomendação (0-100%)
    string analysis_summary;          // Resumo da análise
    SLScenario scenarios[20];         // Cenários analisados (50-1000 pontos)
    int total_scenarios;              // Número total de cenários
    datetime analysis_date;           // Data da análise
    int historical_trades_count;      // Quantidade de trades históricos analisados
    string period_analyzed;           // Período analisado
    double current_sl_score;          // Score do SL atual
    double improvement_potential;     // Potencial de melhoria (%)
};

//+------------------------------------------------------------------+
//| Classe principal para análise de Stop Loss                      |
//+------------------------------------------------------------------+
class CStopLossAnalyzer
{
private:
    HistoricalTrade m_historical_trades[];  // Array de trades históricos
    SLScenario m_scenarios[];               // Array de cenários simulados
    OptimizationResult m_last_result;       // Último resultado de análise
    
    // Configurações da análise
    int m_analysis_period_days;             // Período de análise em dias (30)
    int m_sl_levels[];                      // Níveis de SL para testar
    int m_total_sl_levels;                  // Total de níveis de SL
    
    // Pesos para score balanceado
    double m_weight_profit_factor;          // Peso do Profit Factor (0.30)
    double m_weight_win_rate;               // Peso da Win Rate (0.20)
    double m_weight_recovery_factor;        // Peso do Recovery Factor (0.30)
    double m_weight_expectancy;             // Peso da Expectância (0.20)
    
public:
    //--- Construtor
    CStopLossAnalyzer();
    
    //--- Destrutor
    ~CStopLossAnalyzer();
    
    //--- Métodos principais
    bool InitializeAnalyzer();              // Inicializar analisador
    bool LoadHistoricalData();              // Carregar dados históricos
    bool RunAnalysis();                     // Executar análise completa
    bool GenerateHTMLReport();              // Gerar relatório HTML
    
    //--- Métodos de simulação
    bool SimulateScenario(int sl_points);   // Simular cenário específico
    double CalculateBalancedScore(const SLScenario &scenario); // Calcular score
    
    //--- Métodos de otimização
    int FindOptimalSL();                    // Encontrar SL ótimo
    double CalculateConfidence();           // Calcular confiança
    
    //--- Métodos de acesso
    OptimizationResult GetLastResult() { return m_last_result; }
    bool IsAnalysisReady() { return m_last_result.total_scenarios > 0; }
    
    //--- Métodos utilitários
    string FormatScenarioSummary(const SLScenario &scenario);
    string GetAnalysisStatus();
    void LogAnalysisProgress(string message);
};

//+------------------------------------------------------------------+
//| Construtor                                                       |
//+------------------------------------------------------------------+
CStopLossAnalyzer::CStopLossAnalyzer()
{
    // Configurações padrão
    m_analysis_period_days = 30;  // 1 mês
    
    // Pesos para score balanceado
    m_weight_profit_factor = 0.30;
    m_weight_win_rate = 0.20;
    m_weight_recovery_factor = 0.30;
    m_weight_expectancy = 0.20;
    
    // Níveis de SL para testar (50 a 1000 pontos, de 50 em 50)
    m_total_sl_levels = 20;
    ArrayResize(m_sl_levels, m_total_sl_levels);
    
    for(int i = 0; i < m_total_sl_levels; i++)
    {
        m_sl_levels[i] = 50 + (i * 50); // 50, 100, 150, ..., 1000
    }
    
    // Inicializar arrays
    ArrayResize(m_scenarios, m_total_sl_levels);
    
    // Zerar resultado
    ZeroMemory(m_last_result);
}

//+------------------------------------------------------------------+
//| Destrutor                                                        |
//+------------------------------------------------------------------+
CStopLossAnalyzer::~CStopLossAnalyzer()
{
    ArrayFree(m_historical_trades);
    ArrayFree(m_scenarios);
    ArrayFree(m_sl_levels);
}

//+------------------------------------------------------------------+
//| Inicializar analisador                                          |
//+------------------------------------------------------------------+
bool CStopLossAnalyzer::InitializeAnalyzer()
{
    LogAnalysisProgress("🔧 Inicializando analisador de Stop Loss...");
    
    // Verificar se temos dados suficientes
    if(!LoadHistoricalData())
    {
        LogAnalysisProgress("❌ Falha ao carregar dados históricos");
        return false;
    }
    
    if(ArraySize(m_historical_trades) < 10)
    {
        LogAnalysisProgress("⚠️ Dados insuficientes para análise (mínimo 10 trades)");
        return false;
    }
    
    LogAnalysisProgress("✅ Analisador inicializado com " + IntegerToString(ArraySize(m_historical_trades)) + " trades");
    return true;
}

//+------------------------------------------------------------------+
//| Log de progresso da análise                                     |
//+------------------------------------------------------------------+
void CStopLossAnalyzer::LogAnalysisProgress(string message)
{
    Print("📊 SL Analyzer: " + message);
    
    // Também gravar em arquivo de log específico
    datetime now = TimeCurrent();
    string date_str = TimeToString(now, TIME_DATE);
    StringReplace(date_str, ".", "_");
    
    string log_filename = "TopoLogs\\StopLoss_Analysis_" + date_str + ".txt";
    int handle = FileOpen(log_filename, FILE_WRITE|FILE_TXT|FILE_ANSI|FILE_COMMON);
    
    if(handle != INVALID_HANDLE)
    {
        string timestamp = TimeToString(now, TIME_SECONDS);
        FileSeek(handle, 0, SEEK_END);
        FileWriteString(handle, "[" + timestamp + "] " + message + "\n");
        FileClose(handle);
    }
}

//+------------------------------------------------------------------+
//| Obter status da análise                                         |
//+------------------------------------------------------------------+
string CStopLossAnalyzer::GetAnalysisStatus()
{
    if(!IsAnalysisReady())
        return "❌ Análise não executada";

    string status = "✅ Análise concluída em " + TimeToString(m_last_result.analysis_date, TIME_MINUTES);
    status += " | SL Recomendado: " + IntegerToString(m_last_result.recommended_sl) + " pts";
    status += " | Confiança: " + DoubleToString(m_last_result.confidence_score, 1) + "%";

    return status;
}

//+------------------------------------------------------------------+
//| Carregar dados históricos do último mês                         |
//+------------------------------------------------------------------+
bool CStopLossAnalyzer::LoadHistoricalData()
{
    LogAnalysisProgress("📊 Carregando dados históricos dos últimos " + IntegerToString(m_analysis_period_days) + " dias...");

    // Calcular período de análise
    datetime end_time = TimeCurrent();
    datetime start_time = end_time - (m_analysis_period_days * 24 * 3600);

    // Limpar array anterior
    ArrayFree(m_historical_trades);

    // Carregar histórico de deals do MT5
    if(!HistorySelect(start_time, end_time))
    {
        LogAnalysisProgress("❌ Falha ao selecionar histórico do período");
        return false;
    }

    int total_deals = HistoryDealsTotal();
    LogAnalysisProgress("📈 Encontrados " + IntegerToString(total_deals) + " deals no período");

    // Array temporário para coletar trades
    HistoricalTrade temp_trades[];
    int trade_count = 0;

    // Processar deals e agrupar em trades
    for(int i = 0; i < total_deals; i++)
    {
        ulong deal_ticket = HistoryDealGetTicket(i);
        if(deal_ticket == 0) continue;

        // Verificar se é deal de saída (fechamento)
        if(HistoryDealGetInteger(deal_ticket, DEAL_ENTRY) != DEAL_ENTRY_OUT) continue;

        // Verificar se é do nosso EA (magic number)
        if(HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) != InpMagicNumber) continue;

        // Obter informações do deal
        datetime deal_time = (datetime)HistoryDealGetInteger(deal_ticket, DEAL_TIME);
        double deal_price = HistoryDealGetDouble(deal_ticket, DEAL_PRICE);
        double deal_volume = HistoryDealGetDouble(deal_ticket, DEAL_VOLUME);
        double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
        ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(deal_ticket, DEAL_TYPE);

        // Buscar deal de entrada correspondente
        ulong position_id = HistoryDealGetInteger(deal_ticket, DEAL_POSITION_ID);

        // Encontrar deal de entrada da mesma posição
        datetime entry_time = 0;
        double entry_price = 0;
        bool found_entry = false;

        for(int j = 0; j < total_deals; j++)
        {
            ulong entry_ticket = HistoryDealGetTicket(j);
            if(entry_ticket == 0) continue;

            if(HistoryDealGetInteger(entry_ticket, DEAL_POSITION_ID) == position_id &&
               HistoryDealGetInteger(entry_ticket, DEAL_ENTRY) == DEAL_ENTRY_IN)
            {
                entry_time = (datetime)HistoryDealGetInteger(entry_ticket, DEAL_TIME);
                entry_price = HistoryDealGetDouble(entry_ticket, DEAL_PRICE);
                found_entry = true;
                break;
            }
        }

        if(!found_entry) continue;

        // Calcular informações do trade
        bool is_buy = (deal_type == DEAL_TYPE_SELL); // Deal de saída oposto à entrada
        double profit_points = is_buy ? (deal_price - entry_price) : (entry_price - deal_price);
        int duration_minutes = (int)((deal_time - entry_time) / 60);

        // Adicionar ao array temporário
        ArrayResize(temp_trades, trade_count + 1);

        temp_trades[trade_count].entry_time = entry_time;
        temp_trades[trade_count].entry_price = entry_price;
        temp_trades[trade_count].exit_price = deal_price;
        temp_trades[trade_count].is_buy = is_buy;
        temp_trades[trade_count].volume = deal_volume;
        temp_trades[trade_count].actual_profit_points = profit_points;
        temp_trades[trade_count].duration_minutes = duration_minutes;
        temp_trades[trade_count].entry_method = "Historical"; // Será refinado depois

        // Extrair hora da entrada
        MqlDateTime dt;
        TimeToStruct(entry_time, dt);
        temp_trades[trade_count].hour_entry = dt.hour;

        // Calcular ATR aproximado (simplificado)
        temp_trades[trade_count].volatility_atr = 10.0; // Valor padrão, pode ser refinado
        temp_trades[trade_count].spread_entry = 1.0;    // Valor padrão

        // MAE e MFE serão calculados na simulação (dados não disponíveis no histórico)
        temp_trades[trade_count].max_adverse_excursion = 0;
        temp_trades[trade_count].max_favorable_excursion = MathAbs(profit_points);

        trade_count++;
    }

    // Copiar para array principal
    ArrayResize(m_historical_trades, trade_count);
    for(int i = 0; i < trade_count; i++)
    {
        m_historical_trades[i] = temp_trades[i];
    }

    LogAnalysisProgress("✅ Carregados " + IntegerToString(trade_count) + " trades históricos para análise");
    return trade_count > 0;
}

//+------------------------------------------------------------------+
//| Executar análise completa de todos os cenários                  |
//+------------------------------------------------------------------+
bool CStopLossAnalyzer::RunAnalysis()
{
    LogAnalysisProgress("🚀 Iniciando análise completa de Stop Loss...");

    if(ArraySize(m_historical_trades) == 0)
    {
        LogAnalysisProgress("❌ Nenhum trade histórico disponível");
        return false;
    }

    // Simular todos os cenários de SL
    for(int i = 0; i < m_total_sl_levels; i++)
    {
        int sl_points = m_sl_levels[i];
        LogAnalysisProgress("📊 Simulando cenário SL = " + IntegerToString(sl_points) + " pontos...");

        if(!SimulateScenario(sl_points))
        {
            LogAnalysisProgress("⚠️ Falha na simulação do cenário " + IntegerToString(sl_points) + " pontos");
            continue;
        }
    }

    // Encontrar SL ótimo
    int optimal_sl = FindOptimalSL();
    double confidence = CalculateConfidence();

    // Preparar resultado final
    m_last_result.recommended_sl = optimal_sl;
    m_last_result.confidence_score = confidence;
    m_last_result.analysis_date = TimeCurrent();
    m_last_result.historical_trades_count = ArraySize(m_historical_trades);
    m_last_result.total_scenarios = m_total_sl_levels;
    m_last_result.period_analyzed = IntegerToString(m_analysis_period_days) + " dias";

    // Copiar cenários para resultado
    for(int i = 0; i < m_total_sl_levels; i++)
    {
        m_last_result.scenarios[i] = m_scenarios[i];
    }

    // Gerar resumo
    m_last_result.analysis_summary = "Análise baseada em " + IntegerToString(ArraySize(m_historical_trades)) +
                                   " trades dos últimos " + IntegerToString(m_analysis_period_days) + " dias. " +
                                   "SL recomendado: " + IntegerToString(optimal_sl) + " pontos com " +
                                   DoubleToString(confidence, 1) + "% de confiança.";

    LogAnalysisProgress("✅ Análise concluída! SL recomendado: " + IntegerToString(optimal_sl) + " pontos");
    return true;
}

//+------------------------------------------------------------------+
//| Simular cenário específico de Stop Loss                         |
//+------------------------------------------------------------------+
bool CStopLossAnalyzer::SimulateScenario(int sl_points)
{
    // Encontrar índice do cenário
    int scenario_index = -1;
    for(int i = 0; i < m_total_sl_levels; i++)
    {
        if(m_sl_levels[i] == sl_points)
        {
            scenario_index = i;
            break;
        }
    }

    if(scenario_index == -1) return false;

    // Inicializar cenário
    SLScenario scenario;
    ZeroMemory(scenario);
    scenario.sl_points = sl_points;

    // Variáveis para cálculos
    double total_profit = 0;
    double total_loss = 0;
    int winning_trades = 0;
    int losing_trades = 0;
    double largest_win = 0;
    double largest_loss = 0;
    int consecutive_wins = 0;
    int consecutive_losses = 0;
    int max_consecutive_wins = 0;
    int max_consecutive_losses = 0;
    double running_balance = 0;
    double peak_balance = 0;
    double max_drawdown = 0;

    // Simular cada trade histórico com o SL especificado
    for(int i = 0; i < ArraySize(m_historical_trades); i++)
    {
        HistoricalTrade hist_trade = m_historical_trades[i];
        double trade_result;

        // Simular resultado com SL
        if(MathAbs(hist_trade.actual_profit_points) >= sl_points && hist_trade.actual_profit_points < 0)
        {
            // Trade teria sido parado pelo SL
            trade_result = -sl_points;
        }
        else
        {
            // Trade teria continuado até o resultado real
            trade_result = hist_trade.actual_profit_points;
        }

        // Atualizar estatísticas
        if(trade_result > 0)
        {
            total_profit += trade_result;
            winning_trades++;
            consecutive_wins++;
            consecutive_losses = 0;

            if(trade_result > largest_win)
                largest_win = trade_result;

            if(consecutive_wins > max_consecutive_wins)
                max_consecutive_wins = consecutive_wins;
        }
        else
        {
            total_loss += MathAbs(trade_result);
            losing_trades++;
            consecutive_losses++;
            consecutive_wins = 0;

            if(MathAbs(trade_result) > largest_loss)
                largest_loss = MathAbs(trade_result);

            if(consecutive_losses > max_consecutive_losses)
                max_consecutive_losses = consecutive_losses;
        }

        // Atualizar drawdown
        running_balance += trade_result;
        if(running_balance > peak_balance)
            peak_balance = running_balance;

        double current_drawdown = peak_balance - running_balance;
        if(current_drawdown > max_drawdown)
            max_drawdown = current_drawdown;
    }

    // Calcular métricas finais
    int total_trades = ArraySize(m_historical_trades);
    scenario.total_trades = total_trades;
    scenario.winning_trades = winning_trades;
    scenario.losing_trades = losing_trades;
    scenario.total_profit_points = total_profit;
    scenario.total_loss_points = total_loss;
    scenario.net_profit_points = total_profit - total_loss;
    scenario.largest_win = largest_win;
    scenario.largest_loss = largest_loss;
    scenario.consecutive_wins_max = max_consecutive_wins;
    scenario.consecutive_losses_max = max_consecutive_losses;
    scenario.max_drawdown_points = max_drawdown;

    // Calcular métricas derivadas
    scenario.win_rate = total_trades > 0 ? (double)winning_trades / total_trades * 100.0 : 0;
    scenario.profit_factor = total_loss > 0 ? total_profit / total_loss : (total_profit > 0 ? 999.0 : 0);
    scenario.expectancy = total_trades > 0 ? (total_profit - total_loss) / total_trades : 0;
    scenario.recovery_factor = max_drawdown > 0 ? scenario.net_profit_points / max_drawdown :
                              (scenario.net_profit_points > 0 ? 999.0 : 0);
    scenario.avg_win_points = winning_trades > 0 ? total_profit / winning_trades : 0;
    scenario.avg_loss_points = losing_trades > 0 ? total_loss / losing_trades : 0;

    // Calcular score balanceado
    scenario.balanced_score = CalculateBalancedScore(scenario);

    // Salvar cenário
    m_scenarios[scenario_index] = scenario;

    return true;
}

//+------------------------------------------------------------------+
//| Calcular score balanceado para um cenário                       |
//+------------------------------------------------------------------+
double CStopLossAnalyzer::CalculateBalancedScore(const SLScenario &scenario)
{
    // Normalizar métricas para escala 0-10
    double pf_score = 0;
    double wr_score = 0;
    double rf_score = 0;
    double exp_score = 0;

    // Profit Factor (1.0-3.0 -> 0-10)
    if(scenario.profit_factor >= 1.0)
    {
        pf_score = MathMin(10.0, (scenario.profit_factor - 1.0) / 2.0 * 10.0);
    }

    // Win Rate (0-100% -> 0-10)
    wr_score = scenario.win_rate / 10.0;
    if(wr_score > 10.0) wr_score = 10.0;

    // Recovery Factor (0-10 -> 0-10)
    if(scenario.recovery_factor >= 0)
    {
        rf_score = MathMin(10.0, scenario.recovery_factor);
    }

    // Expectancy (normalizar baseado em valores típicos)
    if(scenario.expectancy > 0)
    {
        exp_score = MathMin(10.0, scenario.expectancy / 5.0); // 5 pontos = score 1
    }

    // Penalização por drawdown excessivo
    double dd_penalty = 0;
    if(scenario.max_drawdown_points > 100) // Penalizar DD > 100 pontos
    {
        dd_penalty = (scenario.max_drawdown_points - 100) / 50.0; // -1 por cada 50 pontos extras
        dd_penalty = MathMin(dd_penalty, 5.0); // Máximo -5 pontos
    }

    // Calcular score final
    double final_score = (pf_score * m_weight_profit_factor) +
                        (wr_score * m_weight_win_rate) +
                        (rf_score * m_weight_recovery_factor) +
                        (exp_score * m_weight_expectancy) -
                        dd_penalty;

    return MathMax(0, final_score); // Não permitir score negativo
}

//+------------------------------------------------------------------+
//| Encontrar Stop Loss ótimo                                       |
//+------------------------------------------------------------------+
int CStopLossAnalyzer::FindOptimalSL()
{
    double best_score = -1;
    int best_sl = 50; // Padrão

    for(int i = 0; i < m_total_sl_levels; i++)
    {
        if(m_scenarios[i].balanced_score > best_score)
        {
            best_score = m_scenarios[i].balanced_score;
            best_sl = m_scenarios[i].sl_points;
        }
    }

    return best_sl;
}

//+------------------------------------------------------------------+
//| Calcular confiança da recomendação                              |
//+------------------------------------------------------------------+
double CStopLossAnalyzer::CalculateConfidence()
{
    if(ArraySize(m_historical_trades) < 10)
        return 30.0; // Baixa confiança com poucos dados

    if(ArraySize(m_historical_trades) < 30)
        return 60.0; // Confiança média

    if(ArraySize(m_historical_trades) < 50)
        return 80.0; // Boa confiança

    return 95.0; // Alta confiança com muitos dados
}

//+------------------------------------------------------------------+
//| Gerar relatório HTML                                            |
//+------------------------------------------------------------------+
bool CStopLossAnalyzer::GenerateHTMLReport()
{
    if(!IsAnalysisReady())
    {
        LogAnalysisProgress("❌ Análise não foi executada ainda");
        return false;
    }

    LogAnalysisProgress("📄 Gerando relatório HTML...");

    // Nome do arquivo
    datetime now = TimeCurrent();
    string date_str = TimeToString(now, TIME_DATE);
    StringReplace(date_str, ".", "_");
    string time_str = TimeToString(now, TIME_MINUTES);
    StringReplace(time_str, ":", "");

    string filename = "TopoLogs\\StopLoss_Report_" + date_str + "_" + time_str + ".html";

    // Abrir arquivo para escrita
    int handle = FileOpen(filename, FILE_WRITE|FILE_TXT|FILE_ANSI|FILE_COMMON);
    if(handle == INVALID_HANDLE)
    {
        LogAnalysisProgress("❌ Falha ao criar arquivo HTML: " + filename);
        return false;
    }

    // Escrever cabeçalho HTML
    FileWriteString(handle, "<!DOCTYPE html>\n");
    FileWriteString(handle, "<html lang='pt-BR'>\n");
    FileWriteString(handle, "<head>\n");
    FileWriteString(handle, "    <meta charset='UTF-8'>\n");
    FileWriteString(handle, "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n");
    FileWriteString(handle, "    <title>Análise de Stop Loss - Topomat EA</title>\n");
    FileWriteString(handle, "    <style>\n");
    FileWriteString(handle, "        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n");
    FileWriteString(handle, "        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n");
    FileWriteString(handle, "        h1 { color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }\n");
    FileWriteString(handle, "        h2 { color: #34495e; border-left: 4px solid #3498db; padding-left: 10px; }\n");
    FileWriteString(handle, "        .summary { background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0; }\n");
    FileWriteString(handle, "        .recommended { background: #d5f4e6; border: 2px solid #27ae60; padding: 15px; border-radius: 5px; margin: 20px 0; }\n");
    FileWriteString(handle, "        table { width: 100%; border-collapse: collapse; margin: 20px 0; }\n");
    FileWriteString(handle, "        th, td { padding: 12px; text-align: center; border: 1px solid #ddd; }\n");
    FileWriteString(handle, "        th { background-color: #3498db; color: white; }\n");
    FileWriteString(handle, "        .best-row { background-color: #d5f4e6; font-weight: bold; }\n");
    FileWriteString(handle, "        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px; min-width: 120px; }\n");
    FileWriteString(handle, "        .metric-value { font-size: 1.2em; font-weight: bold; color: #2c3e50; }\n");
    FileWriteString(handle, "        .metric-label { font-size: 0.9em; color: #7f8c8d; }\n");
    FileWriteString(handle, "    </style>\n");
    FileWriteString(handle, "</head>\n");
    FileWriteString(handle, "<body>\n");

    // Título e informações gerais
    FileWriteString(handle, "<div class='container'>\n");
    FileWriteString(handle, "    <h1>📊 Análise Automática de Stop Loss</h1>\n");
    FileWriteString(handle, "    <div class='summary'>\n");
    FileWriteString(handle, "        <h2>📋 Resumo da Análise</h2>\n");
    FileWriteString(handle, "        <p><strong>Data da Análise:</strong> " + TimeToString(m_last_result.analysis_date, TIME_SECONDS) + "</p>\n");
    FileWriteString(handle, "        <p><strong>Período Analisado:</strong> " + m_last_result.period_analyzed + "</p>\n");
    FileWriteString(handle, "        <p><strong>Trades Analisados:</strong> " + IntegerToString(m_last_result.historical_trades_count) + "</p>\n");
    FileWriteString(handle, "        <p><strong>Cenários Simulados:</strong> " + IntegerToString(m_last_result.total_scenarios) + " (50 a 1000 pontos)</p>\n");
    FileWriteString(handle, "    </div>\n");

    // Continuar escrevendo o relatório (reabrindo arquivo)
    handle = FileOpen(filename, FILE_WRITE|FILE_TXT|FILE_ANSI|FILE_COMMON);
    if(handle == INVALID_HANDLE) return false;

    // Pular para o final do arquivo
    FileSeek(handle, 0, SEEK_END);

    // Recomendação principal
    FileWriteString(handle, "    <div class='recommended'>\n");
    FileWriteString(handle, "        <h2>🎯 Recomendação Principal</h2>\n");
    FileWriteString(handle, "        <div class='metric'>\n");
    FileWriteString(handle, "            <div class='metric-value'>" + IntegerToString(m_last_result.recommended_sl) + " pontos</div>\n");
    FileWriteString(handle, "            <div class='metric-label'>Stop Loss Recomendado</div>\n");
    FileWriteString(handle, "        </div>\n");
    FileWriteString(handle, "        <div class='metric'>\n");
    FileWriteString(handle, "            <div class='metric-value'>" + DoubleToString(m_last_result.confidence_score, 1) + "%</div>\n");
    FileWriteString(handle, "            <div class='metric-label'>Confiança</div>\n");
    FileWriteString(handle, "        </div>\n");
    FileWriteString(handle, "        <p><strong>Justificativa:</strong> " + m_last_result.analysis_summary + "</p>\n");
    FileWriteString(handle, "    </div>\n");

    // Tabela de cenários
    FileWriteString(handle, "    <h2>📊 Comparação de Cenários</h2>\n");
    FileWriteString(handle, "    <table>\n");
    FileWriteString(handle, "        <thead>\n");
    FileWriteString(handle, "            <tr>\n");
    FileWriteString(handle, "                <th>SL (pts)</th>\n");
    FileWriteString(handle, "                <th>Score</th>\n");
    FileWriteString(handle, "                <th>Profit Factor</th>\n");
    FileWriteString(handle, "                <th>Win Rate (%)</th>\n");
    FileWriteString(handle, "                <th>Expectância</th>\n");
    FileWriteString(handle, "                <th>Max DD (pts)</th>\n");
    FileWriteString(handle, "                <th>Recovery Factor</th>\n");
    FileWriteString(handle, "                <th>Lucro Líquido</th>\n");
    FileWriteString(handle, "            </tr>\n");
    FileWriteString(handle, "        </thead>\n");
    FileWriteString(handle, "        <tbody>\n");

    // Adicionar linhas da tabela
    for(int i = 0; i < m_last_result.total_scenarios; i++)
    {
        SLScenario scenario = m_last_result.scenarios[i];
        bool is_recommended = (scenario.sl_points == m_last_result.recommended_sl);

        string row_class = is_recommended ? " class='best-row'" : "";

        FileWriteString(handle, "            <tr" + row_class + ">\n");
        FileWriteString(handle, "                <td>" + IntegerToString(scenario.sl_points) + "</td>\n");
        FileWriteString(handle, "                <td>" + DoubleToString(scenario.balanced_score, 2) + "</td>\n");
        FileWriteString(handle, "                <td>" + DoubleToString(scenario.profit_factor, 2) + "</td>\n");
        FileWriteString(handle, "                <td>" + DoubleToString(scenario.win_rate, 1) + "%</td>\n");
        FileWriteString(handle, "                <td>" + DoubleToString(scenario.expectancy, 2) + "</td>\n");
        FileWriteString(handle, "                <td>" + DoubleToString(scenario.max_drawdown_points, 1) + "</td>\n");
        FileWriteString(handle, "                <td>" + DoubleToString(scenario.recovery_factor, 2) + "</td>\n");
        FileWriteString(handle, "                <td>" + DoubleToString(scenario.net_profit_points, 1) + "</td>\n");
        FileWriteString(handle, "            </tr>\n");
    }

    FileWriteString(handle, "        </tbody>\n");
    FileWriteString(handle, "    </table>\n");

    // Detalhes do cenário recomendado
    SLScenario best_scenario;
    for(int i = 0; i < m_last_result.total_scenarios; i++)
    {
        if(m_last_result.scenarios[i].sl_points == m_last_result.recommended_sl)
        {
            best_scenario = m_last_result.scenarios[i];
            break;
        }
    }

    FileWriteString(handle, "    <h2>🏆 Detalhes do Cenário Recomendado</h2>\n");
    FileWriteString(handle, "    <div class='summary'>\n");
    FileWriteString(handle, "        <div class='metric'>\n");
    FileWriteString(handle, "            <div class='metric-value'>" + IntegerToString(best_scenario.total_trades) + "</div>\n");
    FileWriteString(handle, "            <div class='metric-label'>Total de Trades</div>\n");
    FileWriteString(handle, "        </div>\n");
    FileWriteString(handle, "        <div class='metric'>\n");
    FileWriteString(handle, "            <div class='metric-value'>" + IntegerToString(best_scenario.winning_trades) + " / " + IntegerToString(best_scenario.losing_trades) + "</div>\n");
    FileWriteString(handle, "            <div class='metric-label'>Ganhos / Perdas</div>\n");
    FileWriteString(handle, "        </div>\n");
    FileWriteString(handle, "        <div class='metric'>\n");
    FileWriteString(handle, "            <div class='metric-value'>" + DoubleToString(best_scenario.avg_win_points, 1) + " pts</div>\n");
    FileWriteString(handle, "            <div class='metric-label'>Ganho Médio</div>\n");
    FileWriteString(handle, "        </div>\n");
    FileWriteString(handle, "        <div class='metric'>\n");
    FileWriteString(handle, "            <div class='metric-value'>" + DoubleToString(best_scenario.avg_loss_points, 1) + " pts</div>\n");
    FileWriteString(handle, "            <div class='metric-label'>Perda Média</div>\n");
    FileWriteString(handle, "        </div>\n");
    FileWriteString(handle, "        <div class='metric'>\n");
    FileWriteString(handle, "            <div class='metric-value'>" + DoubleToString(best_scenario.largest_win, 1) + " pts</div>\n");
    FileWriteString(handle, "            <div class='metric-label'>Maior Ganho</div>\n");
    FileWriteString(handle, "        </div>\n");
    FileWriteString(handle, "        <div class='metric'>\n");
    FileWriteString(handle, "            <div class='metric-value'>" + DoubleToString(best_scenario.largest_loss, 1) + " pts</div>\n");
    FileWriteString(handle, "            <div class='metric-label'>Maior Perda</div>\n");
    FileWriteString(handle, "        </div>\n");
    FileWriteString(handle, "    </div>\n");

    // Rodapé
    FileWriteString(handle, "    <div style='margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #7f8c8d;'>\n");
    FileWriteString(handle, "        <p>Relatório gerado automaticamente pelo Topomat EA v4.3.2</p>\n");
    FileWriteString(handle, "        <p>Data: " + TimeToString(TimeCurrent(), TIME_SECONDS) + "</p>\n");
    FileWriteString(handle, "    </div>\n");
    FileWriteString(handle, "</div>\n");
    FileWriteString(handle, "</body>\n");
    FileWriteString(handle, "</html>\n");

    FileClose(handle);

    LogAnalysisProgress("✅ Relatório HTML completo gerado: " + filename);
    return true;
}

//+------------------------------------------------------------------+
//| Formatar resumo de cenário                                      |
//+------------------------------------------------------------------+
string CStopLossAnalyzer::FormatScenarioSummary(const SLScenario &scenario)
{
    string summary = "SL " + IntegerToString(scenario.sl_points) + "pts: ";
    summary += "PF=" + DoubleToString(scenario.profit_factor, 2) + " | ";
    summary += "WR=" + DoubleToString(scenario.win_rate, 1) + "% | ";
    summary += "DD=" + DoubleToString(scenario.max_drawdown_points, 1) + "pts | ";
    summary += "Score=" + DoubleToString(scenario.balanced_score, 2);

    return summary;
}
