//+------------------------------------------------------------------+
//|                                             TradingStrategy.mqh |
//|                        Copyright 2024, Topomat Trading Systems   |
//|                                             https://topomat.com   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Topomat Trading Systems"
#property link      "https://topomat.com"

#include <Trade/Trade.mqh>
#include "LogManager.mqh"
#include "DataValidator.mqh"
#include "SRAnalyzer.mqh"
#include "RiskManager.mqh"

//--- Estratégia: Pontos Pivô com Reversão e Pyramiding
enum ENUM_STRATEGY_STATE
{
   STRATEGY_WAITING_ENTRY,    // Aguardando entrada
   STRATEGY_IN_POSITION,      // Em posição  
   STRATEGY_PYRAMIDING,       // Adicionando posições
   STRATEGY_TAKING_PROFIT     // Realizando lucro
};

//--- Direção da operação
enum ENUM_TRADE_DIRECTION
{
   DIRECTION_NONE,
   DIRECTION_LONG,
   DIRECTION_SHORT
};

//--- Posição ativa
struct SActivePosition
{
   ulong          ticket;
   double         entry_price;
   double         volume;
   double         pivot_price;
   datetime       entry_time;
   int            pyramid_level;
};

//--- Configuração da estratégia
struct SStrategyConfig
{
   double            proximity_weight;      // Peso para proximidade (0-1)
   double            frequency_weight;      // Peso para frequência de testes (0-1)
   double            time_decay_days;       // Dias para decaimento temporal
   int               max_pyramid_levels;    // Máximo de níveis de pyramiding
   double            pyramid_multiplier;    // Multiplicador do volume no pyramiding
   double            min_pivot_distance;    // Distância mínima entre pivôs (pontos)
   double            entry_tolerance;       // Tolerância para entrada (pontos)
   bool              enable_reverse_entry;  // Habilitar entrada reversa após lucro
   double            reverse_entry_delay;   // Delay para entrada reversa (segundos)
};

//+------------------------------------------------------------------+
//| Classe da Estratégia de Trading                                  |
//+------------------------------------------------------------------+
class CTradingStrategy
{
private:
   // Estado da estratégia
   ENUM_STRATEGY_STATE    m_state;
   ENUM_TRADE_DIRECTION   m_direction;
   
   // Configurações
   string                 m_symbol;
   int                    m_magic;
   double                 m_min_pivot_distance;
   double                 m_entry_tolerance;
   int                    m_max_pyramids;
   double                 m_pyramid_multiplier;
   
   // Posições ativas
   SActivePosition        m_positions[];
   int                    m_total_positions;
   double                 m_average_price;
   double                 m_total_volume;
   
   // Controle de operações
   double                 m_last_pivot_used;
   datetime               m_last_entry_time;
   datetime               m_last_profit_time;
   
   // Referências externas
   CLogManager           *m_logger;
   CSRAnalyzer           *m_sr_analyzer;
   CRiskManager          *m_risk_manager;
   CTrade                *m_trade;
   
   // Novas funções
   double m_pivots_used[20]; // Lista de pivôs já usados no trade atual
   int    m_pivots_used_count;
   
public:
   // Construtor
   CTradingStrategy();
   ~CTradingStrategy();
   
   // Inicialização
   bool Initialize(string symbol, int magic);
   void SetLogger(CLogManager *logger) { m_logger = logger; }
   void SetSRAnalyzer(CSRAnalyzer *analyzer) { m_sr_analyzer = analyzer; }
   void SetRiskManager(CRiskManager *manager) { m_risk_manager = manager; }
   void SetTradeObject(CTrade *trade_obj) { m_trade = trade_obj; }
   
   // Processamento principal
   void ProcessStrategy();
   
   // Estados
   ENUM_STRATEGY_STATE GetState() { return m_state; }
   string GetStateString();
   int GetTotalPositions() { return m_total_positions; }
   double GetTotalVolume() { return m_total_volume; }
   double GetAveragePrice() { return m_average_price; }
   
private:
   // Processamento por estado
   void ProcessWaitingEntry();
   void ProcessInPosition();
   void ProcessPyramiding();
   void ProcessTakingProfit();
   
   // Análise de pivôs
   bool FindBestEntryPivot(double current_price, SSRPoint &best_pivot);
   bool FindTargetPivot(double entry_price, ENUM_TRADE_DIRECTION direction, SSRPoint &target_pivot, double pivot_to_ignore);
   double CalculatePivotWeight(SSRPoint &pivot, double current_price);
   
   // Execução de trades
   bool ExecuteEntry(SSRPoint &pivot, double current_price);
   bool ExecutePyramiding(SSRPoint &pivot, double current_price);
   bool ExecuteExit(double current_price);
   bool ExecuteReverseEntry(double exit_price);
   
   // Gestão de posições
   void AddPosition(ulong ticket, double price, double volume, double pivot);
   void RemoveAllPositions();
   void UpdateAveragePrice();
   
   // Cálculos
   double CalculateVolume(double entry_price, double stop_price);
   double CalculatePyramidVolume(double base_volume, int level);
   ENUM_TRADE_DIRECTION DetermineDirection(double current_price, double pivot_price);
   
   // Validações
   bool IsPriceNearPivot(double price, double pivot, double tolerance);
   bool CanAddPyramid();
   bool ShouldTakeProfit(double current_price);
   
   // Utilitários
   void LogAction(string action, double price, double volume, string details = "");
   
   // Novas funções
   double RoundToTick(double price);
   
   // Função para verificar se pivô já foi usado
   bool IsPivotUsed(double pivot) {
      for(int i=0; i<m_pivots_used_count; i++)
         if(MathAbs(m_pivots_used[i] - pivot) < 0.0001) return true;
      return false;
   }
   // Adicionar pivô à lista de usados
   void AddPivotUsed(double pivot) {
      if(!IsPivotUsed(pivot) && m_pivots_used_count < 20) {
         m_pivots_used[m_pivots_used_count++] = pivot;
      }
   }
   // Resetar pivôs usados
   void ResetPivotsUsed() { m_pivots_used_count = 0; }
   
   // Nova função: checar se pivô está distante o suficiente de todos os pivôs já usados
   bool IsPivotFarFromUsed(double pivot, double min_distance) {
      for(int i=0; i<m_pivots_used_count; i++)
         if(MathAbs(m_pivots_used[i] - pivot) < min_distance) return false;
      return true;
   }
   bool FindNextUnusedPivot(double current_price, SSRPoint &next_pivot);
};

//+------------------------------------------------------------------+
//| Construtor                                                        |
//+------------------------------------------------------------------+
CTradingStrategy::CTradingStrategy()
{
   m_state = STRATEGY_WAITING_ENTRY;
   m_direction = DIRECTION_NONE;
   m_symbol = _Symbol;
   m_magic = 0;
   
   // Configurações padrão
   m_min_pivot_distance = 50.0;    // 50 pontos mínimo entre pivôs
   m_entry_tolerance = 20.0;       // 20 pontos de tolerância para entrada
   m_max_pyramids = 5;             // Máximo 5 níveis de pyramiding
   m_pyramid_multiplier = 1.5;     // Multiplicador de volume para pyramiding
   
   // Inicializar arrays
   ArrayResize(m_positions, 20);
   m_total_positions = 0;
   m_average_price = 0;
   m_total_volume = 0;
   
   // Controle
   m_last_pivot_used = 0;
   m_last_entry_time = 0;
   m_last_profit_time = 0;
   m_pivots_used_count = 0;
   
   // Ponteiros externos
   m_logger = NULL;
   m_sr_analyzer = NULL;
   m_risk_manager = NULL;
   m_trade = NULL;
}

//+------------------------------------------------------------------+
//| Destrutor                                                         |
//+------------------------------------------------------------------+
CTradingStrategy::~CTradingStrategy()
{
   // Limpeza se necessária
}

//+------------------------------------------------------------------+
//| Inicialização                                                     |
//+------------------------------------------------------------------+
bool CTradingStrategy::Initialize(string symbol, int magic)
{
   m_symbol = symbol;
   m_magic = magic;
   
   if(m_logger)
      m_logger.LogInfo("TradingStrategy inicializada para " + m_symbol);
   
   return true;
}

//+------------------------------------------------------------------+
//| Processamento principal da estratégia                             |
//+------------------------------------------------------------------+
void CTradingStrategy::ProcessStrategy()
{
   if(!m_sr_analyzer || !m_risk_manager || !m_trade)
      return;
   
   // Log detalhado do processamento da estratégia
   if(m_logger)
   {
      m_logger.LogExecution("STRATEGY", "PROCESS_START", 
                           StringFormat("Estado: %s | Posições: %d | Volume: %.2f", 
                           GetStateString(), GetTotalPositions(), GetTotalVolume()));
   }
   
   // Processar de acordo com estado atual
   switch(m_state)
   {
      case STRATEGY_WAITING_ENTRY:
         ProcessWaitingEntry();
         break;
         
      case STRATEGY_IN_POSITION:
         ProcessInPosition();
         break;
         
      case STRATEGY_PYRAMIDING:
         ProcessPyramiding();
         break;
         
      case STRATEGY_TAKING_PROFIT:
         ProcessTakingProfit();
         break;
   }
}

//+------------------------------------------------------------------+
//| Processar: Aguardando entrada                                     |
//+------------------------------------------------------------------+
void CTradingStrategy::ProcessWaitingEntry()
{
   double current_price = SymbolInfoDouble(m_symbol, SYMBOL_BID);
   
   // Encontrar melhor pivô para entrada
   SSRPoint best_pivot;
   
   if(FindBestEntryPivot(current_price, best_pivot))
   {
      // Verificar se preço está próximo do pivô
      if(IsPriceNearPivot(current_price, best_pivot.price, m_entry_tolerance))
      {
         // Executar entrada
         if(ExecuteEntry(best_pivot, current_price))
         {
            m_state = STRATEGY_IN_POSITION;
            m_last_pivot_used = best_pivot.price;
            m_last_entry_time = TimeCurrent();
            ResetPivotsUsed();
            AddPivotUsed(best_pivot.price);
            
            LogAction("ENTRADA", current_price, m_total_volume, 
                     "Pivô: " + DoubleToString(best_pivot.price, _Digits));
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Processar: Em posição                                             |
//+------------------------------------------------------------------+
void CTradingStrategy::ProcessInPosition()
{
   double current_price = SymbolInfoDouble(m_symbol, SYMBOL_BID);
   
   // Verificar se deve realizar lucro
   if(ShouldTakeProfit(current_price))
   {
      m_state = STRATEGY_TAKING_PROFIT;
      return;
   }
   
   // Verificar se deve fazer pyramiding (preço foi contra nós)
   SSRPoint pyramid_pivot;
   
   if(CanAddPyramid() && FindNextUnusedPivot(current_price, pyramid_pivot))
   {
      // Verificar se é um pivô diferente e próximo
      double distance = MathAbs(pyramid_pivot.price - m_last_pivot_used);
      
      if(distance > m_min_pivot_distance && 
         IsPriceNearPivot(current_price, pyramid_pivot.price, m_entry_tolerance))
      {
         if(ExecutePyramiding(pyramid_pivot, current_price))
         {
            m_state = STRATEGY_PYRAMIDING;
            m_last_pivot_used = pyramid_pivot.price;
            AddPivotUsed(pyramid_pivot.price);
            
            LogAction("PYRAMIDING", current_price, 0, 
                     "Nível: " + IntegerToString(m_total_positions));
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Processar: Pyramiding                                             |
//+------------------------------------------------------------------+
void CTradingStrategy::ProcessPyramiding()
{
   // Mesma lógica de em posição
   ProcessInPosition();
}

//+------------------------------------------------------------------+
//| Processar: Realizando lucro                                       |
//+------------------------------------------------------------------+
void CTradingStrategy::ProcessTakingProfit()
{
   double current_price = SymbolInfoDouble(m_symbol, SYMBOL_BID);
   
   if(ExecuteExit(current_price))
   {
      m_last_profit_time = TimeCurrent();
      
      LogAction("LUCRO REALIZADO", current_price, m_total_volume, 
               "Posições fechadas: " + IntegerToString(m_total_positions));
      
      // Reset para aguardar nova entrada
      RemoveAllPositions();
      m_state = STRATEGY_WAITING_ENTRY;
      m_direction = DIRECTION_NONE;
      ResetPivotsUsed();
      
      // Entrada reversa após pequeno delay
      Sleep(5000); // 5 segundos
      ExecuteReverseEntry(current_price);
   }
}

//+------------------------------------------------------------------+
//| Encontrar melhor pivô para entrada (AGORA: SEMPRE O MAIS PRÓXIMO)|
//+------------------------------------------------------------------+
bool CTradingStrategy::FindBestEntryPivot(double current_price, SSRPoint &best_pivot)
{
   if(!m_sr_analyzer)
      return false;

   SSRPoint sr_points[];
   m_sr_analyzer.GetSRPoints(sr_points);
   int total_points = ArraySize(sr_points);

   if(total_points == 0)
      return false;

   bool found = false;
   double min_distance = DBL_MAX;

   for(int i = 0; i < total_points; i++)
   {
      double distance = MathAbs(current_price - sr_points[i].price);
      if(distance < min_distance)
      {
         min_distance = distance;
         best_pivot = sr_points[i];
         found = true;
      }
   }

   return found;
}

//+------------------------------------------------------------------+
//| Calcular peso do pivô                                             |
//+------------------------------------------------------------------+
double CTradingStrategy::CalculatePivotWeight(SSRPoint &pivot, double current_price)
{
   // Peso baseado na proximidade (0-1)
   double distance = MathAbs(current_price - pivot.price);
   double max_distance = current_price * 0.02; // 2% do preço atual
   double proximity_weight = 1.0 - MathMin(1.0, distance / max_distance);
   
   // Peso baseado na frequência de testes (0-1)
   double frequency_weight = MathMin(1.0, (double)pivot.confirmations / 10.0);
   
   // Peso baseado no tempo (mais recente = maior peso)
   datetime current_time = TimeCurrent();
   int days_old = (int)((current_time - pivot.last_confirmation) / (24 * 3600));
   double time_weight = MathMax(0.1, 1.0 - (days_old / 30.0)); // Decaimento em 30 dias
   
   // Peso total combinado
   double total_weight = (proximity_weight * 0.4) + (frequency_weight * 0.4) + (time_weight * 0.2);
   
   return MathMax(0.0, MathMin(1.0, total_weight));
}

//+------------------------------------------------------------------+
//| Executar entrada                                                  |
//+------------------------------------------------------------------+
bool CTradingStrategy::ExecuteEntry(SSRPoint &pivot, double current_price)
{
   m_direction = DetermineDirection(current_price, pivot.price);
   double stop_price = (m_direction == DIRECTION_LONG) ? 
                      pivot.price - m_entry_tolerance : 
                      pivot.price + m_entry_tolerance;
   double volume = CalculateVolume(current_price, stop_price);

   // Encontrar pivô alvo para TP
   SSRPoint target_pivot;
   double tp = 0;
   if(FindTargetPivot(pivot.price, m_direction, target_pivot, pivot.price))
      tp = RoundToTick(target_pivot.price);
   else
      tp = 0; // fallback

   // Arredondar preços
   double entry_price = RoundToTick(current_price);
   double stop = RoundToTick(stop_price);

   if(m_logger)
   {
      m_logger.LogDebug("[ENTRY] Considerando pivô: " + DoubleToString(pivot.price, _Digits));
      m_logger.LogDebug("[ENTRY] Direção: " + (m_direction == DIRECTION_LONG ? "LONG" : m_direction == DIRECTION_SHORT ? "SHORT" : "NONE"));
      if(tp > 0)
         m_logger.LogDebug("[ENTRY] TP definido no pivô alvo: " + DoubleToString(tp, _Digits));
      else
         m_logger.LogDebug("[ENTRY] Nenhum pivô alvo encontrado para TP");
   }

   bool result = false;
   if(m_direction == DIRECTION_LONG)
   {
      result = m_trade.Buy(volume, m_symbol, entry_price, stop, tp, "Topomat-Entry");
   }
   else if(m_direction == DIRECTION_SHORT)
   {
      result = m_trade.Sell(volume, m_symbol, entry_price, stop, tp, "Topomat-Entry");
   }
   else
   {
      if(m_logger) m_logger.LogError("ExecuteEntry", "Direção inválida na entrada", -1);
      return false;
   }

   if(result)
   {
      ulong ticket = m_trade.ResultOrder();
      AddPosition(ticket, entry_price, volume, pivot.price);
      if(m_logger)
      {
         string direction_str = (m_direction == DIRECTION_LONG) ? "BUY" : "SELL";
         m_logger.LogTrade(direction_str, m_symbol, entry_price, volume, stop, tp, "Entrada em pivô");
      }
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Executar pyramiding                                               |
//+------------------------------------------------------------------+
bool CTradingStrategy::ExecutePyramiding(SSRPoint &pivot, double current_price)
{
   // Proteger: nunca inverter direção durante aumentos
   if(m_direction != DIRECTION_LONG && m_direction != DIRECTION_SHORT) {
      if(m_logger) m_logger.LogError("ExecutePyramiding", "Direção inválida ou nula durante aumento", -1);
      return false;
   }

   double base_volume = m_total_volume / m_total_positions;
   double pyramid_volume = CalculatePyramidVolume(base_volume, m_total_positions);

   // Encontrar pivô alvo para TP
   SSRPoint target_pivot;
   double tp = 0;
   if(FindTargetPivot(m_average_price, m_direction, target_pivot, pivot.price))
      tp = RoundToTick(target_pivot.price);
   else
      tp = 0; // fallback

   // Arredondar preços
   double entry_price = RoundToTick(current_price);

   if(m_logger)
   {
      m_logger.LogDebug("[PYRAMID] Considerando pivô: " + DoubleToString(pivot.price, _Digits));
      m_logger.LogDebug("[PYRAMID] Direção: " + (m_direction == DIRECTION_LONG ? "LONG" : m_direction == DIRECTION_SHORT ? "SHORT" : "NONE"));
      if(tp > 0)
         m_logger.LogDebug("[PYRAMID] TP definido no pivô alvo: " + DoubleToString(tp, _Digits));
      else
         m_logger.LogDebug("[PYRAMID] Nenhum pivô alvo encontrado para TP");
   }

   bool result = false;
   if(m_direction == DIRECTION_LONG)
   {
      result = m_trade.Buy(pyramid_volume, m_symbol, entry_price, 0, tp, "Topomat-Pyramid");
   }
   else if(m_direction == DIRECTION_SHORT)
   {
      result = m_trade.Sell(pyramid_volume, m_symbol, entry_price, 0, tp, "Topomat-Pyramid");
   }
   else
   {
      if(m_logger) m_logger.LogError("ExecutePyramiding", "Direção inválida durante aumento", -1);
      return false;
   }

   if(result)
   {
      ulong ticket = m_trade.ResultOrder();
      AddPosition(ticket, entry_price, pyramid_volume, pivot.price);
      if(m_logger)
         m_logger.LogTrade("PYRAMID", m_symbol, entry_price, pyramid_volume, 0, tp, "Aumento em pivô");
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Executar saída                                                   |
//+------------------------------------------------------------------+
bool CTradingStrategy::ExecuteExit(double current_price)
{
   bool all_closed = true;
   double exit_price = RoundToTick(current_price);
   for(int i = m_total_positions - 1; i >= 0; i--)
   {
      if(!m_trade.PositionClose(m_positions[i].ticket))
      {
         all_closed = false;
         if(m_logger)
            m_logger.LogError("ExecuteExit", "Falha ao fechar posição: " + IntegerToString(m_positions[i].ticket), GetLastError());
      }
      else
      {
         if(m_logger)
            m_logger.LogTrade("CLOSE", m_symbol, exit_price, m_positions[i].volume, 0, 0, "Lucro realizado");
      }
   }
   return all_closed;
}

//+------------------------------------------------------------------+
//| Executar entrada reversa                                         |
//+------------------------------------------------------------------+
bool CTradingStrategy::ExecuteReverseEntry(double exit_price)
{
   ENUM_TRADE_DIRECTION reverse_direction = (m_direction == DIRECTION_LONG) ? DIRECTION_SHORT : DIRECTION_LONG;
   double reverse_volume = CalculateVolume(exit_price, exit_price + (m_entry_tolerance * (reverse_direction == DIRECTION_LONG ? 1 : -1)));

   // Arredondar preço
   double entry_price = RoundToTick(exit_price);

   if(m_logger)
      m_logger.LogDebug("[REVERSE ENTRY] Direção reversa: " + (reverse_direction == DIRECTION_LONG ? "LONG" : "SHORT"));

   bool result = false;
   if(reverse_direction == DIRECTION_LONG)
   {
      result = m_trade.Buy(reverse_volume, m_symbol, entry_price, 0, 0, "Topomat-Reverse");
   }
   else
   {
      result = m_trade.Sell(reverse_volume, m_symbol, entry_price, 0, 0, "Topomat-Reverse");
   }

   if(result)
   {
      ulong ticket = m_trade.ResultOrder();
      AddPosition(ticket, entry_price, reverse_volume, entry_price);
      m_direction = reverse_direction;
      if(m_logger)
         m_logger.LogTrade(reverse_direction == DIRECTION_LONG ? "BUY" : "SELL", 
                          m_symbol, entry_price, reverse_volume, 0, 0, "Entrada reversa");
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Verificar se deve realizar lucro                                 |
//+------------------------------------------------------------------+
bool CTradingStrategy::ShouldTakeProfit(double current_price)
{
   if(m_total_positions == 0)
      return false;

   // Encontrar pivô alvo para lucro
   SSRPoint target_pivot;
   if(FindTargetPivot(m_average_price, m_direction, target_pivot, m_last_pivot_used))
   {
      return IsPriceNearPivot(current_price, target_pivot.price, m_entry_tolerance);
   }

   return false;
}

//+------------------------------------------------------------------+
//| Encontrar pivô alvo para lucro (NUNCA o mesmo da entrada)        |
//+------------------------------------------------------------------+
bool CTradingStrategy::FindTargetPivot(double entry_price, ENUM_TRADE_DIRECTION direction, SSRPoint &target_pivot, double pivot_to_ignore)
{
   if(!m_sr_analyzer)
      return false;

   SSRPoint sr_points[];
   m_sr_analyzer.GetSRPoints(sr_points);
   int total_points = ArraySize(sr_points);

   bool found = false;
   double best_distance = DBL_MAX;

   for(int i = 0; i < total_points; i++)
   {
      double pivot_price = sr_points[i].price;
      if(MathAbs(pivot_price - pivot_to_ignore) < 1e-4) // Ignorar pivô da entrada
         continue;

      // Verificar se pivô está na direção do lucro
      bool is_valid_target = false;
      if(direction == DIRECTION_LONG && pivot_price > entry_price)
         is_valid_target = true;
      else if(direction == DIRECTION_SHORT && pivot_price < entry_price)
         is_valid_target = true;

      if(is_valid_target)
      {
         double distance = MathAbs(pivot_price - entry_price);
         if(distance < best_distance && distance > m_min_pivot_distance)
         {
            best_distance = distance;
            target_pivot = sr_points[i];
            found = true;
         }
      }
   }

   return found;
}

//+------------------------------------------------------------------+
//| Determinar direção baseado na posição relativa ao pivô           |
//+------------------------------------------------------------------+
ENUM_TRADE_DIRECTION CTradingStrategy::DetermineDirection(double current_price, double pivot_price)
{
   // Se preço está acima do pivô, esperamos que volte (vender)
   // Se preço está abaixo do pivô, esperamos que volte (comprar)
   return (current_price > pivot_price) ? DIRECTION_SHORT : DIRECTION_LONG;
}

//+------------------------------------------------------------------+
//| Calcular volume da posição                                        |
//+------------------------------------------------------------------+
double CTradingStrategy::CalculateVolume(double entry_price, double stop_price)
{
   // VOLUME FIXO: Sempre usar o volume base configurado
   double base_volume = 1.0; // Volume base padrão
   
   // Obter volume base das configurações de risco
   if(m_risk_manager)
   {
      // Usar volume base configurado no parâmetro InpVolumeBase
      base_volume = m_risk_manager.GetBaseVolume();
   }
   
   // Log de controle
   if(m_logger)
   {
      m_logger.LogExecution("VOLUME_CALC", "FIXED_VOLUME", 
                           StringFormat("Volume base usado: %.0f", base_volume));
   }
   
   return base_volume;
}

//+------------------------------------------------------------------+
//| Calcular volume para pyramiding                                  |
//+------------------------------------------------------------------+
double CTradingStrategy::CalculatePyramidVolume(double base_volume, int level)
{
   // PYRAMIDING: Sempre usar o mesmo volume base
   double volume = base_volume; // Sem multiplicação - volume fixo
   
   // Log de controle
   if(m_logger)
   {
      m_logger.LogExecution("PYRAMID_CALC", "FIXED_VOLUME", 
                           StringFormat("Pyramid Level: %d | Volume fixo: %.0f", 
                           level, volume));
   }
   
   return volume;
}

//+------------------------------------------------------------------+
//| Adicionar posição                                                 |
//+------------------------------------------------------------------+
void CTradingStrategy::AddPosition(ulong ticket, double price, double volume, double pivot)
{
   if(m_total_positions >= ArraySize(m_positions))
      return;
   
   SActivePosition pos;
   pos.ticket = ticket;
   pos.entry_price = price;
   pos.volume = volume;
   pos.pivot_price = pivot;
   pos.entry_time = TimeCurrent();
   pos.pyramid_level = m_total_positions;
   
   m_positions[m_total_positions] = pos;
   m_total_positions++;
   
   UpdateAveragePrice();
}

//+------------------------------------------------------------------+
//| Remover todas as posições                                        |
//+------------------------------------------------------------------+
void CTradingStrategy::RemoveAllPositions()
{
   m_total_positions = 0;
   m_average_price = 0;
   m_total_volume = 0;
   ArrayResize(m_positions, 20);
}

//+------------------------------------------------------------------+
//| Atualizar preço médio                                             |
//+------------------------------------------------------------------+
void CTradingStrategy::UpdateAveragePrice()
{
   if(m_total_positions == 0)
   {
      m_average_price = 0;
      m_total_volume = 0;
      return;
   }
   
   double total_value = 0.0;
   m_total_volume = 0.0;
   
   for(int i = 0; i < m_total_positions; i++)
   {
      total_value += m_positions[i].entry_price * m_positions[i].volume;
      m_total_volume += m_positions[i].volume;
   }
   
   m_average_price = (m_total_volume > 0) ? total_value / m_total_volume : 0;
}

//+------------------------------------------------------------------+
//| Verificar se preço está próximo do pivô                          |
//+------------------------------------------------------------------+
bool CTradingStrategy::IsPriceNearPivot(double price, double pivot, double tolerance)
{
   return MathAbs(price - pivot) <= tolerance;
}

//+------------------------------------------------------------------+
//| Verificar se pode adicionar pyramid                               |
//+------------------------------------------------------------------+
bool CTradingStrategy::CanAddPyramid()
{
   return m_total_positions < m_max_pyramids;
}

//+------------------------------------------------------------------+
//| Obter string do estado                                            |
//+------------------------------------------------------------------+
string CTradingStrategy::GetStateString()
{
   switch(m_state)
   {
      case STRATEGY_WAITING_ENTRY: return "Aguardando Entrada";
      case STRATEGY_IN_POSITION: return "Em Posição";
      case STRATEGY_PYRAMIDING: return "Pyramiding";
      case STRATEGY_TAKING_PROFIT: return "Realizando Lucro";
      default: return "Desconhecido";
   }
}

//+------------------------------------------------------------------+
//| Log de ação da estratégia                                         |
//+------------------------------------------------------------------+
void CTradingStrategy::LogAction(string action, double price, double volume, string details = "")
{
   if(m_logger)
   {
      string message = StringFormat("ESTRATÉGIA: %s | Preço: %.5f | Vol: %.2f | %s", 
                                   action, price, volume, details);
      m_logger.LogInfo(message);
      
      // Log detalhado de execução
      m_logger.LogExecution("STRATEGY", action, 
                           StringFormat("Preço: %.5f | Volume: %.2f | Posições: %d | %s", 
                           price, volume, m_total_positions, details));
   }
}

//+------------------------------------------------------------------+
//| Arredondar preço para múltiplo de 5 (tick B3)                   |
//+------------------------------------------------------------------+
double CTradingStrategy::RoundToTick(double price)
{
    double tick = 5.0;
    return MathRound(price / tick) * tick;
}

// Nova função: encontrar pivô mais próximo ainda não usado
bool CTradingStrategy::FindNextUnusedPivot(double current_price, SSRPoint &next_pivot)
{
   if(!m_sr_analyzer)
      return false;
   SSRPoint sr_points[];
   m_sr_analyzer.GetSRPoints(sr_points);
   int total_points = ArraySize(sr_points);
   if(total_points == 0)
      return false;
   bool found = false;
   double min_distance = DBL_MAX;
   double min_pyramid_distance = 100.0; // Distância mínima para aumento de posição
   for(int i = 0; i < total_points; i++)
   {
      if(m_logger)
         m_logger.LogDebug("[PYRAMID] Considerando pivô: " + DoubleToString(sr_points[i].price, _Digits));
      if(IsPivotUsed(sr_points[i].price)) {
         if(m_logger) m_logger.LogDebug("[PYRAMID] Descartado (já usado): " + DoubleToString(sr_points[i].price, _Digits));
         continue;
      }
      if(!IsPivotFarFromUsed(sr_points[i].price, min_pyramid_distance)) {
         if(m_logger) m_logger.LogDebug("[PYRAMID] Descartado (distância < 100): " + DoubleToString(sr_points[i].price, _Digits));
         continue;
      }
      double distance = MathAbs(current_price - sr_points[i].price);
      if(distance < min_distance)
      {
         min_distance = distance;
         next_pivot = sr_points[i];
         found = true;
      }
   }
   if(m_logger && found)
      m_logger.LogDebug("[PYRAMID] Pivô selecionado para aumento: " + DoubleToString(next_pivot.price, _Digits));
   return found;
}

 