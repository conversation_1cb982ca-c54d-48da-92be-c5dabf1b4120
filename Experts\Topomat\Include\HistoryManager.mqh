//+------------------------------------------------------------------+
//|                                               HistoryManager.mqh |
//|                        Copyright 2024, Topomat Trading Systems   |
//|                                             https://topomat.com   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Topomat Trading Systems"
#property link      "https://topomat.com"

//+------------------------------------------------------------------+
//| SISTEMA DE EXTRAÇÃO DE HISTÓRICO COMPLETO VIA MAGIC NUMBER       |
//+------------------------------------------------------------------+

// Estrutura para dados de trade histórico
struct HistoryTradeData
{
    datetime abertura;
    datetime fechamento;
    double volume;
    double preco_abertura;
    double preco_fechamento;
    double pontos;
    double lucro_bruto;
    double lucro_liquido; // Considerando comissões/swaps
    bool foi_compra;
    bool foi_lucro;
    int duracao_segundos;
    ulong ticket_abertura;
    ulong ticket_fechamento;
    string comentario;
};

// Estrutura para estatísticas de período
struct PeriodStats
{
    datetime periodo_inicio;
    datetime periodo_fim;
    int total_trades;
    int trades_win;
    int trades_loss;
    double lucro_bruto_total;
    double lucro_liquido_total;
    double maior_lucro;
    double maior_prejuizo;
    double maior_sequencia_positiva;
    double maior_sequencia_negativa;
    int atual_sequencia;
    double drawdown_maximo;
    double pontos_totais;
    int duracao_media_segundos;
    double win_rate;
    double profit_factor;
    double recovery_factor;
};

//+------------------------------------------------------------------+
//| Classe para gerenciamento de histórico completo                  |
//+------------------------------------------------------------------+
class CHistoryManager
{
private:
    int m_magic_number;
    string m_symbol;
    bool m_initialized;
    
    // Arrays para dados históricos
    HistoryTradeData m_all_trades[];
    PeriodStats m_stats_diaria;
    PeriodStats m_stats_semanal;
    PeriodStats m_stats_mensal;
    PeriodStats m_stats_total;
    
    // Cache para performance
    datetime m_last_update;
    bool m_cache_valido;
    
public:
    // Construtor/Destrutor
    CHistoryManager(void);
    ~CHistoryManager(void);
    
    // Inicialização
    bool Initialize(string symbol, int magic_number);
    
    // Métodos principais de extração
    bool CarregarHistoricoCompleto(datetime data_inicio = 0);
    bool AtualizarHistorico(void);
    
    // Extração de estatísticas por período
    PeriodStats GetEstatisticasDiarias(datetime data = 0);
    PeriodStats GetEstatisticasSemanais(datetime data = 0);
    PeriodStats GetEstatisticasMensais(datetime data = 0);
    PeriodStats GetEstatisticasTotais(void);
    
    // Relatórios detalhados
    string GerarRelatorioDetalhado(ENUM_TIMEFRAMES periodo = PERIOD_D1);
    string GerarRelatorioComparativo(void);
    string GerarRelatorioPerformance(void);
    
    // Análises avançadas
    double CalcularDrawdownMaximo(datetime inicio, datetime fim);
    double CalcularProfitFactor(datetime inicio, datetime fim);
    int CalcularMediaDuracao(datetime inicio, datetime fim);
    double CalcularWinRate(datetime inicio, datetime fim);
    
    // Análise de padrões
    string AnalisarPadroesHorarios(void);
    string AnalisarPadroesDiasSemana(void);
    string AnalisarTendencias(void);
    
    // Métodos auxiliares públicos
    datetime GetInicioSemana(datetime data);
    datetime GetInicioMes(datetime data);
    
    // Getters
    int GetTotalTrades(void) { return ArraySize(m_all_trades); }
    HistoryTradeData GetTrade(int index);
    bool IsInitialized(void) { return m_initialized; }
    
private:
    // Métodos auxiliares
    bool ExtrairDealsDoHistorico(datetime inicio, datetime fim);
    bool ProcessarDeal(ulong deal_ticket);
    void CalcularEstatisticas(PeriodStats &stats, datetime inicio, datetime fim);
    void ResetarEstatisticas(PeriodStats &stats);
    datetime GetFimPeriodo(datetime inicio, ENUM_TIMEFRAMES periodo);
    string FormatarDuracao(int segundos);
    string FormatarMoeda(double valor);
    void OrdenarTradesPorData(void);
    
    // Validações
    bool ValidarDeal(ulong deal_ticket);
    bool ValidarPeriodo(datetime inicio, datetime fim);
};

//+------------------------------------------------------------------+
//| Construtor                                                        |
//+------------------------------------------------------------------+
CHistoryManager::CHistoryManager(void)
{
    m_magic_number = 0;
    m_symbol = "";
    m_initialized = false;
    m_last_update = 0;
    m_cache_valido = false;
    
    ArrayResize(m_all_trades, 0);
    ResetarEstatisticas(m_stats_diaria);
    ResetarEstatisticas(m_stats_semanal);
    ResetarEstatisticas(m_stats_mensal);
    ResetarEstatisticas(m_stats_total);
}

//+------------------------------------------------------------------+
//| Destrutor                                                         |
//+------------------------------------------------------------------+
CHistoryManager::~CHistoryManager(void)
{
    ArrayFree(m_all_trades);
}

//+------------------------------------------------------------------+
//| Inicialização do gerenciador de histórico                        |
//+------------------------------------------------------------------+
bool CHistoryManager::Initialize(string symbol, int magic_number)
{
    if(symbol == "" || magic_number <= 0)
    {
        Print("❌ HistoryManager: Parâmetros inválidos");
        return false;
    }
    
    m_symbol = symbol;
    m_magic_number = magic_number;
    m_initialized = true;
    m_cache_valido = false;
    
    Print("✅ HistoryManager inicializado - Símbolo: ", symbol, " | Magic: ", magic_number);
    return true;
}

//+------------------------------------------------------------------+
//| Carrega histórico completo de trades                             |
//+------------------------------------------------------------------+
bool CHistoryManager::CarregarHistoricoCompleto(datetime data_inicio = 0)
{
    if(!m_initialized)
    {
        Print("❌ HistoryManager não inicializado");
        return false;
    }
    
    // Se data_inicio não especificada, carregar últimos 365 dias
    if(data_inicio == 0)
        data_inicio = TimeCurrent() - (365 * 24 * 3600);
    
    datetime data_fim = TimeCurrent() + 3600; // +1 hora para garantir
    
    Print("🔍 Carregando histórico completo...");
    Print("📅 Período: ", TimeToString(data_inicio, TIME_DATE), " até ", TimeToString(data_fim, TIME_DATE));
    Print("🎯 Magic Number: ", m_magic_number, " | Símbolo: ", m_symbol);
    
    // Solicitar histórico ao servidor
    if(!HistorySelect(data_inicio, data_fim))
    {
        Print("❌ Falha ao solicitar histórico do servidor");
        return false;
    }
    
    // Extrair deals
    bool resultado = ExtrairDealsDoHistorico(data_inicio, data_fim);
    
    if(resultado)
    {
        OrdenarTradesPorData();
        CalcularEstatisticas(m_stats_total, data_inicio, data_fim);
        m_cache_valido = true;
        m_last_update = TimeCurrent();
        
        Print("✅ Histórico carregado com sucesso!");
        Print("📊 Total de trades encontrados: ", ArraySize(m_all_trades));
    }
    
    return resultado;
}

//+------------------------------------------------------------------+
//| Extrai deals do histórico dentro do período                      |
//+------------------------------------------------------------------+
bool CHistoryManager::ExtrairDealsDoHistorico(datetime inicio, datetime fim)
{
    ArrayResize(m_all_trades, 0);
    
    // Estruturas para organizar trades (entrada + saída)
    struct TradeTemp
    {
        ulong ticket_entrada;
        ulong ticket_saida;
        datetime time_entrada;
        datetime time_saida;
        double price_entrada;
        double price_saida;
        double volume;
        double profit;
        double swap;
        double commission;
        bool is_buy;
        string comment;
        bool completo;
    };
    
    TradeTemp trades_temp[];
    ArrayResize(trades_temp, 0);
    
    int total_deals = HistoryDealsTotal();
    
    Print("🔍 Analisando ", total_deals, " deals no histórico...");
    
    for(int i = 0; i < total_deals; i++)
    {
        ulong deal_ticket = HistoryDealGetTicket(i);
        if(deal_ticket <= 0) continue;
        
        // Verificar se o deal pertence ao nosso EA
        if(HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) != m_magic_number) continue;
        if(HistoryDealGetString(deal_ticket, DEAL_SYMBOL) != m_symbol) continue;
        
        // Obter tipo do deal
        ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
        
        // Processar apenas operações de compra/venda
        if(deal_type != DEAL_TYPE_BUY && deal_type != DEAL_TYPE_SELL) continue;
        
        // Obter entrada do deal
        ENUM_DEAL_ENTRY deal_entry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(deal_ticket, DEAL_ENTRY);
        
        datetime deal_time = (datetime)HistoryDealGetInteger(deal_ticket, DEAL_TIME);
        double deal_price = HistoryDealGetDouble(deal_ticket, DEAL_PRICE);
        double deal_volume = HistoryDealGetDouble(deal_ticket, DEAL_VOLUME);
        double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
        double deal_swap = HistoryDealGetDouble(deal_ticket, DEAL_SWAP);
        double deal_commission = HistoryDealGetDouble(deal_ticket, DEAL_COMMISSION);
        string deal_comment = HistoryDealGetString(deal_ticket, DEAL_COMMENT);
        
        // Procurar trade correspondente ou criar novo
        bool encontrado = false;
        for(int j = 0; j < ArraySize(trades_temp); j++)
        {
            if(!trades_temp[j].completo && 
               MathAbs(trades_temp[j].volume - deal_volume) < 0.01 &&
               MathAbs((int)trades_temp[j].time_entrada - (int)deal_time) < 86400) // Máximo 24h de diferença
            {
                if(deal_entry == DEAL_ENTRY_IN && trades_temp[j].ticket_entrada == 0)
                {
                    // Entrada do trade
                    trades_temp[j].ticket_entrada = deal_ticket;
                    trades_temp[j].time_entrada = deal_time;
                    trades_temp[j].price_entrada = deal_price;
                    trades_temp[j].is_buy = (deal_type == DEAL_TYPE_BUY);
                    trades_temp[j].comment = deal_comment;
                    encontrado = true;
                    break;
                }
                else if(deal_entry == DEAL_ENTRY_OUT && trades_temp[j].ticket_saida == 0)
                {
                    // Saída do trade
                    trades_temp[j].ticket_saida = deal_ticket;
                    trades_temp[j].time_saida = deal_time;
                    trades_temp[j].price_saida = deal_price;
                    trades_temp[j].profit = deal_profit;
                    trades_temp[j].swap = deal_swap;
                    trades_temp[j].commission = deal_commission;
                    trades_temp[j].completo = true;
                    encontrado = true;
                    break;
                }
            }
        }
        
        // Se não encontrou, criar novo trade temporário
        if(!encontrado)
        {
            int novo_index = ArraySize(trades_temp);
            ArrayResize(trades_temp, novo_index + 1);
            
            if(deal_entry == DEAL_ENTRY_IN)
            {
                trades_temp[novo_index].ticket_entrada = deal_ticket;
                trades_temp[novo_index].time_entrada = deal_time;
                trades_temp[novo_index].price_entrada = deal_price;
                trades_temp[novo_index].volume = deal_volume;
                trades_temp[novo_index].is_buy = (deal_type == DEAL_TYPE_BUY);
                trades_temp[novo_index].comment = deal_comment;
                trades_temp[novo_index].completo = false;
            }
            else if(deal_entry == DEAL_ENTRY_OUT)
            {
                trades_temp[novo_index].ticket_saida = deal_ticket;
                trades_temp[novo_index].time_saida = deal_time;
                trades_temp[novo_index].price_saida = deal_price;
                trades_temp[novo_index].volume = deal_volume;
                trades_temp[novo_index].profit = deal_profit;
                trades_temp[novo_index].swap = deal_swap;
                trades_temp[novo_index].commission = deal_commission;
                trades_temp[novo_index].completo = false;
            }
        }
    }
    
    // Converter trades temporários em dados finais
    int trades_validos = 0;
    for(int i = 0; i < ArraySize(trades_temp); i++)
    {
        if(trades_temp[i].completo && trades_temp[i].ticket_entrada > 0 && trades_temp[i].ticket_saida > 0)
        {
            trades_validos++;
        }
    }
    
    ArrayResize(m_all_trades, trades_validos);
    int index_final = 0;
    
    for(int i = 0; i < ArraySize(trades_temp); i++)
    {
        if(trades_temp[i].completo && trades_temp[i].ticket_entrada > 0 && trades_temp[i].ticket_saida > 0)
        {
            m_all_trades[index_final].abertura = trades_temp[i].time_entrada;
            m_all_trades[index_final].fechamento = trades_temp[i].time_saida;
            m_all_trades[index_final].volume = trades_temp[i].volume;
            m_all_trades[index_final].preco_abertura = trades_temp[i].price_entrada;
            m_all_trades[index_final].preco_fechamento = trades_temp[i].price_saida;
            
            // Calcular pontos
            double pontos = 0;
            if(trades_temp[i].is_buy)
                pontos = trades_temp[i].price_saida - trades_temp[i].price_entrada;
            else
                pontos = trades_temp[i].price_entrada - trades_temp[i].price_saida;
            
            m_all_trades[index_final].pontos = pontos;
            m_all_trades[index_final].lucro_bruto = trades_temp[i].profit;
            m_all_trades[index_final].lucro_liquido = trades_temp[i].profit + trades_temp[i].swap + trades_temp[i].commission;
            m_all_trades[index_final].foi_compra = trades_temp[i].is_buy;
            m_all_trades[index_final].foi_lucro = (trades_temp[i].profit > 0);
            m_all_trades[index_final].duracao_segundos = (int)(trades_temp[i].time_saida - trades_temp[i].time_entrada);
            m_all_trades[index_final].ticket_abertura = trades_temp[i].ticket_entrada;
            m_all_trades[index_final].ticket_fechamento = trades_temp[i].ticket_saida;
            m_all_trades[index_final].comentario = trades_temp[i].comment;
            
            index_final++;
        }
    }
    
    Print("✅ Processamento concluído: ", trades_validos, " trades completos extraídos");
    return true;
}

//+------------------------------------------------------------------+
//| Calcula estatísticas para um período específico                  |
//+------------------------------------------------------------------+
void CHistoryManager::CalcularEstatisticas(PeriodStats &stats, datetime inicio, datetime fim)
{
    ResetarEstatisticas(stats);
    stats.periodo_inicio = inicio;
    stats.periodo_fim = fim;
    
    double sequencia_atual = 0;
    double maior_seq_pos = 0;
    double maior_seq_neg = 0;
    double lucros_totais = 0;
    double perdas_totais = 0;
    double peak = 0;
    double atual = 0;
    double max_dd = 0;
    
    for(int i = 0; i < ArraySize(m_all_trades); i++)
    {
        if(m_all_trades[i].abertura >= inicio && m_all_trades[i].abertura <= fim)
        {
            stats.total_trades++;
            stats.lucro_bruto_total += m_all_trades[i].lucro_bruto;
            stats.lucro_liquido_total += m_all_trades[i].lucro_liquido;
            stats.pontos_totais += m_all_trades[i].pontos;
            stats.duracao_media_segundos += m_all_trades[i].duracao_segundos;
            
            if(m_all_trades[i].foi_lucro)
            {
                stats.trades_win++;
                lucros_totais += m_all_trades[i].lucro_liquido;
                
                if(m_all_trades[i].lucro_liquido > stats.maior_lucro)
                    stats.maior_lucro = m_all_trades[i].lucro_liquido;
                
                if(sequencia_atual >= 0)
                    sequencia_atual += m_all_trades[i].lucro_liquido;
                else
                    sequencia_atual = m_all_trades[i].lucro_liquido;
                
                if(sequencia_atual > maior_seq_pos)
                    maior_seq_pos = sequencia_atual;
            }
            else
            {
                stats.trades_loss++;
                perdas_totais += MathAbs(m_all_trades[i].lucro_liquido);
                
                if(m_all_trades[i].lucro_liquido < stats.maior_prejuizo)
                    stats.maior_prejuizo = m_all_trades[i].lucro_liquido;
                
                if(sequencia_atual <= 0)
                    sequencia_atual += m_all_trades[i].lucro_liquido;
                else
                    sequencia_atual = m_all_trades[i].lucro_liquido;
                
                if(sequencia_atual < maior_seq_neg)
                    maior_seq_neg = sequencia_atual;
            }
            
            // Calcular drawdown
            atual += m_all_trades[i].lucro_liquido;
            if(atual > peak) peak = atual;
            double dd_atual = peak - atual;
            if(dd_atual > max_dd) max_dd = dd_atual;
        }
    }
    
    stats.maior_sequencia_positiva = maior_seq_pos;
    stats.maior_sequencia_negativa = maior_seq_neg;
    stats.drawdown_maximo = max_dd;
    
    if(stats.total_trades > 0)
    {
        stats.win_rate = (double)stats.trades_win / stats.total_trades * 100.0;
        stats.duracao_media_segundos = stats.duracao_media_segundos / stats.total_trades;
        stats.profit_factor = (perdas_totais > 0) ? lucros_totais / perdas_totais : 0;
        stats.recovery_factor = (max_dd > 0) ? stats.lucro_liquido_total / max_dd : 0;
    }
}

//+------------------------------------------------------------------+
//| Gera relatório detalhado por período                             |
//+------------------------------------------------------------------+
string CHistoryManager::GerarRelatorioDetalhado(ENUM_TIMEFRAMES periodo = PERIOD_D1)
{
    if(!m_initialized || !m_cache_valido)
    {
        CarregarHistoricoCompleto();
    }
    
    datetime agora = TimeCurrent();
    datetime inicio;
    string titulo_periodo;
    
    switch(periodo)
    {
        case PERIOD_D1:
            inicio = StringToTime(TimeToString(agora, TIME_DATE) + " 00:00:00");
            titulo_periodo = "DIÁRIO";
            CalcularEstatisticas(m_stats_diaria, inicio, agora);
            break;
            
        case PERIOD_W1:
            inicio = GetInicioSemana(agora);
            titulo_periodo = "SEMANAL";
            CalcularEstatisticas(m_stats_semanal, inicio, agora);
            break;
            
        case PERIOD_MN1:
            inicio = GetInicioMes(agora);
            titulo_periodo = "MENSAL";
            CalcularEstatisticas(m_stats_mensal, inicio, agora);
            break;
            
        default:
            inicio = TimeCurrent() - (365 * 24 * 3600);
            titulo_periodo = "TOTAL";
            CalcularEstatisticas(m_stats_total, inicio, agora);
            break;
    }
    
    PeriodStats stats = (periodo == PERIOD_D1) ? m_stats_diaria : 
                       (periodo == PERIOD_W1) ? m_stats_semanal :
                       (periodo == PERIOD_MN1) ? m_stats_mensal : m_stats_total;
    
    string relatorio = "";
    relatorio += "╔═══ RELATÓRIO " + titulo_periodo + " - " + m_symbol + " ═══\n";
    relatorio += "║ Magic Number: " + IntegerToString(m_magic_number) + "\n";
    relatorio += "║ Período: " + TimeToString(stats.periodo_inicio, TIME_DATE) + " até " + TimeToString(stats.periodo_fim, TIME_DATE) + "\n";
    relatorio += "╠═══ PERFORMANCE GERAL ═══\n";
    relatorio += "║ 📊 Total de Trades: " + IntegerToString(stats.total_trades) + "\n";
    relatorio += "║ 🟢 Trades Ganhos: " + IntegerToString(stats.trades_win) + " (" + DoubleToString(stats.win_rate, 1) + "%)\n";
    relatorio += "║ 🔴 Trades Perdidos: " + IntegerToString(stats.trades_loss) + "\n";
    relatorio += "║ 💰 Lucro Líquido: " + FormatarMoeda(stats.lucro_liquido_total) + "\n";
    relatorio += "║ 📈 Pontos Totais: " + DoubleToString(stats.pontos_totais, 1) + " pts\n";
    relatorio += "╠═══ ANÁLISE DE RISCO ═══\n";
    relatorio += "║ 📊 Profit Factor: " + DoubleToString(stats.profit_factor, 2) + "\n";
    relatorio += "║ 📉 Drawdown Máximo: " + FormatarMoeda(stats.drawdown_maximo) + "\n";
    relatorio += "║ 🔄 Recovery Factor: " + DoubleToString(stats.recovery_factor, 2) + "\n";
    relatorio += "╠═══ EXTREMOS ═══\n";
    relatorio += "║ 🚀 Maior Lucro: " + FormatarMoeda(stats.maior_lucro) + "\n";
    relatorio += "║ 💥 Maior Prejuízo: " + FormatarMoeda(stats.maior_prejuizo) + "\n";
    relatorio += "║ 🔥 Maior Seq. Positiva: " + FormatarMoeda(stats.maior_sequencia_positiva) + "\n";
    relatorio += "║ 🧊 Maior Seq. Negativa: " + FormatarMoeda(stats.maior_sequencia_negativa) + "\n";
    relatorio += "╠═══ TEMPO MÉDIO ═══\n";
    relatorio += "║ ⏱️ Duração Média: " + FormatarDuracao(stats.duracao_media_segundos) + "\n";
    relatorio += "╚═══════════════════════════════════════\n";
    
    return relatorio;
}

//+------------------------------------------------------------------+
//| Métodos auxiliares                                                |
//+------------------------------------------------------------------+
void CHistoryManager::ResetarEstatisticas(PeriodStats &stats)
{
    stats.periodo_inicio = 0;
    stats.periodo_fim = 0;
    stats.total_trades = 0;
    stats.trades_win = 0;
    stats.trades_loss = 0;
    stats.lucro_bruto_total = 0;
    stats.lucro_liquido_total = 0;
    stats.maior_lucro = 0;
    stats.maior_prejuizo = 0;
    stats.maior_sequencia_positiva = 0;
    stats.maior_sequencia_negativa = 0;
    stats.atual_sequencia = 0;
    stats.drawdown_maximo = 0;
    stats.pontos_totais = 0;
    stats.duracao_media_segundos = 0;
    stats.win_rate = 0;
    stats.profit_factor = 0;
    stats.recovery_factor = 0;
}

datetime CHistoryManager::GetInicioSemana(datetime data)
{
    MqlDateTime dt;
    TimeToStruct(data, dt);
    int dias_desde_segunda = (dt.day_of_week == 0) ? 6 : dt.day_of_week - 1;
    return StringToTime(TimeToString(data - dias_desde_segunda * 24 * 3600, TIME_DATE) + " 00:00:00");
}

datetime CHistoryManager::GetInicioMes(datetime data)
{
    MqlDateTime dt;
    TimeToStruct(data, dt);
    return StringToTime(IntegerToString(dt.year) + "." + 
                       (dt.mon < 10 ? "0" : "") + IntegerToString(dt.mon) + ".01 00:00:00");
}

string CHistoryManager::FormatarDuracao(int segundos)
{
    int horas = segundos / 3600;
    int minutos = (segundos % 3600) / 60;
    int secs = segundos % 60;
    
    if(horas > 0)
        return IntegerToString(horas) + "h " + IntegerToString(minutos) + "m " + IntegerToString(secs) + "s";
    else if(minutos > 0)
        return IntegerToString(minutos) + "m " + IntegerToString(secs) + "s";
    else
        return IntegerToString(secs) + "s";
}

string CHistoryManager::FormatarMoeda(double valor)
{
    return "R$ " + DoubleToString(valor, 2);
}

void CHistoryManager::OrdenarTradesPorData(void)
{
    int n = ArraySize(m_all_trades);
    for(int i = 0; i < n - 1; i++)
    {
        for(int j = 0; j < n - i - 1; j++)
        {
            if(m_all_trades[j].abertura > m_all_trades[j + 1].abertura)
            {
                HistoryTradeData temp = m_all_trades[j];
                m_all_trades[j] = m_all_trades[j + 1];
                m_all_trades[j + 1] = temp;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Getters das estatísticas                                          |
//+------------------------------------------------------------------+
PeriodStats CHistoryManager::GetEstatisticasDiarias(datetime data = 0)
{
    if(data == 0) data = TimeCurrent();
    datetime inicio = StringToTime(TimeToString(data, TIME_DATE) + " 00:00:00");
    CalcularEstatisticas(m_stats_diaria, inicio, data);
    return m_stats_diaria;
}

PeriodStats CHistoryManager::GetEstatisticasSemanais(datetime data = 0)
{
    if(data == 0) data = TimeCurrent();
    datetime inicio = GetInicioSemana(data);
    CalcularEstatisticas(m_stats_semanal, inicio, data);
    return m_stats_semanal;
}

PeriodStats CHistoryManager::GetEstatisticasMensais(datetime data = 0)
{
    if(data == 0) data = TimeCurrent();
    datetime inicio = GetInicioMes(data);
    CalcularEstatisticas(m_stats_mensal, inicio, data);
    return m_stats_mensal;
}

PeriodStats CHistoryManager::GetEstatisticasTotais(void)
{
    datetime inicio = TimeCurrent() - (365 * 24 * 3600);
    CalcularEstatisticas(m_stats_total, inicio, TimeCurrent());
    return m_stats_total;
}

HistoryTradeData CHistoryManager::GetTrade(int index)
{
    HistoryTradeData empty_trade = {};
    if(index < 0 || index >= ArraySize(m_all_trades))
        return empty_trade;
    return m_all_trades[index];
}

//+------------------------------------------------------------------+
//| Implementação do método AtualizarHistorico                      |
//+------------------------------------------------------------------+
bool CHistoryManager::AtualizarHistorico(void)
{
    if(!m_initialized)
        return false;
        
    // Atualizar apenas trades do último dia para performance
    datetime ultimo_dia = TimeCurrent() - (24 * 3600);
    return CarregarHistoricoCompleto(ultimo_dia);
}

//+------------------------------------------------------------------+
//| Métodos de análise de padrões                                   |
//+------------------------------------------------------------------+
string CHistoryManager::GerarRelatorioComparativo(void)
{
    if(!m_initialized || !m_cache_valido)
        return "❌ HistoryManager não inicializado ou sem dados";
        
    string relatorio = "╔═══ RELATÓRIO COMPARATIVO DE PERÍODOS ═══\n";
    
    PeriodStats stats_hoje = GetEstatisticasDiarias();
    PeriodStats stats_semana = GetEstatisticasSemanais();
    PeriodStats stats_mes = GetEstatisticasMensais();
    PeriodStats stats_total = GetEstatisticasTotais();
    
    relatorio += "║ PERÍODO     │ TRADES │ LUCRO      │ WIN% │ PF   \n";
    relatorio += "╠════════════════════════════════════════════════\n";
    relatorio += "║ HOJE        │ " + IntegerToString(stats_hoje.total_trades) + "      │ R$ " + DoubleToString(stats_hoje.lucro_liquido_total, 2) + " │ " + DoubleToString(stats_hoje.win_rate, 1) + "% │ " + DoubleToString(stats_hoje.profit_factor, 2) + "\n";
    relatorio += "║ ESTA SEMANA │ " + IntegerToString(stats_semana.total_trades) + "      │ R$ " + DoubleToString(stats_semana.lucro_liquido_total, 2) + " │ " + DoubleToString(stats_semana.win_rate, 1) + "% │ " + DoubleToString(stats_semana.profit_factor, 2) + "\n";
    relatorio += "║ ESTE MÊS    │ " + IntegerToString(stats_mes.total_trades) + "      │ R$ " + DoubleToString(stats_mes.lucro_liquido_total, 2) + " │ " + DoubleToString(stats_mes.win_rate, 1) + "% │ " + DoubleToString(stats_mes.profit_factor, 2) + "\n";
    relatorio += "║ TOTAL       │ " + IntegerToString(stats_total.total_trades) + "      │ R$ " + DoubleToString(stats_total.lucro_liquido_total, 2) + " │ " + DoubleToString(stats_total.win_rate, 1) + "% │ " + DoubleToString(stats_total.profit_factor, 2) + "\n";
    relatorio += "╚════════════════════════════════════════════════\n";
    
    return relatorio;
}

string CHistoryManager::GerarRelatorioPerformance(void)
{
    if(!m_initialized || !m_cache_valido)
        return "❌ HistoryManager não inicializado ou sem dados";
        
    string relatorio = "╔═══ ANÁLISE DE PERFORMANCE AVANÇADA ═══\n";
    
    PeriodStats stats = GetEstatisticasTotais();
    
    // Calcular métricas avançadas
    double trades_por_dia = 0;
    if(ArraySize(m_all_trades) > 0)
    {
        int dias_operacao = (int)((TimeCurrent() - m_all_trades[0].abertura) / 86400) + 1;
        trades_por_dia = (double)stats.total_trades / dias_operacao;
    }
    
    double lucro_medio_trade = 0;
    if(stats.total_trades > 0)
        lucro_medio_trade = stats.lucro_liquido_total / stats.total_trades;
    
    double expectativa = (stats.win_rate / 100.0) * (stats.maior_lucro) + 
                        ((100.0 - stats.win_rate) / 100.0) * stats.maior_prejuizo;
    
    relatorio += "║ 📊 Trades por dia: " + DoubleToString(trades_por_dia, 1) + "\n";
    relatorio += "║ 💰 Lucro médio/trade: R$ " + DoubleToString(lucro_medio_trade, 2) + "\n";
    relatorio += "║ 🎯 Expectativa: R$ " + DoubleToString(expectativa, 2) + "\n";
    relatorio += "║ 📈 Sharpe Ratio: " + DoubleToString(stats.recovery_factor, 2) + "\n";
    relatorio += "║ ⏱️ Duração média: " + FormatarDuracao(stats.duracao_media_segundos) + "\n";
    relatorio += "╚═══════════════════════════════════════\n";
    
    return relatorio;
}

string CHistoryManager::AnalisarPadroesHorarios(void)
{
    if(!m_initialized || ArraySize(m_all_trades) == 0)
        return "❌ Sem dados para análise de padrões horários";
        
    string relatorio = "╔═══ ANÁLISE DE PADRÕES HORÁRIOS ═══\n";
    
    // Arrays para contar por hora
    int trades_por_hora[24];
    double lucro_por_hora[24];
    int wins_por_hora[24];
    
    ArrayInitialize(trades_por_hora, 0);
    ArrayInitialize(lucro_por_hora, 0.0);
    ArrayInitialize(wins_por_hora, 0);
    
    // Analisar todos os trades
    for(int i = 0; i < ArraySize(m_all_trades); i++)
    {
        MqlDateTime dt;
        TimeToStruct(m_all_trades[i].abertura, dt);
        int hora = dt.hour;
        
        trades_por_hora[hora]++;
        lucro_por_hora[hora] += m_all_trades[i].lucro_liquido;
        if(m_all_trades[i].foi_lucro)
            wins_por_hora[hora]++;
    }
    
    // Encontrar melhores e piores horários
    int melhor_hora = 0, pior_hora = 0;
    double melhor_lucro = lucro_por_hora[0], pior_lucro = lucro_por_hora[0];
    
    for(int h = 1; h < 24; h++)
    {
        if(lucro_por_hora[h] > melhor_lucro)
        {
            melhor_lucro = lucro_por_hora[h];
            melhor_hora = h;
        }
        if(lucro_por_hora[h] < pior_lucro)
        {
            pior_lucro = lucro_por_hora[h];
            pior_hora = h;
        }
    }
    
    relatorio += "║ 🕐 Melhor horário: " + IntegerToString(melhor_hora) + ":00 (R$ " + DoubleToString(melhor_lucro, 2) + ")\n";
    relatorio += "║ 🕐 Pior horário: " + IntegerToString(pior_hora) + ":00 (R$ " + DoubleToString(pior_lucro, 2) + ")\n";
    relatorio += "╚═══════════════════════════════════════\n";
    
    return relatorio;
}

string CHistoryManager::AnalisarPadroesDiasSemana(void)
{
    if(!m_initialized || ArraySize(m_all_trades) == 0)
        return "❌ Sem dados para análise de padrões de dias da semana";
        
    string relatorio = "╔═══ ANÁLISE DE PADRÕES - DIAS DA SEMANA ═══\n";
    
    string nomes_dias[7] = {"Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"};
    int trades_por_dia[7];
    double lucro_por_dia[7];
    int wins_por_dia[7];
    
    ArrayInitialize(trades_por_dia, 0);
    ArrayInitialize(lucro_por_dia, 0.0);
    ArrayInitialize(wins_por_dia, 0);
    
    // Analisar todos os trades
    for(int i = 0; i < ArraySize(m_all_trades); i++)
    {
        MqlDateTime dt;
        TimeToStruct(m_all_trades[i].abertura, dt);
        int dia = dt.day_of_week;
        
        trades_por_dia[dia]++;
        lucro_por_dia[dia] += m_all_trades[i].lucro_liquido;
        if(m_all_trades[i].foi_lucro)
            wins_por_dia[dia]++;
    }
    
    // Mostrar estatísticas por dia
    for(int d = 1; d <= 5; d++) // Segunda a sexta
    {
        if(trades_por_dia[d] > 0)
        {
            double win_rate = (double)wins_por_dia[d] / trades_por_dia[d] * 100.0;
            relatorio += "║ " + nomes_dias[d] + ": " + IntegerToString(trades_por_dia[d]) + " trades, R$ " + 
                        DoubleToString(lucro_por_dia[d], 2) + " (" + DoubleToString(win_rate, 1) + "%)\n";
        }
    }
    
    relatorio += "╚═══════════════════════════════════════\n";
    
    return relatorio;
}

string CHistoryManager::AnalisarTendencias(void)
{
    if(!m_initialized || ArraySize(m_all_trades) < 10)
        return "❌ Dados insuficientes para análise de tendências (mín: 10 trades)";
        
    string relatorio = "╔═══ ANÁLISE DE TENDÊNCIAS ═══\n";
    
    // Analisar últimos 30 dias vs 30 dias anteriores
    datetime agora = TimeCurrent();
    datetime inicio_periodo_atual = agora - (30 * 24 * 3600);
    datetime inicio_periodo_anterior = inicio_periodo_atual - (30 * 24 * 3600);
    
    PeriodStats stats_atual, stats_anterior;
    CalcularEstatisticas(stats_atual, inicio_periodo_atual, agora);
    CalcularEstatisticas(stats_anterior, inicio_periodo_anterior, inicio_periodo_atual);
    
    if(stats_atual.total_trades > 0 && stats_anterior.total_trades > 0)
    {
        double variacao_trades = ((double)(stats_atual.total_trades - stats_anterior.total_trades) / stats_anterior.total_trades) * 100.0;
        double variacao_lucro = ((stats_atual.lucro_liquido_total - stats_anterior.lucro_liquido_total) / MathAbs(stats_anterior.lucro_liquido_total)) * 100.0;
        double variacao_winrate = stats_atual.win_rate - stats_anterior.win_rate;
        
        relatorio += "║ 📈 COMPARAÇÃO ÚLTIMOS 30 DIAS:\n";
        relatorio += "║ Trades: " + (variacao_trades >= 0 ? "+" : "") + DoubleToString(variacao_trades, 1) + "%\n";
        relatorio += "║ Lucro: " + (variacao_lucro >= 0 ? "+" : "") + DoubleToString(variacao_lucro, 1) + "%\n";
        relatorio += "║ Win Rate: " + (variacao_winrate >= 0 ? "+" : "") + DoubleToString(variacao_winrate, 1) + "%\n";
        
        // Determinar tendência geral
        int indicadores_positivos = 0;
        if(variacao_trades > 0) indicadores_positivos++;
        if(variacao_lucro > 0) indicadores_positivos++;
        if(variacao_winrate > 0) indicadores_positivos++;
        
        string tendencia = "";
        if(indicadores_positivos >= 2)
            tendencia = "📈 MELHORANDO";
        else if(indicadores_positivos == 1)
            tendencia = "📊 ESTÁVEL";
        else
            tendencia = "📉 DETERIORANDO";
            
        relatorio += "║ 🎯 Tendência: " + tendencia + "\n";
    }
    
    relatorio += "╚═══════════════════════════════════════\n";
    
    return relatorio;
}