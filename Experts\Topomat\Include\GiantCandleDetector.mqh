//+------------------------------------------------------------------+
//| GiantCandleDetector.mqh - Detecta candles gigantes no gráfico    |
//+------------------------------------------------------------------+
#ifndef __GIANT_CANDLE_DETECTOR_MQH__
#define __GIANT_CANDLE_DETECTOR_MQH__
#include <Trade\Trade.mqh>

// Classe para detecção de candles gigantes
class CGiantCandleDetector
  {
   private:
      int m_period;
      double m_multiplier;
      double m_volume_multiplier;
      double m_close_extreme_pct;
   public:
      // Construtor
      CGiantCandleDetector(int period=20, double multiplier=2.5, double volume_multiplier=1.5, double close_extreme_pct=0.8)
        {
         m_period=period;
         m_multiplier=multiplier;
         m_volume_multiplier=volume_multiplier;
         m_close_extreme_pct=close_extreme_pct;
        }
      // Detecta se o último candle é gigante e atende aos filtros
      bool IsGiantCandle(const MqlRates &rates[])
        {
         if(ArraySize(rates)<=m_period)
            return false;
         // Média do tamanho dos candles
         double sum=0;
         for(int i=1;i<=m_period;i++)
            sum+=MathAbs(rates[i].high-rates[i].low);
         double avg=sum/m_period;
         double last_size=MathAbs(rates[0].high-rates[0].low);
         if(last_size<avg*m_multiplier)
            return false;
         // Filtro de volume
         double vol_sum=0;
         for(int i=1;i<=m_period;i++)
            vol_sum+=(double)rates[i].tick_volume;
         double avg_vol=vol_sum/m_period;
         if(rates[0].tick_volume<avg_vol*m_volume_multiplier)
            return false;
         // Confirmação de fechamento no extremo
         double close_pct = (rates[0].close - rates[0].low) / (rates[0].high - rates[0].low + 0.0000001);
         if(rates[0].close > rates[0].open) // Alta
           {
            if(close_pct < m_close_extreme_pct)
               return false;
           }
         else // Baixa
           {
            if(close_pct > (1.0-m_close_extreme_pct))
               return false;
           }
         return true;
        }
      // Retorna o tamanho do último candle
      double LastCandleSize(const MqlRates &rates[])
        {
         return MathAbs(rates[0].high-rates[0].low);
        }
  };
#endif 