//+------------------------------------------------------------------+
//|                                       UltraScalper_ML_EA.mq5     |
//|                   Ultra Scalper com ML Avançado e Rede Neural   |
//|                           Versão v2.0.1 - <PERSON><PERSON><PERSON>     |
//|                                                                  |
//|  CONFIGURAÇÕES ESPECIAIS:                                        |
//|  - Take Profit: 5 pontos (fixo)                                 |
//|  - Stop Loss: DESABILITADO                                      |
//|  - Preços: Múltiplos de 5 pontos (obrigatório)                 |
//|  - Execução: Ultrarrápida (≤10 segundos)                       |
//+------------------------------------------------------------------+
#property copyright "Topomat Trading Systems"
#property link      "https://github.com/topomat"
#property version   "2.01"

#define EA_VERSION "v2.0.1"

// Includes
#include <Trade/Trade.mqh>
#include <Math/Stat/Math.mqh>
#include <Object.mqh>
#include <ChartObjects/ChartObjectsTxtControls.mqh>
#include "Include/UltraML.mqh"
#include "Include/LogManager.mqh"

//+------------------------------------------------------------------+
//| PARÂMETROS DO EA                                                 |
//+------------------------------------------------------------------+

// Parâmetros básicos
input group "=== CONFIGURAÇÕES BÁSICAS ==="
input double InpLotSize = 0.1;                          // Tamanho do lote
input int InpMagicNumber = 20250115;                     // Magic number
input int InpMaxSimultaneousPositions = 2;               // Máximo de posições simultâneas
input int InpMaxOperationsPerDay = 50;                   // Máximo de operações por dia

// Parâmetros de risco ultrarápido
input group "=== GESTÃO DE RISCO ULTRARÁPIDA ==="
input double InpTakeProfit = 5.0;                       // Take Profit em pontos (fixo)
input double InpStopLoss = 0.0;                         // Stop Loss DESABILITADO
input int InpMaxDurationSeconds = 10;                   // Duração máxima em segundos
input double InpMaxSpreadPoints = 1.5;                  // Spread máximo permitido
input double InpMaxDailyLoss = 100.0;                   // Perda máxima diária

// Parâmetros ML avançados
input group "=== MACHINE LEARNING AVANÇADO ==="
input bool InpUseAdvancedML = true;                     // Usar ML avançado
input bool InpUseEnsemble = true;                       // Usar ensemble de modelos
input bool InpUseCrossValidation = true;                // Usar cross-validation
input bool InpUseRegularization = true;                 // Usar regularização
input double InpMLThreshold = 0.85;                     // Threshold de confiança ML (aumentado)
input int InpMLRetrain = 30;                            // Retreinar a cada N operações
input bool InpMLAutoOptimize = true;                    // Otimização automática

// Parâmetros de sessão
input group "=== HORÁRIOS DE NEGOCIAÇÃO ==="
input int InpStartHour = 9;                             // Hora de início
input int InpStartMinute = 5;                           // Minuto de início
input int InpEndHour = 16;                              // Hora de fim
input int InpEndMinute = 30;                            // Minuto de fim
input bool InpTradeBreakSession = false;                // Negociar no intervalo

// Parâmetros do painel
input group "=== PAINEL VISUAL ==="
input bool InpPanelOnLeft = true;                       // Posicionar painel à esquerda
input int InpPanelXDistance = 10;                       // Distância horizontal do painel
input int InpPanelYDistance = 10;                       // Distância vertical do painel

// Parâmetros de notificação
input group "=== NOTIFICAÇÕES ==="
input bool InpSendPushOnTrade = true;                   // Enviar push nas operações ML
input bool InpSendPushOnTraining = true;                // Enviar push no treinamento ML

//+------------------------------------------------------------------+
//| VARIÁVEIS GLOBAIS                                                |
//+------------------------------------------------------------------+

// Objetos principais
CTrade trade;
CUltraML *ultra_ml_system;
CLogManager *log_manager;

// Controles
datetime last_trade_time = 0;
int daily_operations = 0;
double daily_pnl = 0.0;
datetime last_daily_reset = 0;

// Estatísticas avançadas
struct AdvancedStats
{
    int total_ml_predictions;
    int correct_ml_predictions;
    int ensemble_predictions;
    int neural_network_predictions;
    double average_confidence;
    double best_model_accuracy;
    int cross_validation_score;
    double regularization_loss;
    
    int ultrafast_trades;                                // Trades <= 5 segundos
    int fast_trades;                                     // Trades <= 10 segundos
    int slow_trades;                                     // Trades > 10 segundos
    
    double avg_profit_ultrafast;
    double avg_profit_fast;
    double avg_profit_slow;
    
    double ml_accuracy_today;
    double ensemble_accuracy_today;
    int retrain_count;
    
    datetime last_stats_update;
};

AdvancedStats g_advanced_stats;

// Controle de posições
struct PositionControl
{
    ulong ticket;
    datetime open_time;
    double open_price;
    double ml_confidence;
    string ml_model_used;
    uint execution_speed_ms;
    bool is_ultrafast;
};

PositionControl g_position_controls[100];
int g_position_count = 0;

//+------------------------------------------------------------------+
//| INICIALIZAÇÃO                                                    |
//+------------------------------------------------------------------+

int OnInit()
{
    Print("🚀 Iniciando UltraScalper ML EA ", EA_VERSION);
    
    // Configurar trade
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_IOC);
    
    // Inicializar log manager
    log_manager = new CLogManager();
    if(!log_manager.Initialize("UltraScalper_ML", true))
    {
        Print("❌ Falha ao inicializar sistema de logs");
        return INIT_FAILED;
    }
    
    // Verificar compatibilidade do símbolo
    if(!ValidateSymbolCompatibility())
    {
        Print("❌ Símbolo não compatível com preços múltiplos de 5");
        return INIT_FAILED;
    }
    
    // Inicializar sistema ML ultra avançado
    Print("🧠 Iniciando sistema ML ultra avançado...");
    ultra_ml_system = new CUltraML();
    if(!ultra_ml_system.Initialize(_Symbol))
    {
        Print("❌ Falha ao inicializar sistema ML ultra avançado");
        return INIT_FAILED;
    }
    
    // Verificar se o sistema foi treinado
    if(ultra_ml_system.IsModelTrained())
    {
        Print("✅ Sistema ML já possui modelo treinado!");
        Print("🎯 Precisão do modelo: ", DoubleToString(ultra_ml_system.GetAccuracy(), 2), "%");
        Print("🔄 Retreinamentos realizados: ", ultra_ml_system.GetRetrainCount());
        
        // Notificação de modelo carregado
        if(InpSendPushOnTraining)
        {
            SendNotification(StringFormat("✅ UltraScalper ML CARREGADO! Precisão: %.1f%% | Retreinos: %d", 
                                         ultra_ml_system.GetAccuracy(), ultra_ml_system.GetRetrainCount()));
        }
    }
    else
    {
        Print("🔄 Realizando treinamento inicial do modelo ML...");
        Print("⏳ Aguarde, coletando dados históricos para treinamento...");
        
        // Forçar treinamento inicial
        if(ultra_ml_system.ForceInitialTraining())
        {
            Print("✅ Treinamento inicial concluído com sucesso!");
            Print("🎯 Precisão inicial: ", DoubleToString(ultra_ml_system.GetAccuracy(), 2), "%");
            Print("📊 Amostras utilizadas: ", ultra_ml_system.GetTrainingSamples());
            
            // Notificação de treinamento concluído
            if(InpSendPushOnTraining)
            {
                SendNotification(StringFormat("🎓 UltraScalper ML TREINADO! Precisão: %.1f%% | Amostras: %d", 
                                             ultra_ml_system.GetAccuracy(), ultra_ml_system.GetTrainingSamples()));
            }
        }
        else
        {
            Print("❌ Falha no treinamento inicial - EA continuará com modelo básico");
            if(InpSendPushOnTraining)
            {
                SendNotification("❌ UltraScalper ML: Falha no treinamento inicial");
            }
        }
    }
    
    // Inicializar estatísticas
    ZeroMemory(g_advanced_stats);
    g_advanced_stats.last_stats_update = TimeCurrent();
    
    // Configurar painel avançado
    CreateAdvancedPanel();
    
    log_manager.LogInfo("Sistema ML avançado inicializado com sucesso");
    Print("✅ UltraScalper ML EA ", EA_VERSION, " iniciado com sucesso!");
    Print("🧠 Precisão do Sistema ML: ", DoubleToString(ultra_ml_system.GetAccuracy(), 2), "%");
    Print("⚙️ Configurações Especiais:");
    Print("   📊 Take Profit: ", DoubleToString(InpTakeProfit, 1), " pontos (fixo)");
    Print("   🚫 Stop Loss: DESABILITADO");
    Print("   🎯 Preços: Múltiplos de 5 pontos (obrigatório)");
    Print("   ⚡ Duração Máxima: ", InpMaxDurationSeconds, " segundos");
    Print("   🖥️ Painel: ", InpPanelOnLeft ? "Esquerda" : "Direita", " (", InpPanelXDistance, "x", InpPanelYDistance, ")");
    Print("   📱 Notificações: ", InpSendPushOnTrade ? "ATIVAS" : "INATIVAS", " | Treinamento: ", InpSendPushOnTraining ? "ATIVO" : "INATIVO");
    Print("");
    Print("🔍 COMO SABER QUE ESTÁ FUNCIONANDO:");
    Print("   ✅ Painel à ", InpPanelOnLeft ? "esquerda" : "direita", " com status '✅ TREINADO'");
    Print("   📊 Logs 'ML OPERACIONAL' a cada 10 predições");
    Print("   🎯 Mensagens 'ORDEM ML EXECUTADA' nas operações");
    Print("   📱 Notificações push (se ativadas)");
    Print("   🤖 Resumo completo a cada 5 minutos");
    Print("");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| TICK PRINCIPAL                                                   |
//+------------------------------------------------------------------+

void OnTick()
{
    // Verificar se está no horário de negociação
    if(!IsWithinTradingHours())
        return;
    
    // Reset diário
    CheckDailyReset();
    
    // Verificar limites
    if(daily_operations >= InpMaxOperationsPerDay)
        return;
    
    if(daily_pnl <= -InpMaxDailyLoss)
        return;
    
    // Verificar spread - usando método oficial da documentação MT5
    double ask_price;
    double bid_price;
    double point_value;
    
    // Obter ASK, BID e POINT usando versão bool oficial
    if(!SymbolInfoDouble(_Symbol, SYMBOL_ASK, ask_price))
        return;
    if(!SymbolInfoDouble(_Symbol, SYMBOL_BID, bid_price))
        return;
    if(!SymbolInfoDouble(_Symbol, SYMBOL_POINT, point_value))
        return;
    
    // Calcular spread manualmente (conforme documentação MT5)
    double spread = ask_price - bid_price;
    int spread_points = (int)MathRound(spread / point_value);
    if(spread_points > InpMaxSpreadPoints)
        return;
    
    // Verificar posições existentes
    int positions = CountOpenPositions();
    if(positions >= InpMaxSimultaneousPositions)
        return;
    
    // Análise ML avançada
    if(InpUseAdvancedML)
    {
        PerformAdvancedMLAnalysis();
    }
    
    // Monitorar posições existentes
    MonitorPositions();
    
    // Mostrar status ML periodicamente (a cada 5 minutos)
    static datetime last_status_time = 0;
    if(TimeCurrent() - last_status_time >= 300) // 5 minutos
    {
        ShowMLStatusSummary();
        last_status_time = TimeCurrent();
    }
    
    // Atualizar painel
    UpdateAdvancedPanel();
}

//+------------------------------------------------------------------+
//| ANÁLISE ML AVANÇADA                                              |
//+------------------------------------------------------------------+

void PerformAdvancedMLAnalysis()
{
    // Verificar cooldown
    if(TimeCurrent() - last_trade_time < 1) // Mínimo 1 segundo
        return;
    
    uint start_time = GetTickCount();
    
    // Extrair features ultra
    UltraFeatures features = ultra_ml_system.ExtractFeatures();
    
    // Predição do sistema ML
    double ml_confidence = ultra_ml_system.PredictUltra(features);
    
    // Atualizar estatísticas
    g_advanced_stats.total_ml_predictions++;
    g_advanced_stats.average_confidence = 
        (g_advanced_stats.average_confidence * (g_advanced_stats.total_ml_predictions - 1) + ml_confidence) / 
        g_advanced_stats.total_ml_predictions;
    g_advanced_stats.last_stats_update = TimeCurrent(); // Atualizar timestamp de atividade
    
    uint analysis_time = GetTickCount() - start_time;
    
    // Log detalhado da análise
    log_manager.LogInfo(StringFormat("🧠 ML ANÁLISE - Confiança: %.3f | Tempo: %dms | Predições: %d", 
                                     ml_confidence, analysis_time, g_advanced_stats.total_ml_predictions));
    
    // Log no console a cada 10 predições para mostrar atividade
    if(g_advanced_stats.total_ml_predictions % 10 == 0)
    {
        Print("🧠 ML OPERACIONAL - Predições: ", g_advanced_stats.total_ml_predictions, 
              " | Confiança Média: ", DoubleToString(g_advanced_stats.average_confidence, 3),
              " | Precisão: ", DoubleToString(ultra_ml_system.GetAccuracy(), 2), "%");
    }
    
    // Decisão de entrada
    if(ml_confidence > InpMLThreshold)
    {
        // Determinar direção baseada em features  
        ENUM_ORDER_TYPE order_type = DetermineOrderDirection(features);
        
        // Executar ordem ultrarrápida
        ExecuteUltraFastOrder(order_type, ml_confidence, features);
    }
    else if(ml_confidence < (1.0 - InpMLThreshold))
    {
        // Sinal de venda
        ExecuteUltraFastOrder(ORDER_TYPE_SELL, 1.0 - ml_confidence, features);
    }
}

//+------------------------------------------------------------------+
//| EXECUÇÃO ULTRARRÁPIDA                                            |
//+------------------------------------------------------------------+

void ExecuteUltraFastOrder(ENUM_ORDER_TYPE order_type, double confidence, const UltraFeatures &features)
{
    uint execution_start = GetTickCount();
    
    // Calcular preços
    double ask_price;
    double bid_price;
    double point_value;
    
    // Obter preços usando versão bool oficial (documentação MT5)
    if(!SymbolInfoDouble(_Symbol, SYMBOL_ASK, ask_price))
        return;
    if(!SymbolInfoDouble(_Symbol, SYMBOL_BID, bid_price))
        return;
    if(!SymbolInfoDouble(_Symbol, SYMBOL_POINT, point_value))
        return;
    
    double current_price = (order_type == ORDER_TYPE_BUY) ? ask_price : bid_price;
    
    // Ajustar preço para múltiplo de 5
    double adjusted_price = AdjustPriceToMultipleOf5(current_price, point_value);
    
    // Verificar se o preço ajustado é válido
    if(MathAbs(adjusted_price - current_price) > 2 * point_value)
    {
        log_manager.LogWarning("PRICE_ADJUSTMENT", StringFormat("Preço não ajustável para múltiplo de 5: %.5f", current_price));
        return;
    }
    
    // Calcular TP fixo de 5 pontos
    double tp_points = InpTakeProfit; // 5 pontos fixos
    double tp_price;
    
    if(order_type == ORDER_TYPE_BUY)
    {
        tp_price = adjusted_price + tp_points * point_value;
    }
    else
    {
        tp_price = adjusted_price - tp_points * point_value;
    }
    
    // Ajustar TP para múltiplo de 5
    tp_price = AdjustPriceToMultipleOf5(tp_price, point_value);
    
    // Validação final dos preços
    if(!ValidatePriceMultipleOf5(adjusted_price, point_value) || 
       !ValidatePriceMultipleOf5(tp_price, point_value))
    {
        log_manager.LogError("PRICE_VALIDATION", StringFormat("Preços não são múltiplos de 5: Entry=%.5f, TP=%.5f", 
                                         adjusted_price, tp_price), GetLastError());
        return;
    }
    
    // Execução com tipo de preenchimento otimizado
    trade.SetTypeFilling(ORDER_FILLING_IOC); // Immediate or Cancel
    
    bool result = false;
    if(order_type == ORDER_TYPE_BUY)
    {
        result = trade.Buy(InpLotSize, _Symbol, adjusted_price, 0, tp_price, 
                          StringFormat("UltraML_%.2f", confidence));
    }
    else
    {
        result = trade.Sell(InpLotSize, _Symbol, adjusted_price, 0, tp_price, 
                           StringFormat("UltraML_%.2f", confidence));
    }
    
    uint execution_time = GetTickCount() - execution_start;
    
    if(result)
    {
        // Registrar controle da posição
        if(g_position_count < 100)
        {
            g_position_controls[g_position_count].ticket = trade.ResultOrder();
            g_position_controls[g_position_count].open_time = TimeCurrent();
            g_position_controls[g_position_count].open_price = adjusted_price;
            g_position_controls[g_position_count].ml_confidence = confidence;
            g_position_controls[g_position_count].ml_model_used = "Ensemble";
            g_position_controls[g_position_count].execution_speed_ms = execution_time;
            g_position_controls[g_position_count].is_ultrafast = (execution_time <= 50);
            g_position_count++;
        }
        
        // Atualizar estatísticas
        daily_operations++;
        last_trade_time = TimeCurrent();
        
        if(execution_time <= 50)
            g_advanced_stats.ultrafast_trades++;
        else if(execution_time <= 100)
            g_advanced_stats.fast_trades++;
        else
            g_advanced_stats.slow_trades++;
        
        // Log da execução
        log_manager.LogInfo(StringFormat("🎯 ORDEM ML EXECUTADA: %s | Confiança: %.3f | Tempo: %dms | Entry: %.5f | TP: %.5f",
                                        EnumToString(order_type), confidence, execution_time, adjusted_price, tp_price));
        
        Print(StringFormat("🎯 ORDEM ML EXECUTADA: %s | Confiança: %.2f%% | Tempo: %dms | Entry: %.5f | TP: %.5f", 
                          EnumToString(order_type), confidence * 100, execution_time, adjusted_price, tp_price));
        
        // Notificação push para mostrar que ML está operando
        if(InpSendPushOnTrade)
        {
            SendNotification(StringFormat("🤖 UltraScalper ML ATIVO! %s | Confiança: %.1f%% | Tempo: %dms", 
                                         EnumToString(order_type), confidence * 100, execution_time));
        }
    }
    else
    {
        log_manager.LogError("ORDER_EXECUTION", StringFormat("Order failed: %s, Error: %d", 
                                         EnumToString(order_type), trade.ResultRetcode()), trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| VALIDAÇÃO DE PREÇOS MÚLTIPLOS DE 5                               |
//+------------------------------------------------------------------+

bool ValidatePriceMultipleOf5(double price, double point_value)
{
    // Converter preço em pontos
    double price_in_points = price / point_value;
    
    // Verificar se é múltiplo de 5
    double remainder = MathMod(price_in_points, 5.0);
    
    // Tolerância para erros de ponto flutuante
    return (remainder < 0.001 || remainder > 4.999);
}

//+------------------------------------------------------------------+
//| DETERMINAR DIREÇÃO DA ORDEM                                      |
//+------------------------------------------------------------------+

ENUM_ORDER_TYPE DetermineOrderDirection(const UltraFeatures &features)
{
    double buy_score = 0;
    double sell_score = 0;
    
    // Análise de momentum
    if(features.price_velocity > 0) buy_score += 1;
    else sell_score += 1;
    
    if(features.price_acceleration > 0) buy_score += 1;
    else sell_score += 1;
    
    // Análise de volume
    if(features.volume_ratio > 1.2) buy_score += 1;
    
    // Análise técnica
    if(features.rsi_value < 30) buy_score += 2;
    else if(features.rsi_value > 70) sell_score += 2;
    
    // Análise de tendência
    if(features.trend_strength > 0.6) buy_score += 1;
    else if(features.trend_strength < 0.4) sell_score += 1;
    
    // Análise de Bollinger
    if(features.bollinger_pos < 0.2) buy_score += 1;
    else if(features.bollinger_pos > 0.8) sell_score += 1;
    
    // Análise de padrões
    if(features.candlestick_pattern > 0.7) buy_score += 1;
    else if(features.candlestick_pattern < 0.3) sell_score += 1;
    
    // Análise de microestrutura
    if(features.bid_ask_imbalance > 0.1) buy_score += 1;
    else if(features.bid_ask_imbalance < -0.1) sell_score += 1;
    
    return (buy_score > sell_score) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
}

//+------------------------------------------------------------------+
//| MONITORAMENTO DE POSIÇÕES                                        |
//+------------------------------------------------------------------+

void MonitorPositions()
{
    for(int i = 0; i < g_position_count; i++)
    {
        if(g_position_controls[i].ticket == 0)
            continue;
        
        // Verificar se posição ainda existe
        if(!PositionSelectByTicket(g_position_controls[i].ticket))
        {
            // Posição fechada - registrar resultado
            RegisterPositionResult(i);
            g_position_controls[i].ticket = 0;
            continue;
        }
        
        // Verificar timeout ultrarrápido
        datetime current_time = TimeCurrent();
        int duration = (int)(current_time - g_position_controls[i].open_time);
        
        if(duration > InpMaxDurationSeconds)
        {
            // Fechar posição por timeout
            ClosePositionByTimeout(g_position_controls[i].ticket);
            
            log_manager.LogInfo(StringFormat("Position closed by timeout: %I64u, Duration: %ds", 
                                           g_position_controls[i].ticket, duration));
        }
    }
}

//+------------------------------------------------------------------+
//| REGISTRAR RESULTADO DA POSIÇÃO                                   |
//+------------------------------------------------------------------+

void RegisterPositionResult(int control_index)
{
    // Obter dados da posição fechada
    HistorySelectByPosition(g_position_controls[control_index].ticket);
    
    double profit = 0;
    datetime close_time = 0;
    
    // Procurar deal de fechamento
    for(int i = HistoryDealsTotal() - 1; i >= 0; i--)
    {
        ulong deal_ticket = HistoryDealGetTicket(i);
        if(deal_ticket == 0) continue;
        
        if(HistoryDealGetInteger(deal_ticket, DEAL_POSITION_ID) == g_position_controls[control_index].ticket)
        {
            if(HistoryDealGetInteger(deal_ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
            {
                profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
                close_time = (datetime)HistoryDealGetInteger(deal_ticket, DEAL_TIME);
                break;
            }
        }
    }
    
    // Calcular duração
    int duration = (int)(close_time - g_position_controls[control_index].open_time);
    
    // Atualizar estatísticas
    daily_pnl += profit;
    
    // Classificar por velocidade
    if(duration <= 5)
    {
        g_advanced_stats.avg_profit_ultrafast = 
            (g_advanced_stats.avg_profit_ultrafast * (g_advanced_stats.ultrafast_trades - 1) + profit) / 
            g_advanced_stats.ultrafast_trades;
    }
    else if(duration <= 10)
    {
        g_advanced_stats.avg_profit_fast = 
            (g_advanced_stats.avg_profit_fast * (g_advanced_stats.fast_trades - 1) + profit) / 
            g_advanced_stats.fast_trades;
    }
    else
    {
        g_advanced_stats.avg_profit_slow = 
            (g_advanced_stats.avg_profit_slow * (g_advanced_stats.slow_trades - 1) + profit) / 
            g_advanced_stats.slow_trades;
    }
    
    // Verificar se predição foi correta
    bool prediction_correct = (profit > 0);
    if(prediction_correct)
    {
        g_advanced_stats.correct_ml_predictions++;
    }
    
    // Atualizar precisão do ML
    if(g_advanced_stats.total_ml_predictions > 0)
    {
        g_advanced_stats.ml_accuracy_today = 
            (double)g_advanced_stats.correct_ml_predictions / g_advanced_stats.total_ml_predictions * 100.0;
    }
    
    // Log resultado
    log_manager.LogInfo(StringFormat("Position result: Ticket=%I64u, Profit=%.2f, Duration=%ds, Confidence=%.3f",
                                     g_position_controls[control_index].ticket, profit, duration, 
                                     g_position_controls[control_index].ml_confidence));
    
    // Verificar necessidade de retreinamento
    if(g_advanced_stats.total_ml_predictions % InpMLRetrain == 0)
    {
        ScheduleMLRetraining();
    }
}

//+------------------------------------------------------------------+
//| RETREINAMENTO DO ML                                              |
//+------------------------------------------------------------------+

void ScheduleMLRetraining()
{
    if(!InpMLAutoOptimize)
        return;
    
    Print("🔄 Iniciando retreinamento do sistema ML...");
    
    // Retreinar sistema avançado
    if(ultra_ml_system.TrainEnsembleModel())
    {
        g_advanced_stats.retrain_count++;
        g_advanced_stats.best_model_accuracy = ultra_ml_system.GetEnsembleAccuracy();
        
        Print("✅ Retreinamento concluído - Nova precisão: ", 
              DoubleToString(g_advanced_stats.best_model_accuracy, 2), "%");
        
        log_manager.LogInfo(StringFormat("ML retrained - New accuracy: %.2f%%", 
                                       g_advanced_stats.best_model_accuracy));
    }
    else
    {
        Print("❌ Falha no retreinamento do ML");
        log_manager.LogError("ML_TRAINING", "ML retraining failed", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| PAINEL AVANÇADO                                                  |
//+------------------------------------------------------------------+

void CreateAdvancedPanel()
{
    // Criar painel avançado com posicionamento configurável
    // O usuário pode escolher esquerda/direita e ajustar distâncias
    string panel_name = "UltraScalperAdvancedPanel";
    
    // Painel principal - posicionamento configurável
    ObjectCreate(0, panel_name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, panel_name, OBJPROP_CORNER, InpPanelOnLeft ? CORNER_LEFT_UPPER : CORNER_RIGHT_UPPER);
    ObjectSetInteger(0, panel_name, OBJPROP_XDISTANCE, InpPanelXDistance);
    ObjectSetInteger(0, panel_name, OBJPROP_YDISTANCE, InpPanelYDistance);
    ObjectSetInteger(0, panel_name, OBJPROP_XSIZE, 320);
    ObjectSetInteger(0, panel_name, OBJPROP_YSIZE, 425);
    ObjectSetInteger(0, panel_name, OBJPROP_BGCOLOR, clrDarkSlateGray);
    ObjectSetInteger(0, panel_name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, panel_name, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, panel_name, OBJPROP_WIDTH, 2);
    
    // Título
    ObjectCreate(0, panel_name + "_title", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, panel_name + "_title", OBJPROP_CORNER, InpPanelOnLeft ? CORNER_LEFT_UPPER : CORNER_RIGHT_UPPER);
    ObjectSetInteger(0, panel_name + "_title", OBJPROP_XDISTANCE, InpPanelXDistance + 10);
    ObjectSetInteger(0, panel_name + "_title", OBJPROP_YDISTANCE, InpPanelYDistance + 5);
    ObjectSetString(0, panel_name + "_title", OBJPROP_TEXT, "🚀 UltraScalper ML " + EA_VERSION + " | TP:5pts | NO SL");
    
    // Criar labels para informações
    string labels[] = {
        "ml_status", "ensemble_accuracy", "daily_ops", "daily_pnl",
        "ultrafast_trades", "fast_trades", "slow_trades", "ml_accuracy",
        "avg_confidence", "retrain_count", "best_model", "execution_speed", "last_activity"
    };
    
    for(int i = 0; i < ArraySize(labels); i++)
    {
        ObjectCreate(0, panel_name + "_" + labels[i], OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, panel_name + "_" + labels[i], OBJPROP_CORNER, InpPanelOnLeft ? CORNER_LEFT_UPPER : CORNER_RIGHT_UPPER);
        ObjectSetInteger(0, panel_name + "_" + labels[i], OBJPROP_XDISTANCE, InpPanelXDistance + 10);
        ObjectSetInteger(0, panel_name + "_" + labels[i], OBJPROP_YDISTANCE, InpPanelYDistance + 30 + (i * 25));
        ObjectSetString(0, panel_name + "_" + labels[i], OBJPROP_TEXT, "");
    }
}

void UpdateAdvancedPanel()
{
    string panel_name = "UltraScalperAdvancedPanel";
    
    // Status do ML com indicação de treinamento
    string ml_status = "INATIVO";
    if(InpUseAdvancedML && ultra_ml_system != NULL)
    {
        if(ultra_ml_system.IsModelTrained())
        {
            ml_status = "✅ TREINADO";
        }
        else
        {
            ml_status = "🔄 TREINANDO...";
        }
    }
    
    // Atualizar informações
    ObjectSetString(0, panel_name + "_ml_status", OBJPROP_TEXT, 
                  StringFormat("🧠 ML Status: %s", ml_status));
    
    ObjectSetString(0, panel_name + "_ensemble_accuracy", OBJPROP_TEXT, 
                  StringFormat("🎯 Precisão: %.2f%% | Amostras: %d", 
                              ultra_ml_system.GetAccuracy(), ultra_ml_system.GetTrainingSamples()));
    
    ObjectSetString(0, panel_name + "_daily_ops", OBJPROP_TEXT, 
                  StringFormat("📊 Operações: %d/%d", daily_operations, InpMaxOperationsPerDay));
    
    ObjectSetString(0, panel_name + "_daily_pnl", OBJPROP_TEXT, 
                  StringFormat("💰 PnL Diário: %.2f", daily_pnl));
    
    ObjectSetString(0, panel_name + "_ultrafast_trades", OBJPROP_TEXT, 
                  StringFormat("⚡ Ultra (≤5s): %d (%.2f)", g_advanced_stats.ultrafast_trades, g_advanced_stats.avg_profit_ultrafast));
    
    ObjectSetString(0, panel_name + "_fast_trades", OBJPROP_TEXT, 
                  StringFormat("🏃 Rápidas (≤10s): %d (%.2f)", g_advanced_stats.fast_trades, g_advanced_stats.avg_profit_fast));
    
    ObjectSetString(0, panel_name + "_slow_trades", OBJPROP_TEXT, 
                  StringFormat("🐌 Lentas (>10s): %d (%.2f)", g_advanced_stats.slow_trades, g_advanced_stats.avg_profit_slow));
    
    ObjectSetString(0, panel_name + "_ml_accuracy", OBJPROP_TEXT, 
                  StringFormat("🎯 ML Hoje: %.2f%%", g_advanced_stats.ml_accuracy_today));
    
    ObjectSetString(0, panel_name + "_avg_confidence", OBJPROP_TEXT, 
                  StringFormat("🔍 Confiança Média: %.3f", g_advanced_stats.average_confidence));
    
    ObjectSetString(0, panel_name + "_retrain_count", OBJPROP_TEXT, 
                  StringFormat("🔄 Retreinamentos: %d", g_advanced_stats.retrain_count));
    
    ObjectSetString(0, panel_name + "_best_model", OBJPROP_TEXT, 
                  StringFormat("🏆 Melhor Modelo: %.2f%%", g_advanced_stats.best_model_accuracy));
    
    // Tempo desde última predição ML
    datetime current_time = TimeCurrent();
    int seconds_since_last = (int)(current_time - g_advanced_stats.last_stats_update);
    string last_activity = "Nunca";
    if(g_advanced_stats.total_ml_predictions > 0)
    {
        if(seconds_since_last < 60)
            last_activity = StringFormat("%ds atrás", seconds_since_last);
        else if(seconds_since_last < 3600)
            last_activity = StringFormat("%dm atrás", seconds_since_last / 60);
        else
            last_activity = StringFormat("%dh atrás", seconds_since_last / 3600);
    }
    
    ObjectSetString(0, panel_name + "_execution_speed", OBJPROP_TEXT, 
                  StringFormat("⏱️ Config: TP:%gpts | SL:OFF | Preços:×5", InpTakeProfit));
                  
    ObjectSetString(0, panel_name + "_last_activity", OBJPROP_TEXT, 
                  StringFormat("🕐 Última Análise ML: %s", last_activity));
}

//+------------------------------------------------------------------+
//| FUNÇÕES AUXILIARES                                               |
//+------------------------------------------------------------------+

//--- Validar compatibilidade do símbolo
bool ValidateSymbolCompatibility()
{
    double point;
    double tick_size;
    
    // Obter informações do símbolo usando versão bool oficial (documentação MT5)
    if(!SymbolInfoDouble(_Symbol, SYMBOL_POINT, point))
    {
        Print("❌ Erro ao obter SYMBOL_POINT");
        return false;
    }
    if(!SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE, tick_size))
    {
        Print("❌ Erro ao obter SYMBOL_TRADE_TICK_SIZE");
        return false;
    }
    
    // Verificar se o tick size permite múltiplos de 5 pontos
    double five_points = 5.0 * point;
    double ratio = five_points / tick_size;
    
    // Deve ser um número inteiro ou muito próximo
    bool compatible = (MathAbs(ratio - MathRound(ratio)) < 0.001);
    
    if(compatible)
    {
        Print("✅ Símbolo compatível - 5 pontos = ", DoubleToString(five_points, 5), 
              " (", DoubleToString(ratio, 2), " ticks)");
    }
    else
    {
        Print("❌ Símbolo incompatível - 5 pontos = ", DoubleToString(five_points, 5), 
              " / tick size = ", DoubleToString(tick_size, 5), " (ratio: ", DoubleToString(ratio, 4), ")");
    }
    
    return compatible;
}

//--- Ajustar preço para múltiplo de 5 pontos
double AdjustPriceToMultipleOf5(double price, double point_value)
{
    // Converter preço em pontos
    double price_in_points = price / point_value;
    
    // Arredondar para múltiplo de 5
    double rounded_points = MathRound(price_in_points / 5.0) * 5.0;
    
    // Converter de volta para preço
    return rounded_points * point_value;
}

bool IsWithinTradingHours()
{
    MqlDateTime dt;
    TimeCurrent(dt);
    
    int current_minutes = dt.hour * 60 + dt.min;
    int start_minutes = InpStartHour * 60 + InpStartMinute;
    int end_minutes = InpEndHour * 60 + InpEndMinute;
    
    return (current_minutes >= start_minutes && current_minutes <= end_minutes);
}

void CheckDailyReset()
{
    MqlDateTime dt;
    TimeCurrent(dt);
    
    MqlDateTime last_dt;
    TimeToStruct(last_daily_reset, last_dt);
    if(dt.day != last_dt.day)
    {
        daily_operations = 0;
        daily_pnl = 0.0;
        last_daily_reset = TimeCurrent();
        
        // Reset estatísticas diárias
        g_advanced_stats.ml_accuracy_today = 0;
        g_advanced_stats.total_ml_predictions = 0;
        g_advanced_stats.correct_ml_predictions = 0;
        
        log_manager.LogInfo("Daily reset performed");
    }
}

int CountOpenPositions()
{
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetTicket(i) != 0)
        {
            if(PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
                count++;
        }
    }
    return count;
}

void ClosePositionByTimeout(ulong ticket)
{
    if(PositionSelectByTicket(ticket))
    {
        double volume = PositionGetDouble(POSITION_VOLUME);
        ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        
        if(type == POSITION_TYPE_BUY)
        {
            trade.Sell(volume, _Symbol, 0, 0, 0, "Timeout");
        }
        else
        {
            trade.Buy(volume, _Symbol, 0, 0, 0, "Timeout");
        }
    }
}

void ShowMLStatusSummary()
{
    if(ultra_ml_system == NULL || !InpUseAdvancedML)
        return;
        
    Print("🤖 ===== RESUMO ML ULTRA SCALPER =====");
    Print("🧠 Status: ", ultra_ml_system.IsModelTrained() ? "✅ TREINADO" : "🔄 TREINANDO");
    Print("🎯 Precisão Atual: ", DoubleToString(ultra_ml_system.GetAccuracy(), 2), "%");
    Print("📊 Total de Predições Hoje: ", g_advanced_stats.total_ml_predictions);
    Print("🔍 Confiança Média: ", DoubleToString(g_advanced_stats.average_confidence, 3));
    Print("⚡ Operações Ultrarrápidas: ", g_advanced_stats.ultrafast_trades);
    Print("🏃 Operações Rápidas: ", g_advanced_stats.fast_trades);
    Print("🐌 Operações Lentas: ", g_advanced_stats.slow_trades);
    Print("💰 PnL Diário: R$ ", DoubleToString(daily_pnl, 2));
    Print("📈 Operações Hoje: ", daily_operations, "/", InpMaxOperationsPerDay);
    Print("🔄 Retreinamentos: ", ultra_ml_system.GetRetrainCount());
    Print("🤖 =====================================");
}

//+------------------------------------------------------------------+
//| EVENTOS                                                          |
//+------------------------------------------------------------------+

void OnDeinit(const int reason)
{
    // Salvar dados do ML
    if(ultra_ml_system != NULL)
    {
        ultra_ml_system.SaveModel();
        delete ultra_ml_system;
    }
    
    // Cleanup
    if(log_manager != NULL)
    {
        log_manager.LogInfo("UltraScalper ML EA stopped");
        delete log_manager;
    }
    
    // Remover objetos do painel
    ObjectsDeleteAll(0, "UltraScalperAdvancedPanel");
    
    Print("🛑 UltraScalper ML EA ", EA_VERSION, " finalizado");
}

void OnTrade()
{
    // Processar eventos de negociação
    // Será implementado conforme necessário
}

void OnTimer()
{
    // Timer para operações periódicas
    // Será implementado conforme necessário
} 