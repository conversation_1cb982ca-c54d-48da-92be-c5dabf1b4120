//+------------------------------------------------------------------+
//|                                                   SRAnalyzer.mqh |
//|                        Copyright 2024, Topomat Trading Systems   |
//|                                             https://topomat.com   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Topomat Trading Systems"
#property link      "https://topomat.com"

//--- Includes necessários
#include "LogManager.mqh"
#include "DataValidator.mqh"

//+------------------------------------------------------------------+
//| SISTEMA DE ANÁLISE SUPORTE E RESISTÊNCIA                         |
//+------------------------------------------------------------------+

//--- Origem do nível S&R
enum ENUM_ORIGEM_SR
{
   ORIGEM_PIVOT = 0,        // Pivô histórico
   ORIGEM_VOLUME = 1,       // Volume Profile
   ORIGEM_REDONDO = 2,      // Número redondo
   ORIGEM_FIBONACCI = 3     // Fibonacci
};

//--- Estrutura de ponto de interesse S&R
struct SSRPoint
{
   double            price;              // Preço do ponto
   ENUM_ORIGEM_SR    origin;             // Origem do ponto
   double            strength;           // Força do ponto (1-10)
   datetime          created_time;       // Timestamp de criação
   int               confirmations;      // Quantas vezes foi confirmado
   datetime          last_confirmation; // Última vez que foi testado
   double            temporal_weight;    // Peso baseado na idade
   int               importance;         // Nível de importância (1-5)
   double            touch_tolerance;    // Tolerância para considerar toque
   int               timeframe_detected; // Timeframe onde foi detectado
};

//--- Configurações da análise S&R
struct SSRConfig
{
   int               history_days;           // Dias de histórico para análise
   int               min_confirmations;      // Mínimo de confirmações para validar
   double            touch_tolerance;        // Tolerância para considerar toque (pontos)
   int               recent_days_weight;     // Dias considerados "recentes"
   double            recent_multiplier;      // Multiplicador para pontos recentes
   int               max_active_levels;      // Máximo de níveis ativos
   double            min_strength;           // Força mínima para considerar válido
   bool              use_volume_profile;     // Usar Volume Profile
   bool              use_fibonacci;          // Usar Fibonacci
   bool              use_round_numbers;      // Usar números redondos
};

//+------------------------------------------------------------------+
//| Classe para análise de Suporte e Resistência                     |
//+------------------------------------------------------------------+

class CSRAnalyzer
{
private:
   //--- Configurações
   SSRConfig         m_config;
   string            m_symbol;
   ENUM_TIMEFRAMES   m_timeframe;
   
   //--- Arrays de dados
   SSRPoint          m_sr_points[];        // Pontos S&R identificados
   int               m_total_points;       // Total de pontos ativos
   
   //--- Controle temporal
   datetime          m_last_analysis;      // Última análise completa
   datetime          m_last_pivot_scan;    // Última busca por pivôs
   datetime          m_last_round_scan;    // Última busca por números redondos
   
   //--- Referências externas
   CLogManager      *m_logger;
   CDataValidator   *m_validator;
   
   //--- Dados de mercado
   MqlRates          m_rates[];            // Dados históricos
   int               m_rates_total;        // Total de barras carregadas
   
public:
   //--- Construtor/Destrutor
                     CSRAnalyzer(void);
                    ~CSRAnalyzer(void);
   
   //--- Inicialização
   bool              Initialize(string symbol, ENUM_TIMEFRAMES timeframe, SSRConfig &config);
   void              SetLogger(CLogManager *logger) { m_logger = logger; }
   void              SetValidator(CDataValidator *validator) { m_validator = validator; }
   
   //--- Análise principal
   bool              AnalyzeSupportResistance(void);
   bool              UpdateSRPoints(void);
   
   //--- Métodos de análise específicos
   bool              DetectHistoricalPivots(void);
   bool              DetectRoundNumbers(void);
   bool              DetectVolumeProfile(void);
   bool              DetectFibonacciLevels(void);
   
   //--- Cálculos de peso e força
   void              CalculateTemporalWeights(void);
   void              CalculateStrengthValues(void);
   void              SortPointsByStrength(void);
   
   //--- Gestão de pontos
   bool              AddSRPoint(double price, ENUM_ORIGEM_SR origin, datetime timestamp, double initial_strength = 1.0);
   bool              ExistsNearbyPoint(double price, double tolerance);
   void              IncrementConfirmations(double price, double tolerance, datetime timestamp);
   void              RemoveWeakPoints(void);
   void              RemoveOldPoints(void);
   
   //--- Consultas
   int               GetTotalPoints(void) { return m_total_points; }
   void              GetSRPoints(SSRPoint &points[]);
   bool              GetStrongestPoint(SSRPoint &point);
   bool              FindNearestPoint(double price, SSRPoint &point, double max_distance = 0);
   
   //--- Análise de mercado
   bool              IsPriceNearSR(double price, double &nearest_sr_price, double &distance);
   ENUM_ORIGEM_SR    GetPointOrigin(int index);
   double            GetPointStrength(int index);
   int               GetPointConfirmations(int index);
   
   //--- Configuração
   void              SetConfig(SSRConfig &config) { m_config = config; }
   SSRConfig         GetConfig(void) { return m_config; }
   
   //--- Métodos de desenho/visualização
   bool              DrawSRLevels(void);
   bool              UpdateSRDisplay(void);
   bool              IsEntryCandidate(double point_price, double current_price);
   
private:
   //--- Métodos auxiliares privados
   bool              LoadHistoricalData(void);
   bool              ValidateHistoricalData(void);
   
   //--- Detecção de pivôs
   bool              IsPivotHigh(int index, int left_bars = 2, int right_bars = 2);
   bool              IsPivotLow(int index, int left_bars = 2, int right_bars = 2);
   double            CalculatePivotStrength(int index, bool is_high);
   
   //--- Números redondos
   bool              IsRoundNumber(double price);
   double            GetNearestRoundNumber(double price);
   double            CalculateRoundNumberStrength(double price);
   
   //--- Cálculos temporais
   double            CalculateTimeDecay(datetime point_time);
   double            CalculateRecentBonus(datetime point_time);
   
   //--- Validação de pontos
   bool              IsValidSRPoint(double price, ENUM_ORIGEM_SR origin);
   bool              ShouldRemovePoint(int index);
   
   //--- Otimização de arrays
   void              OptimizePointsArray(void);
   void              CompactArray(void);
   
   //--- Utilitários
   string            OriginToString(ENUM_ORIGEM_SR origin);
   color             GetOriginColor(ENUM_ORIGEM_SR origin);
   int               GetOriginLineStyle(ENUM_ORIGEM_SR origin);
   
   //--- Novas funções adicionadas
   void              ConfluirPivosProximos();
};

//+------------------------------------------------------------------+
//| Construtor                                                        |
//+------------------------------------------------------------------+
CSRAnalyzer::CSRAnalyzer(void)
{
   m_symbol = _Symbol;
   m_timeframe = _Period;
   m_total_points = 0;
   m_last_analysis = 0;
   m_last_pivot_scan = 0;
   m_last_round_scan = 0;
   m_logger = NULL;
   m_validator = NULL;
   m_rates_total = 0;
   
   // Configurações padrão
   m_config.history_days = 60;
   m_config.min_confirmations = 2;
   m_config.touch_tolerance = 30.0;
   m_config.recent_days_weight = 30;
   m_config.recent_multiplier = 2.0;
   m_config.max_active_levels = 50;
   m_config.min_strength = 1.5;
   m_config.use_volume_profile = false; // Não implementado ainda
   m_config.use_fibonacci = false;      // Não implementado ainda
   m_config.use_round_numbers = true;
   
   // Inicializar arrays
   ArrayResize(m_sr_points, m_config.max_active_levels);
   ArrayResize(m_rates, 0);
}

//+------------------------------------------------------------------+
//| Destrutor                                                         |
//+------------------------------------------------------------------+
CSRAnalyzer::~CSRAnalyzer(void)
{
   // Limpar objetos gráficos se necessário
   for(int i = ObjectsTotal(0, 0, OBJ_HLINE) - 1; i >= 0; i--)
   {
      string name = ObjectName(0, i, 0, OBJ_HLINE);
      if(StringFind(name, "SR_") == 0)
         ObjectDelete(0, name);
   }
}

//+------------------------------------------------------------------+
//| Inicialização do analisador                                       |
//+------------------------------------------------------------------+
bool CSRAnalyzer::Initialize(string symbol, ENUM_TIMEFRAMES timeframe, SSRConfig &config)
{
   m_symbol = symbol;
   m_timeframe = timeframe;
   m_config = config;
   
   // Redimensionar arrays
   if(ArrayResize(m_sr_points, m_config.max_active_levels) < 0)
   {
      if(m_logger) m_logger.LogError("CSRAnalyzer::Initialize", "Falha ao redimensionar array de pontos S&R");
      return false;
   }
   
   // Carregar dados históricos
   if(!LoadHistoricalData())
   {
      if(m_logger) m_logger.LogError("CSRAnalyzer::Initialize", "Falha ao carregar dados históricos");
      return false;
   }
   
   if(m_logger) m_logger.LogInfo("CSRAnalyzer inicializado para " + m_symbol + " em " + EnumToString(m_timeframe));
   
   return true;
}

//+------------------------------------------------------------------+
//| Análise principal de Suporte e Resistência                       |
//+------------------------------------------------------------------+
bool CSRAnalyzer::AnalyzeSupportResistance(void)
{
   datetime current_time = TimeCurrent();
   
   // Verificar se é necessário atualizar dados históricos
   if(current_time - m_last_analysis > 300) // 5 minutos
   {
      if(!LoadHistoricalData())
      {
         if(m_logger) m_logger.LogError("CSRAnalyzer::AnalyzeSupportResistance", "Falha ao atualizar dados históricos");
         return false;
      }
   }
   
   // Limpar pontos antigos e fracos
   RemoveOldPoints();
   RemoveWeakPoints();
   
   // Detectar pivôs históricos
   if(current_time - m_last_pivot_scan > 60) // 1 minuto
   {
      DetectHistoricalPivots();
      m_last_pivot_scan = current_time;
   }
   
   // Detectar números redondos
   if(m_config.use_round_numbers && current_time - m_last_round_scan > 300) // 5 minutos
   {
      DetectRoundNumbers();
      m_last_round_scan = current_time;
   }
   
   // Detectar Volume Profile (se habilitado)
   if(m_config.use_volume_profile)
   {
      DetectVolumeProfile();
   }
   
   // Detectar níveis de Fibonacci (se habilitado)
   if(m_config.use_fibonacci)
   {
      DetectFibonacciLevels();
   }
   
   // Calcular pesos temporais
   CalculateTemporalWeights();
   
   // Calcular valores de força
   CalculateStrengthValues();
   
   // Ordenar por força
   SortPointsByStrength();
   
   // Otimizar array
   OptimizePointsArray();
   
   m_last_analysis = current_time;
   
   // Log do resultado
   if(m_logger)
   {
      SSRPoint strongest_point;
      bool has_strongest = GetStrongestPoint(strongest_point);
      double strongest_price = has_strongest ? strongest_point.price : 0;
      int confirmations = has_strongest ? strongest_point.confirmations : 0;
      
      m_logger.LogSRAnalysis(m_total_points, strongest_price, confirmations);
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Detecção de pivôs históricos                                      |
//+------------------------------------------------------------------+
bool CSRAnalyzer::DetectHistoricalPivots(void)
{
   if(m_rates_total < 10)
      return false;
   
   int pivot_period = 5; // Períodos para detectar pivô
   int detected = 0;
   
   // Buscar pivôs de alta
   for(int i = pivot_period; i < m_rates_total - pivot_period; i++)
   {
      if(IsPivotHigh(i, pivot_period, pivot_period))
      {
         double strength = CalculatePivotStrength(i, true);
         if(AddSRPoint(m_rates[i].high, ORIGEM_PIVOT, m_rates[i].time, strength))
            detected++;
      }
   }
   
   // Buscar pivôs de baixa
   for(int i = pivot_period; i < m_rates_total - pivot_period; i++)
   {
      if(IsPivotLow(i, pivot_period, pivot_period))
      {
         double strength = CalculatePivotStrength(i, false);
         if(AddSRPoint(m_rates[i].low, ORIGEM_PIVOT, m_rates[i].time, strength))
            detected++;
      }
   }
   
   if(m_logger && detected > 0)
      m_logger.LogDebug("Detectados " + IntegerToString(detected) + " novos pivôs históricos");
   
   return detected > 0;
}

//+------------------------------------------------------------------+
//| Detecção de números redondos                                      |
//+------------------------------------------------------------------+
bool CSRAnalyzer::DetectRoundNumbers(void)
{
   if(m_rates_total < 1)
      return false;
   
   double current_price = m_rates[m_rates_total-1].close;
   double price_range = current_price * 0.10; // 10% de range
   
   int detected = 0;
   
   // Níveis de 100 em 100
   double base_100 = MathFloor(current_price / 100) * 100;
   for(double price = base_100 - price_range; price <= base_100 + price_range + 200; price += 100)
   {
      if(price > 0 && IsRoundNumber(price))
      {
         double strength = CalculateRoundNumberStrength(price);
         if(AddSRPoint(price, ORIGEM_REDONDO, TimeCurrent(), strength))
            detected++;
      }
   }
   
   // Níveis de 250 em 250
   double base_250 = MathFloor(current_price / 250) * 250;
   for(double price = base_250 - price_range; price <= base_250 + price_range + 500; price += 250)
   {
      if(price > 0)
      {
         double strength = CalculateRoundNumberStrength(price) * 1.5; // Números 250 são mais fortes
         if(AddSRPoint(price, ORIGEM_REDONDO, TimeCurrent(), strength))
            detected++;
      }
   }
   
   // Níveis de 500 em 500
   double base_500 = MathFloor(current_price / 500) * 500;
   for(double price = base_500 - price_range; price <= base_500 + price_range + 1000; price += 500)
   {
      if(price > 0)
      {
         double strength = CalculateRoundNumberStrength(price) * 2.0; // Números 500 são ainda mais fortes
         if(AddSRPoint(price, ORIGEM_REDONDO, TimeCurrent(), strength))
            detected++;
      }
   }
   
   if(m_logger && detected > 0)
      m_logger.LogDebug("Detectados " + IntegerToString(detected) + " números redondos");
   
   return detected > 0;
}

//+------------------------------------------------------------------+
//| Adicionar ponto S&R                                              |
//+------------------------------------------------------------------+
bool CSRAnalyzer::AddSRPoint(double price, ENUM_ORIGEM_SR origin, datetime timestamp, double initial_strength = 1.0)
{
   // Verificar se já existe ponto próximo
   if(ExistsNearbyPoint(price, m_config.touch_tolerance))
   {
      // Incrementar confirmações do ponto existente
      IncrementConfirmations(price, m_config.touch_tolerance, timestamp);
      return false; // Não é novo ponto
   }
   
   // Verificar se há espaço no array
   if(m_total_points >= m_config.max_active_levels)
   {
      // Remover pontos mais fracos para fazer espaço
      RemoveWeakPoints();
      if(m_total_points >= m_config.max_active_levels)
         return false;
   }
   
   // Validar o ponto
   if(!IsValidSRPoint(price, origin))
      return false;
   
   // Adicionar novo ponto
   SSRPoint new_point;
   new_point.price = price;
   new_point.origin = origin;
   new_point.strength = initial_strength;
   new_point.created_time = timestamp;
   new_point.confirmations = 1;
   new_point.last_confirmation = timestamp;
   new_point.temporal_weight = 1.0;
   new_point.importance = 1;
   new_point.touch_tolerance = m_config.touch_tolerance;
   new_point.timeframe_detected = m_timeframe;
   
   m_sr_points[m_total_points] = new_point;
   m_total_points++;
   
   return true;
}

//+------------------------------------------------------------------+
//| Carregar dados históricos                                         |
//+------------------------------------------------------------------+
bool CSRAnalyzer::LoadHistoricalData(void)
{
   datetime start_time = TimeCurrent() - (m_config.history_days * 24 * 3600);
   
   // Copiar dados históricos
   int copied = CopyRates(m_symbol, m_timeframe, start_time, TimeCurrent(), m_rates);
   
   if(copied <= 0)
   {
      if(m_logger) m_logger.LogError("CSRAnalyzer::LoadHistoricalData", 
                                    "Falha ao copiar dados: " + IntegerToString(GetLastError()));
      return false;
   }
   
   m_rates_total = copied;
   
   // Validar dados se validator disponível
   if(m_validator)
   {
      SValidationResult validation = m_validator.ValidateRatesData(m_rates, m_rates_total);
      if(!validation.is_valid)
      {
         if(m_logger) m_logger.LogError("CSRAnalyzer::LoadHistoricalData", 
                                       "Dados inválidos: " + validation.error_message);
         return false;
      }
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| MÉTODOS AUXILIARES PRIVADOS                                       |
//+------------------------------------------------------------------+

bool CSRAnalyzer::IsPivotHigh(int index, int left_bars = 2, int right_bars = 2)
{
   if(index < left_bars || index >= m_rates_total - right_bars)
      return false;
   
   double high = m_rates[index].high;
   
   // Verificar barras à esquerda
   for(int i = index - left_bars; i < index; i++)
   {
      if(m_rates[i].high >= high)
         return false;
   }
   
   // Verificar barras à direita
   for(int i = index + 1; i <= index + right_bars; i++)
   {
      if(m_rates[i].high >= high)
         return false;
   }
   
   return true;
}

bool CSRAnalyzer::IsPivotLow(int index, int left_bars = 2, int right_bars = 2)
{
   if(index < left_bars || index >= m_rates_total - right_bars)
      return false;
   
   double low = m_rates[index].low;
   
   // Verificar barras à esquerda
   for(int i = index - left_bars; i < index; i++)
   {
      if(m_rates[i].low <= low)
         return false;
   }
   
   // Verificar barras à direita
   for(int i = index + 1; i <= index + right_bars; i++)
   {
      if(m_rates[i].low <= low)
         return false;
   }
   
   return true;
}

bool CSRAnalyzer::IsRoundNumber(double price)
{
   // Verificar se é múltiplo de 100, 250, 500 ou 1000
   return (MathMod(price, 100) == 0);
}

void CSRAnalyzer::CalculateTemporalWeights(void)
{
   datetime current_time = TimeCurrent();
   
   for(int i = 0; i < m_total_points; i++)
   {
      double time_decay = CalculateTimeDecay(m_sr_points[i].created_time);
      double recent_bonus = CalculateRecentBonus(m_sr_points[i].last_confirmation);
      
      m_sr_points[i].temporal_weight = time_decay + recent_bonus;
   }
}

double CSRAnalyzer::CalculateTimeDecay(datetime point_time)
{
   datetime current_time = TimeCurrent();
   int days_old = (int)((current_time - point_time) / (24 * 3600));
   
   if(days_old <= 0) return 1.0;
   if(days_old >= 90) return 0.1; // Decaimento mínimo
   
   return 1.0 - (days_old / 90.0) * 0.9; // Decaimento linear
}

bool CSRAnalyzer::ExistsNearbyPoint(double price, double tolerance)
{
   for(int i = 0; i < m_total_points; i++)
   {
      if(MathAbs(m_sr_points[i].price - price) <= tolerance)
         return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Implementação das funções de consulta                             |
//+------------------------------------------------------------------+

void CSRAnalyzer::GetSRPoints(SSRPoint &points[])
{
   ArrayResize(points, m_total_points);
   for(int i = 0; i < m_total_points; i++)
   {
      points[i] = m_sr_points[i];
   }
}

bool CSRAnalyzer::GetStrongestPoint(SSRPoint &point)
{
   if(m_total_points == 0)
      return false;
   
   int strongest_index = 0;
   double max_strength = m_sr_points[0].strength;
   
   for(int i = 1; i < m_total_points; i++)
   {
      if(m_sr_points[i].strength > max_strength)
      {
         max_strength = m_sr_points[i].strength;
         strongest_index = i;
      }
   }
   
   point = m_sr_points[strongest_index];
   return true;
}

bool CSRAnalyzer::FindNearestPoint(double price, SSRPoint &point, double max_distance = 0)
{
   if(m_total_points == 0)
      return false;
   
   int nearest_index = -1;
   double min_distance = DBL_MAX;
   
   for(int i = 0; i < m_total_points; i++)
   {
      double distance = MathAbs(m_sr_points[i].price - price);
      if(distance < min_distance)
      {
         if(max_distance == 0 || distance <= max_distance)
         {
            min_distance = distance;
            nearest_index = i;
         }
      }
   }
   
   if(nearest_index >= 0)
   {
      point = m_sr_points[nearest_index];
      return true;
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Calcular bônus recente para pontos testados recentemente         |
//+------------------------------------------------------------------+
double CSRAnalyzer::CalculateRecentBonus(datetime point_time)
{
   datetime current_time = TimeCurrent();
   int hours_since = (int)((current_time - point_time) / 3600);
   
   if(hours_since <= 0) return 2.0;        // Testado agora
   if(hours_since <= 6) return 1.5;        // Testado nas últimas 6 horas
   if(hours_since <= 24) return 1.0;       // Testado nas últimas 24 horas
   if(hours_since <= 168) return 0.5;      // Testado na última semana
   
   return 0.0; // Muito antigo
}

//+------------------------------------------------------------------+
//| Remover pontos fracos baseado na força mínima                    |
//+------------------------------------------------------------------+
void CSRAnalyzer::RemoveWeakPoints(void)
{
   int write_index = 0;
   
   for(int i = 0; i < m_total_points; i++)
   {
      if(m_sr_points[i].strength >= m_config.min_strength)
      {
         if(write_index != i)
            m_sr_points[write_index] = m_sr_points[i];
         write_index++;
      }
   }
   
   m_total_points = write_index;
}

//+------------------------------------------------------------------+
//| Remover pontos muito antigos                                     |
//+------------------------------------------------------------------+
void CSRAnalyzer::RemoveOldPoints(void)
{
   datetime current_time = TimeCurrent();
   datetime cutoff_time = current_time - (m_config.history_days * 24 * 3600);
   
   int write_index = 0;
   
   for(int i = 0; i < m_total_points; i++)
   {
      if(m_sr_points[i].created_time >= cutoff_time)
      {
         if(write_index != i)
            m_sr_points[write_index] = m_sr_points[i];
         write_index++;
      }
   }
   
   m_total_points = write_index;
}

//+------------------------------------------------------------------+
//| Detectar perfil de volume (implementação básica)                 |
//+------------------------------------------------------------------+
bool CSRAnalyzer::DetectVolumeProfile(void)
{
   // Implementação básica - pode ser expandida futuramente
   // Por enquanto apenas retorna true para não causar erro
   if(m_logger) m_logger.LogDebug("DetectVolumeProfile: Análise de volume profile não implementada ainda");
   return true;
}

//+------------------------------------------------------------------+
//| Detectar níveis de Fibonacci (implementação básica)              |
//+------------------------------------------------------------------+
bool CSRAnalyzer::DetectFibonacciLevels(void)
{
   // Implementação básica - pode ser expandida futuramente
   // Por enquanto apenas retorna true para não causar erro
   if(m_logger) m_logger.LogDebug("DetectFibonacciLevels: Análise de níveis Fibonacci não implementada ainda");
   return true;
}

//+------------------------------------------------------------------+
//| Calcular valores de força dos pontos S&R                         |
//+------------------------------------------------------------------+
void CSRAnalyzer::CalculateStrengthValues(void)
{
   for(int i = 0; i < m_total_points; i++)
   {
      // Fórmula de força combinada:
      // Força = (Confirmações * 2.0) + Peso Temporal + Bônus por Origem
      
      double base_strength = m_sr_points[i].confirmations * 2.0;
      double temporal_contribution = m_sr_points[i].temporal_weight;
      double origin_bonus = 0.0;
      
      // Bônus baseado na origem do ponto
      switch(m_sr_points[i].origin)
      {
         case ORIGEM_PIVOT:
            origin_bonus = 1.5;
            break;
         case ORIGEM_REDONDO:
            origin_bonus = 1.0;
            break;
         case ORIGEM_VOLUME:
            origin_bonus = 2.0;
            break;
         case ORIGEM_FIBONACCI:
            origin_bonus = 1.5;
            break;
         default:
            origin_bonus = 0.5;
            break;
      }
      
      // Calcular força final
      m_sr_points[i].strength = base_strength + temporal_contribution + origin_bonus;
      
      // Aplicar limitadores
      if(m_sr_points[i].strength < 0.1)
         m_sr_points[i].strength = 0.1;
      else if(m_sr_points[i].strength > 10.0)
         m_sr_points[i].strength = 10.0;
   }
}

//+------------------------------------------------------------------+
//| Ordenar pontos por força (do mais forte para o mais fraco)       |
//+------------------------------------------------------------------+
void CSRAnalyzer::SortPointsByStrength(void)
{
   if(m_total_points <= 1)
      return;
   
   // Implementação de bubble sort simples (adequado para poucos pontos)
   for(int i = 0; i < m_total_points - 1; i++)
   {
      for(int j = 0; j < m_total_points - i - 1; j++)
      {
         if(m_sr_points[j].strength < m_sr_points[j + 1].strength)
         {
            // Trocar elementos
            SSRPoint temp = m_sr_points[j];
            m_sr_points[j] = m_sr_points[j + 1];
            m_sr_points[j + 1] = temp;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Implementações de funções auxiliares que estavam faltando        |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Calcular força de um pivô                                        |
//+------------------------------------------------------------------+
double CSRAnalyzer::CalculatePivotStrength(int index, bool is_high)
{
   if(index < 5 || index >= m_rates_total - 5)
      return 1.0;
   
   double strength = 1.0;
   double pivot_price = is_high ? m_rates[index].high : m_rates[index].low;
   
   // Força baseada no volume se disponível
   if(m_rates[index].tick_volume > 0)
   {
      double avg_volume = 0;
      for(int i = index - 5; i <= index + 5; i++)
      {
         if(i >= 0 && i < m_rates_total)
            avg_volume += (double)m_rates[i].tick_volume;
      }
      avg_volume /= 11;
      
              if((double)m_rates[index].tick_volume > avg_volume * 1.5)
         strength += 0.5;
   }
   
   // Força baseada na altura da vela
   double candle_size = MathAbs(m_rates[index].high - m_rates[index].low);
   double avg_candle = 0;
   for(int i = index - 10; i < index; i++)
   {
      if(i >= 0)
         avg_candle += MathAbs(m_rates[i].high - m_rates[i].low);
   }
   avg_candle /= 10;
   
   if(candle_size > avg_candle * 1.5)
      strength += 0.3;
   
   return MathMax(1.0, MathMin(strength, 3.0));
}

//+------------------------------------------------------------------+
//| Calcular força de número redondo                                 |
//+------------------------------------------------------------------+
double CSRAnalyzer::CalculateRoundNumberStrength(double price)
{
   double strength = 1.0;
   
   // Verificar múltiplos
   if(MathMod(price, 1000) == 0) strength = 3.0;        // 1000, 2000, etc
   else if(MathMod(price, 500) == 0) strength = 2.5;    // 500, 1500, etc
   else if(MathMod(price, 250) == 0) strength = 2.0;    // 250, 750, etc
   else if(MathMod(price, 100) == 0) strength = 1.5;    // 100, 200, etc
   else if(MathMod(price, 50) == 0) strength = 1.2;     // 50, 150, etc
   
   return strength;
}

//+------------------------------------------------------------------+
//| Incrementar confirmações de um ponto existente                   |
//+------------------------------------------------------------------+
void CSRAnalyzer::IncrementConfirmations(double price, double tolerance, datetime timestamp)
{
   for(int i = 0; i < m_total_points; i++)
   {
      if(MathAbs(m_sr_points[i].price - price) <= tolerance)
      {
         m_sr_points[i].confirmations++;
         m_sr_points[i].last_confirmation = timestamp;
         
         // Aumentar força baseado nas confirmações
         if(m_sr_points[i].confirmations >= 3)
            m_sr_points[i].strength += 0.5;
         
         return;
      }
   }
}

//+------------------------------------------------------------------+
//| Validar se um ponto S&R é válido                                |
//+------------------------------------------------------------------+
bool CSRAnalyzer::IsValidSRPoint(double price, ENUM_ORIGEM_SR origin)
{
   // Verificar se o preço é válido
   if(price <= 0)
      return false;
      
   // Verificar se não está muito longe do preço atual
   double current_price = m_rates_total > 0 ? m_rates[m_rates_total-1].close : 0;
   if(current_price > 0)
   {
      double distance = MathAbs(price - current_price);
      double max_distance = current_price * 0.20; // 20% de distância máxima
      
      if(distance > max_distance)
         return false;
   }
   
   // Validações específicas por origem
   switch(origin)
   {
      case ORIGEM_PIVOT:
         return true; // Pivôs são sempre válidos se chegaram até aqui
         
      case ORIGEM_REDONDO:
         return IsRoundNumber(price);
         
      case ORIGEM_VOLUME:
      case ORIGEM_FIBONACCI:
         return true; // Por enquanto aceitar sempre
         
      default:
         return false;
   }
}

//+------------------------------------------------------------------+
//| Confluir pivôs próximos (<50 pontos) em um só                   |
//+------------------------------------------------------------------+
void CSRAnalyzer::ConfluirPivosProximos()
{
   if(m_total_points < 2) return;
   const double LIMIAR = 50.0;
   bool merged = true;
   while(merged) {
      merged = false;
      for(int i = 0; i < m_total_points-1; i++) {
         for(int j = i+1; j < m_total_points; j++) {
            if(MathAbs(m_sr_points[i].price - m_sr_points[j].price) < LIMIAR) {
               // Merge: média dos preços, soma força/confirmações, timestamp mais recente
               double new_price = (m_sr_points[i].price + m_sr_points[j].price) / 2.0;
               double new_strength = m_sr_points[i].strength + m_sr_points[j].strength;
               int new_conf = m_sr_points[i].confirmations + m_sr_points[j].confirmations;
               datetime new_time = MathMax(m_sr_points[i].last_confirmation, m_sr_points[j].last_confirmation);
               ENUM_ORIGEM_SR new_origin = m_sr_points[i].origin;
               // Atualiza i
               m_sr_points[i].price = new_price;
               m_sr_points[i].strength = new_strength;
               m_sr_points[i].confirmations = new_conf;
               m_sr_points[i].last_confirmation = new_time;
               m_sr_points[i].origin = new_origin;
               // Remove j
               for(int k = j; k < m_total_points-1; k++)
                  m_sr_points[k] = m_sr_points[k+1];
               m_total_points--;
               merged = true;
               break;
            }
         }
         if(merged) break;
      }
   }
}

//+------------------------------------------------------------------+
//| Desenhar níveis S&R no gráfico (linhas por baixo do painel)     |
//+------------------------------------------------------------------+
bool CSRAnalyzer::DrawSRLevels(void)
{
   // Remove linhas antigas primeiro
   for(int i = ObjectsTotal(0, 0, OBJ_HLINE) - 1; i >= 0; i--)
   {
      string name = ObjectName(0, i, 0, OBJ_HLINE);
      if(StringFind(name, "SR_") == 0 || StringFind(name, "ENTRY_") == 0)
         ObjectDelete(0, name);
   }

   // Confluir pivôs próximos
   ConfluirPivosProximos();

   // Obter preço atual para determinar pontos de entrada
   double current_price = 0;
   if(m_rates_total > 0)
      current_price = m_rates[m_rates_total-1].close;

   // Encontrar o pivô mais próximo acima e abaixo do preço
   int idx_acima = -1, idx_abaixo = -1;
   double min_dist_acima = DBL_MAX, min_dist_abaixo = DBL_MAX;
   for(int i = 0; i < m_total_points; i++) {
      double d = m_sr_points[i].price - current_price;
      if(d > 0 && d < min_dist_acima) { min_dist_acima = d; idx_acima = i; }
      if(d < 0 && -d < min_dist_abaixo) { min_dist_abaixo = -d; idx_abaixo = i; }
   }

   for(int i = 0; i < m_total_points && i < 25; i++) // Limitar a 25 níveis visíveis
   {
      double point_price = m_sr_points[i].price;
      bool is_entry_candidate = (i == idx_acima || i == idx_abaixo);

      string line_name = (is_entry_candidate ? "ENTRY_" : "SR_") + IntegerToString(i) + "_" + DoubleToString(point_price, 5);

      if(ObjectCreate(0, line_name, OBJ_HLINE, 0, 0, point_price))
      {
         // Definir cor baseada no tipo
         color line_color;
         if(is_entry_candidate)
         {
            line_color = clrLime; // Verde para pontos de entrada potenciais
         }
         else
         {
            line_color = GetOriginColor(m_sr_points[i].origin);
         }

         // Definir espessura baseada na força (mas limitada)
         int line_width = is_entry_candidate ? 2 : MathMin(3, MathMax(1, (int)(m_sr_points[i].strength / 2.0)));

         // Definir estilo
         int line_style = is_entry_candidate ? STYLE_SOLID : GetOriginLineStyle(m_sr_points[i].origin);

         ObjectSetInteger(0, line_name, OBJPROP_COLOR, line_color);
         ObjectSetInteger(0, line_name, OBJPROP_STYLE, line_style);
         ObjectSetInteger(0, line_name, OBJPROP_WIDTH, line_width);
         ObjectSetInteger(0, line_name, OBJPROP_BACK, true); // Sempre por baixo do painel

         // Texto descritivo
         string description = "";
         if(is_entry_candidate)
         {
            description = "🎯 ENTRADA " + OriginToString(m_sr_points[i].origin);
            description += " | F:" + DoubleToString(m_sr_points[i].strength, 1);
         }
         else
         {
            description = OriginToString(m_sr_points[i].origin);
            description += " | Força:" + DoubleToString(m_sr_points[i].strength, 1);
            description += " | Conf:" + IntegerToString(m_sr_points[i].confirmations);
         }

         ObjectSetString(0, line_name, OBJPROP_TEXT, description);
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Verificar se um ponto é candidato a entrada                     |
//+------------------------------------------------------------------+
bool CSRAnalyzer::IsEntryCandidate(double point_price, double current_price)
{
   if(current_price <= 0 || point_price <= 0)
      return false;
   
   // Calcular distância do preço atual
   double distance = MathAbs(current_price - point_price);
   double distance_percent = (distance / current_price) * 100.0;
   
   // Considerar candidato se:
   // 1. Está dentro de uma distância razoável (0.1% a 2.0%)
   // 2. Não está exatamente no preço atual (precisa de movimento)
   double min_distance_percent = 0.05;  // 0.05%
   double max_distance_percent = 1.5;   // 1.5%
   
   return (distance_percent >= min_distance_percent && distance_percent <= max_distance_percent);
}

//+------------------------------------------------------------------+
//| Atualizar display dos níveis S&R                                |
//+------------------------------------------------------------------+
bool CSRAnalyzer::UpdateSRDisplay(void)
{
   // Por enquanto apenas chama DrawSRLevels
   return DrawSRLevels();
}

//+------------------------------------------------------------------+
//| Funções utilitárias para visualização                           |
//+------------------------------------------------------------------+
string CSRAnalyzer::OriginToString(ENUM_ORIGEM_SR origin)
{
   switch(origin)
   {
      case ORIGEM_PIVOT:     return "Pivô";
      case ORIGEM_REDONDO:   return "Redondo";
      case ORIGEM_VOLUME:    return "Volume";
      case ORIGEM_FIBONACCI: return "Fibonacci";
      default:               return "Desconhecido";
   }
}

color CSRAnalyzer::GetOriginColor(ENUM_ORIGEM_SR origin)
{
   switch(origin)
   {
      case ORIGEM_PIVOT:     return clrSteelBlue;    // Azul mais suave
      case ORIGEM_REDONDO:   return clrGoldenrod;    // Dourado mais suave
      case ORIGEM_VOLUME:    return clrMediumSeaGreen; // Verde mais suave
      case ORIGEM_FIBONACCI: return clrMediumOrchid;  // Magenta mais suave
      default:               return clrDarkGray;
   }
}

int CSRAnalyzer::GetOriginLineStyle(ENUM_ORIGEM_SR origin)
{
   switch(origin)
   {
      case ORIGEM_PIVOT:     return STYLE_SOLID;
      case ORIGEM_REDONDO:   return STYLE_DASH;
      case ORIGEM_VOLUME:    return STYLE_DOT;
      case ORIGEM_FIBONACCI: return STYLE_DASHDOT;
      default:               return STYLE_SOLID;
   }
}

//+------------------------------------------------------------------+
//| Otimizar array de pontos S&R                                    |
//+------------------------------------------------------------------+
void CSRAnalyzer::OptimizePointsArray(void)
{
   // Primeiro, compactar o array removendo espaços vazios  
   CompactArray();
   
   // Se ainda há muitos pontos, manter apenas os mais fortes
   if(m_total_points > m_config.max_active_levels)
   {
      // Já está ordenado por força, então manter apenas os primeiros
      m_total_points = m_config.max_active_levels;
   }
   
   // Remover pontos que devem ser removidos
   int write_index = 0;
   for(int i = 0; i < m_total_points; i++)
   {
      if(!ShouldRemovePoint(i))
      {
         if(write_index != i)
            m_sr_points[write_index] = m_sr_points[i];
         write_index++;
      }
   }
   m_total_points = write_index;
}

//+------------------------------------------------------------------+
//| Compactar array removendo elementos inválidos                   |
//+------------------------------------------------------------------+
void CSRAnalyzer::CompactArray(void)
{
   int write_index = 0;
   
   for(int i = 0; i < m_total_points; i++)
   {
      // Verificar se o ponto é válido (preço > 0 e força > 0)
      if(m_sr_points[i].price > 0 && m_sr_points[i].strength > 0)
      {
         if(write_index != i)
            m_sr_points[write_index] = m_sr_points[i];
         write_index++;
      }
   }
   
   m_total_points = write_index;
}

//+------------------------------------------------------------------+
//| Verificar se um ponto deve ser removido                         |
//+------------------------------------------------------------------+
bool CSRAnalyzer::ShouldRemovePoint(int index)
{
   if(index < 0 || index >= m_total_points)
      return true;
      
   // Remover se força muito baixa
   if(m_sr_points[index].strength < m_config.min_strength * 0.5)
      return true;
      
   // Remover se muito antigo e sem confirmações recentes
   datetime current_time = TimeCurrent();
   int days_old = (int)((current_time - m_sr_points[index].created_time) / (24 * 3600));
   int hours_since_confirmation = (int)((current_time - m_sr_points[index].last_confirmation) / 3600);
   
   if(days_old > m_config.history_days && hours_since_confirmation > 168) // 1 semana sem confirmação
      return true;
      
   // Remover se preço inválido
   if(m_sr_points[index].price <= 0)
      return true;
      
   return false;
} 