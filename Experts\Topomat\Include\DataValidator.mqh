//+------------------------------------------------------------------+
//|                                                 DataValidator.mqh |
//|                        Copyright 2024, Topomat Trading Systems   |
//|                                             https://topomat.com   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Topomat Trading Systems"
#property link      "https://topomat.com"

//--- Constantes de erro personalizadas para compatibilidade
#define ERR_NO_CONNECTION                   6
#define ERR_DLL_CALLS_NOT_ALLOWED          14
#define ERR_TRADE_TIMEOUT                  128
#define ERR_INVALID_ACCOUNT                64
#define ERR_NOT_ENOUGH_MONEY               134
#define ERR_MARKET_CLOSED                  132
#define ERR_INVALID_PRICE                  129
#define ERR_INVALID_VOLUME                 131
#define ERR_INVALID_ORDER_TYPE             147
#define ERR_INVALID_STOPS                  130

//--- Usar constantes MT5 nativas para evitar redefinição
// ERR_TRADE_DISABLED já definido no MT5
// ERR_NOT_ENOUGH_MEMORY já definido no MT5

//--- Constantes matemáticas
#ifndef INFINITY
#define INFINITY                           1e308
#endif

//+------------------------------------------------------------------+
//| SISTEMA DE VALIDAÇÃO RIGOROSA DE DADOS MT5                       |
//+------------------------------------------------------------------+

//--- Estrutura para resultado de validação
struct SValidationResult
{
   bool              is_valid;          // Resultado da validação
   string            error_message;     // Mensagem de erro se inválido
   int               error_code;        // Código de erro MT5
};

//+------------------------------------------------------------------+
//| Classe para validação de dados MT5                               |
//+------------------------------------------------------------------+

class CDataValidator
{
private:
   string            m_symbol;              // Símbolo atual
   datetime          m_last_connection_check; // Última verificação de conexão
   int               m_connection_check_interval; // Intervalo de verificação (segundos)
   
public:
   //--- Construtor
                     CDataValidator(string symbol = "");
   
   //--- Configuração
   void              SetSymbol(string symbol) { m_symbol = symbol; }
   void              SetConnectionCheckInterval(int seconds) { m_connection_check_interval = seconds; }
   
   //--- Validação de conectividade
   SValidationResult ValidateConnection(void);
   SValidationResult ValidateTerminalState(void);
   SValidationResult ValidateAccount(void);
   
   //--- Validação de dados de mercado
   SValidationResult ValidateSymbolInfo(string symbol = "");
   SValidationResult ValidateTickData(const MqlTick &tick);
   SValidationResult ValidateRatesData(const MqlRates &rates[], int count);
   SValidationResult ValidateBookData(const MqlBookInfo &book[], int count);
   
   //--- Validação de dados de trading
   SValidationResult ValidateOrderData(ENUM_ORDER_TYPE type, double volume, double price, 
                                      double sl = 0, double tp = 0);
   SValidationResult ValidatePositionData(ulong ticket);
   SValidationResult ValidateDealData(ulong ticket);
   
   //--- Validação de indicadores técnicos
   SValidationResult ValidateIndicatorData(const double &data[], int count, string indicator_name);
   SValidationResult ValidateATRData(const double &atr_values[], int count);
   SValidationResult ValidateMAData(const double &ma_values[], int count);
   
   //--- Validação de dados históricos
   SValidationResult ValidateHistoricalData(const MqlRates &rates[], int count, 
                                           datetime start_time, datetime end_time);
   
   //--- Validação de configurações
   SValidationResult ValidateEASettings(int magic_number, double risk_percent, 
                                       int max_positions, double min_volume, double max_volume);
   
   //--- Métodos auxiliares
   bool              IsMarketOpen(string symbol = "");
   bool              IsTradingAllowed(void);
   double            GetSpread(string symbol = "");
   datetime          GetServerTime(void);
   
private:
   //--- Métodos auxiliares privados
   SValidationResult CreateValidResult(void);
   SValidationResult CreateErrorResult(string message, int code = 0);
   bool              IsValidPrice(double price, string symbol = "");
   bool              IsValidVolume(double volume, string symbol = "");
   bool              IsValidTime(datetime time);
   string            GetLastErrorDescription(void);
   bool              CheckDataIntegrity(const double &data[], int count);
   bool              CheckTimeSequence(const MqlRates &rates[], int count);
};

//+------------------------------------------------------------------+
//| Construtor                                                        |
//+------------------------------------------------------------------+
CDataValidator::CDataValidator(string symbol = "")
{
   m_symbol = (symbol == "") ? _Symbol : symbol;
   m_last_connection_check = 0;
   m_connection_check_interval = 30; // 30 segundos
}

//+------------------------------------------------------------------+
//| Validação de conexão                                              |
//+------------------------------------------------------------------+
SValidationResult CDataValidator::ValidateConnection(void)
{
   datetime current_time = TimeCurrent();
   
   // Verificar se é necessário checar novamente
   if(current_time - m_last_connection_check < m_connection_check_interval)
   {
      return CreateValidResult();
   }
   
   m_last_connection_check = current_time;
   
   // Verificar conexão com servidor
   if(!TerminalInfoInteger(TERMINAL_CONNECTED))
   {
      return CreateErrorResult("Conexão com servidor perdida", ERR_NO_CONNECTION);
   }
   
   // Verificar se DLLs estão permitidas
   if(!TerminalInfoInteger(TERMINAL_DLLS_ALLOWED))
   {
      return CreateErrorResult("DLLs não permitidas", ERR_DLL_CALLS_NOT_ALLOWED);
   }
   
   // Verificar trading automático
   if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
   {
      return CreateErrorResult("Trading automático desabilitado", ERR_TRADE_DISABLED);
   }
   
   return CreateValidResult();
}

//+------------------------------------------------------------------+
//| Validação do estado do terminal                                   |
//+------------------------------------------------------------------+
SValidationResult CDataValidator::ValidateTerminalState(void)
{
   // Verificar se terminal está conectado
   if(!TerminalInfoInteger(TERMINAL_CONNECTED))
   {
      return CreateErrorResult("Terminal desconectado", ERR_TRADE_TIMEOUT);
   }
   
   // Verificar memória disponível
   long memory_used = TerminalInfoInteger(TERMINAL_MEMORY_USED);
   long memory_total = TerminalInfoInteger(TERMINAL_MEMORY_TOTAL);
   
   if(memory_total > 0 && (double)memory_used / memory_total > 0.95) // > 95%
   {
      return CreateErrorResult("Memória insuficiente", ERR_NOT_ENOUGH_MEMORY);
   }
   
   return CreateValidResult();
}

//+------------------------------------------------------------------+
//| Validação da conta                                                |
//+------------------------------------------------------------------+
SValidationResult CDataValidator::ValidateAccount(void)
{
   // Verificar se conta é válida
   if(AccountInfoInteger(ACCOUNT_LOGIN) <= 0)
   {
      return CreateErrorResult("Conta inválida", ERR_INVALID_ACCOUNT);
   }
   
   // Verificar se trading é permitido
   if(!AccountInfoInteger(ACCOUNT_TRADE_ALLOWED))
   {
      return CreateErrorResult("Trading não permitido para esta conta", ERR_TRADE_DISABLED);
   }
   
   // Verificar margem livre
   double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
   double margin_level = AccountInfoDouble(ACCOUNT_MARGIN_LEVEL);
   
   if(free_margin <= 0)
   {
      return CreateErrorResult("Margem livre insuficiente", ERR_NOT_ENOUGH_MONEY);
   }
   
   if(margin_level > 0 && margin_level < 100) // Margin call
   {
      return CreateErrorResult("Margin call ativo", ERR_NOT_ENOUGH_MONEY);
   }
   
   return CreateValidResult();
}

//+------------------------------------------------------------------+
//| Validação de informações do símbolo                               |
//+------------------------------------------------------------------+
SValidationResult CDataValidator::ValidateSymbolInfo(string symbol = "")
{
   if(symbol == "") symbol = m_symbol;
   
   // Verificar se símbolo existe
   if(!SymbolInfoInteger(symbol, SYMBOL_SELECT))
   {
      if(!SymbolSelect(symbol, true))
      {
         return CreateErrorResult("Símbolo " + symbol + " não encontrado", ERR_MARKET_CLOSED);
      }
   }
   
   // Verificar se trading é permitido para o símbolo
   if(!SymbolInfoInteger(symbol, SYMBOL_TRADE_MODE))
   {
      return CreateErrorResult("Trading desabilitado para " + symbol, ERR_TRADE_DISABLED);
   }
   
   // Verificar dados essenciais do símbolo
   double bid = SymbolInfoDouble(symbol, SYMBOL_BID);
   double ask = SymbolInfoDouble(symbol, SYMBOL_ASK);
   
   if(bid <= 0 || ask <= 0)
   {
      return CreateErrorResult("Preços inválidos para " + symbol, ERR_INVALID_PRICE);
   }
   
   if(ask <= bid) // Spread negativo impossível
   {
      return CreateErrorResult("Spread inválido para " + symbol, ERR_INVALID_PRICE);
   }
   
   // Verificar volume mínimo/máximo
   double volume_min = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double volume_max = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
   
   if(volume_min <= 0 || volume_max <= 0 || volume_min > volume_max)
   {
      return CreateErrorResult("Configurações de volume inválidas para " + symbol, ERR_INVALID_VOLUME);
   }
   
   return CreateValidResult();
}

//+------------------------------------------------------------------+
//| Validação de dados de tick                                        |
//+------------------------------------------------------------------+
SValidationResult CDataValidator::ValidateTickData(const MqlTick &tick)
{
   // Verificar se tick é válido
   if(tick.time <= 0)
   {
      return CreateErrorResult("Tempo do tick inválido", ERR_INVALID_PARAMETER);
   }
   
   // Verificar preços
   if(tick.bid <= 0 || tick.ask <= 0)
   {
      return CreateErrorResult("Preços do tick inválidos", ERR_INVALID_PRICE);
   }
   
   if(tick.ask <= tick.bid)
   {
      return CreateErrorResult("Spread do tick inválido", ERR_INVALID_PRICE);
   }
   
   // Verificar se tick não é muito antigo (> 1 minuto)
   datetime current_time = TimeCurrent();
   if(current_time - tick.time > 60)
   {
      return CreateErrorResult("Tick muito antigo", ERR_INVALID_PARAMETER);
   }
   
   // Verificar volume (se disponível)
   if(tick.volume_real < 0)
   {
      return CreateErrorResult("Volume do tick inválido", ERR_INVALID_PARAMETER);
   }
   
   return CreateValidResult();
}

//+------------------------------------------------------------------+
//| Validação de dados de rates                                       |
//+------------------------------------------------------------------+
SValidationResult CDataValidator::ValidateRatesData(const MqlRates &rates[], int count)
{
   if(count <= 0)
   {
      return CreateErrorResult("Contagem de rates inválida", ERR_INVALID_PARAMETER);
   }
   
   // Verificar integridade da sequência de tempo
   if(!CheckTimeSequence(rates, count))
   {
      return CreateErrorResult("Sequência temporal dos rates inválida", ERR_INVALID_PARAMETER);
   }
   
   // Verificar cada rate
   for(int i = 0; i < count; i++)
   {
      // Verificar OHLC
      if(rates[i].open <= 0 || rates[i].high <= 0 || 
         rates[i].low <= 0 || rates[i].close <= 0)
      {
         return CreateErrorResult("Preços OHLC inválidos no índice " + IntegerToString(i), ERR_INVALID_PRICE);
      }
      
      // Verificar lógica OHLC
      if(rates[i].high < rates[i].low ||
         rates[i].high < rates[i].open ||
         rates[i].high < rates[i].close ||
         rates[i].low > rates[i].open ||
         rates[i].low > rates[i].close)
      {
         return CreateErrorResult("Lógica OHLC inválida no índice " + IntegerToString(i), ERR_INVALID_PRICE);
      }
      
      // Verificar volume
      if(rates[i].tick_volume < 0 || rates[i].real_volume < 0)
      {
         return CreateErrorResult("Volume inválido no índice " + IntegerToString(i), ERR_INVALID_PARAMETER);
      }
   }
   
   return CreateValidResult();
}

//+------------------------------------------------------------------+
//| Validação de dados de ordem                                       |
//+------------------------------------------------------------------+
SValidationResult CDataValidator::ValidateOrderData(ENUM_ORDER_TYPE type, double volume, double price, 
                                                   double sl = 0, double tp = 0)
{
   string symbol = m_symbol;
   
   // Validar tipo de ordem
   if(type < ORDER_TYPE_BUY || type > ORDER_TYPE_SELL_STOP_LIMIT)
   {
      return CreateErrorResult("Tipo de ordem inválido", ERR_INVALID_ORDER_TYPE);
   }
   
   // Validar volume
   if(!IsValidVolume(volume, symbol))
   {
      return CreateErrorResult("Volume inválido: " + DoubleToString(volume, 2), ERR_INVALID_VOLUME);
   }
   
   // Validar preço
   if(!IsValidPrice(price, symbol))
   {
      return CreateErrorResult("Preço inválido: " + DoubleToString(price, _Digits), ERR_INVALID_PRICE);
   }
   
   // Validar stop loss
   if(sl > 0 && !IsValidPrice(sl, symbol))
   {
      return CreateErrorResult("Stop Loss inválido: " + DoubleToString(sl, _Digits), ERR_INVALID_STOPS);
   }
   
   // Validar take profit
   if(tp > 0 && !IsValidPrice(tp, symbol))
   {
      return CreateErrorResult("Take Profit inválido: " + DoubleToString(tp, _Digits), ERR_INVALID_STOPS);
   }
   
   // Validar lógica SL/TP para ordens de compra
   if(type == ORDER_TYPE_BUY || type == ORDER_TYPE_BUY_LIMIT || type == ORDER_TYPE_BUY_STOP)
   {
      if(sl > 0 && sl >= price)
      {
         return CreateErrorResult("Stop Loss deve ser menor que preço para compra", ERR_INVALID_STOPS);
      }
      if(tp > 0 && tp <= price)
      {
         return CreateErrorResult("Take Profit deve ser maior que preço para compra", ERR_INVALID_STOPS);
      }
   }
   
   // Validar lógica SL/TP para ordens de venda
   if(type == ORDER_TYPE_SELL || type == ORDER_TYPE_SELL_LIMIT || type == ORDER_TYPE_SELL_STOP)
   {
      if(sl > 0 && sl <= price)
      {
         return CreateErrorResult("Stop Loss deve ser maior que preço para venda", ERR_INVALID_STOPS);
      }
      if(tp > 0 && tp >= price)
      {
         return CreateErrorResult("Take Profit deve ser menor que preço para venda", ERR_INVALID_STOPS);
      }
   }
   
   return CreateValidResult();
}

//+------------------------------------------------------------------+
//| Validação de dados de indicador                                   |
//+------------------------------------------------------------------+
SValidationResult CDataValidator::ValidateIndicatorData(const double &data[], int count, string indicator_name)
{
   if(count <= 0)
   {
      return CreateErrorResult("Contagem de dados do indicador inválida", ERR_INVALID_PARAMETER);
   }
   
   if(!CheckDataIntegrity(data, count))
   {
      return CreateErrorResult("Dados do indicador " + indicator_name + " corrompidos", ERR_INVALID_PARAMETER);
   }
   
   // Verificar se não há muitos valores EMPTY_VALUE
   int empty_count = 0;
   for(int i = 0; i < count; i++)
   {
      if(data[i] == EMPTY_VALUE || data[i] == DBL_MAX)
         empty_count++;
   }
   
   // Se mais de 50% dos dados estão vazios, considerar inválido
   if((double)empty_count / count > 0.5)
   {
      return CreateErrorResult("Muitos valores vazios no indicador " + indicator_name, ERR_INVALID_PARAMETER);
   }
   
   return CreateValidResult();
}

//+------------------------------------------------------------------+
//| MÉTODOS AUXILIARES PRIVADOS                                       |
//+------------------------------------------------------------------+

SValidationResult CDataValidator::CreateValidResult(void)
{
   SValidationResult result;
   result.is_valid = true;
   result.error_message = "";
   result.error_code = 0;
   return result;
}

SValidationResult CDataValidator::CreateErrorResult(string message, int code = 0)
{
   SValidationResult result;
   result.is_valid = false;
   result.error_message = message;
   result.error_code = code;
   return result;
}

bool CDataValidator::IsValidPrice(double price, string symbol = "")
{
   if(symbol == "") symbol = m_symbol;
   
   if(price <= 0) return false;
   
   // Verificar se preço está dentro dos limites do símbolo
   double price_min = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_SIZE);
   if(price_min > 0)
   {
      // Verificar se preço é múltiplo do tick size
      double remainder = MathMod(price, price_min);
      if(MathAbs(remainder) > price_min * 0.1) // Tolerância de 10%
         return false;
   }
   
   return true;
}

bool CDataValidator::IsValidVolume(double volume, string symbol = "")
{
   if(symbol == "") symbol = m_symbol;
   
   if(volume <= 0) return false;
   
   double volume_min = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double volume_max = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
   double volume_step = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
   
   if(volume < volume_min || volume > volume_max)
      return false;
   
   if(volume_step > 0)
   {
      double remainder = MathMod(volume - volume_min, volume_step);
      if(MathAbs(remainder) > volume_step * 0.1) // Tolerância de 10%
         return false;
   }
   
   return true;
}

bool CDataValidator::CheckDataIntegrity(const double &data[], int count)
{
   if(count <= 0) return false;
   
   int nan_count = 0;
   int inf_count = 0;
   
   for(int i = 0; i < count; i++)
   {
      if(MathIsValidNumber(data[i]))
         continue;
         
      if(data[i] != data[i]) // NaN check
         nan_count++;
      else if(MathAbs(data[i]) == INFINITY)
         inf_count++;
   }
   
   // Se mais de 10% dos dados são NaN ou infinito, considerar corrompido
   return (nan_count + inf_count) < (count * 0.1);
}

bool CDataValidator::CheckTimeSequence(const MqlRates &rates[], int count)
{
   if(count <= 1) return true;
   bool crescente = true;
   bool decrescente = true;
   for(int i = 1; i < count; i++)
   {
      if(rates[i].time <= rates[i-1].time)
         crescente = false;
      if(rates[i].time >= rates[i-1].time)
         decrescente = false;
   }
   // Aceita se for estritamente crescente OU estritamente decrescente
   return crescente || decrescente;
}

bool CDataValidator::IsMarketOpen(string symbol = "")
{
   if(symbol == "") symbol = m_symbol;
   
   return SymbolInfoInteger(symbol, SYMBOL_TRADE_CALC_MODE) != SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE;
}

bool CDataValidator::IsTradingAllowed(void)
{
   return TerminalInfoInteger(TERMINAL_TRADE_ALLOWED) && 
          AccountInfoInteger(ACCOUNT_TRADE_ALLOWED);
}

//+------------------------------------------------------------------+
//| INSTÂNCIA GLOBAL DO VALIDADOR                                     |
//+------------------------------------------------------------------+

CDataValidator g_data_validator; 