//+------------------------------------------------------------------+
//|                                          AdvancedMLSystem.mqh    |
//|                   Sistema ML Avançado para Ultra Scalper        |
//|                   Rede Neural + Ensemble + Feature Engineering  |
//+------------------------------------------------------------------+

#include <Files/File.mqh>
#include <Math/Stat/Math.mqh>

//+------------------------------------------------------------------+
//| CONFIGURAÇÕES AVANÇADAS                                          |
//+------------------------------------------------------------------+
#define MAX_FEATURES 25              // Features expandidas
#define HIDDEN_NEURONS 15            // Neurônios na camada oculta
#define ENSEMBLE_MODELS 5            // Número de modelos no ensemble
#define MAX_SAMPLES 2000             // Amostras para treinamento
#define EPOCHS 200                   // Épocas de treinamento
#define CROSS_VALIDATION_FOLDS 5     // K-fold cross-validation

//+------------------------------------------------------------------+
//| ESTRUTURAS AVANÇADAS                                             |
//+------------------------------------------------------------------+

//--- Features expandidas com engenharia de features
struct AdvancedFeatures
{
    // Features básicas
    double volume_ratio;             
    double direction_ratio;
    double spread_points;
    double rsi_value;
    double price_velocity;
    
    // Features de engenharia técnica
    double volume_acceleration;      // Aceleração do volume
    double price_acceleration;       // Aceleração do preço
    double volatility_ratio;         // Ratio volatilidade atual/histórica
    double trend_strength;           // Força da tendência
    double support_resistance;       // Proximidade S/R
    
    // Features de padrões
    double candlestick_pattern;      // Score de padrões de candles
    double fibonacci_level;          // Proximidade níveis Fibonacci
    double bollinger_position;       // Posição nas Bandas de Bollinger
    double macd_divergence;          // Divergência MACD
    double stochastic_value;         // Valor do Stochastic
    
    // Features temporais avançadas
    double intraday_pattern;         // Padrão intraday
    double weekday_factor;           // Fator dia da semana
    double month_seasonality;        // Sazonalidade mensal
    double hour_volatility;          // Volatilidade por hora
    double session_momentum;         // Momentum da sessão
    
    // Features de microestrutura
    double bid_ask_imbalance;        // Desequilíbrio bid/ask
    double tick_imbalance;           // Desequilíbrio de ticks
    double order_flow_pressure;     // Pressão do fluxo de ordens
    double liquidity_depth;          // Profundidade de liquidez
    double market_impact;            // Impacto no mercado
};

//--- Rede neural multicamada
struct NeuralNetwork
{
    // Pesos das camadas
    double input_weights[MAX_FEATURES][HIDDEN_NEURONS];    // Input → Hidden
    double hidden_weights[HIDDEN_NEURONS];                // Hidden → Output
    double input_bias[HIDDEN_NEURONS];                    // Bias input
    double output_bias;                                   // Bias output
    
    // Ativações das camadas
    double hidden_activations[HIDDEN_NEURONS];
    double output_activation;
    
    // Gradientes para backpropagation
    double hidden_gradients[HIDDEN_NEURONS];
    double output_gradient;
    
    // Parâmetros de treinamento
    double learning_rate;
    double momentum;
    double l1_regularization;
    double l2_regularization;
    
    // Métricas
    double accuracy;
    double precision;
    double recall;
    double f1_score;
    int training_epochs;
};

//--- Modelo ensemble
struct EnsembleModel
{
    NeuralNetwork models[ENSEMBLE_MODELS];               // Múltiplos modelos
    double model_weights[ENSEMBLE_MODELS];               // Pesos dos modelos
    double ensemble_accuracy;                            // Precisão do ensemble
    int best_model_index;                               // Melhor modelo individual
};

//--- Normalizador de features
struct FeatureNormalizer
{
    double means[MAX_FEATURES];                          // Médias das features
    double stds[MAX_FEATURES];                          // Desvios padrão
    double mins[MAX_FEATURES];                          // Valores mínimos
    double maxs[MAX_FEATURES];                          // Valores máximos
    bool is_fitted;                                     // Se foi ajustado
};

//--- Cross-validation
struct CrossValidationResult
{
    double accuracy_scores[CROSS_VALIDATION_FOLDS];
    double mean_accuracy;
    double std_accuracy;
    double best_fold_accuracy;
    int best_fold_index;
};

//+------------------------------------------------------------------+
//| CLASSE PRINCIPAL AVANÇADA                                        |
//+------------------------------------------------------------------+

class CAdvancedMLSystem
{
private:
    // Dados e modelos
    AdvancedFeatures m_training_features[MAX_SAMPLES];
    double m_training_labels[MAX_SAMPLES];
    int m_sample_count;
    
    // Ensemble de modelos
    EnsembleModel m_ensemble;
    FeatureNormalizer m_normalizer;
    
    // Controle avançado
    bool m_use_ensemble;
    bool m_use_cross_validation;
    bool m_use_feature_selection;
    bool m_use_regularization;
    
    // Arquivos
    string m_model_file;
    string m_features_file;
    
public:
    CAdvancedMLSystem();
    ~CAdvancedMLSystem();
    
    // Inicialização
    bool Initialize(string symbol, int magic);
    
    // Feature engineering avançado
    AdvancedFeatures ExtractAdvancedFeatures();
    void NormalizeFeatures(AdvancedFeatures &features);
    
    // Treinamento avançado
    bool TrainEnsembleModel();
    bool TrainNeuralNetwork(NeuralNetwork &nn, int fold = -1);
    CrossValidationResult PerformCrossValidation();
    
    // Predição
    double PredictEnsemble(const AdvancedFeatures &features);
    double PredictNeuralNetwork(const NeuralNetwork &nn, const AdvancedFeatures &features);
    
    // Utilidades
    double CalculateAccuracy(const NeuralNetwork &nn);
    void SaveAdvancedModel();
    bool LoadAdvancedModel();
    
    // Configurações
    void EnableEnsemble(bool enable) { m_use_ensemble = enable; }
    void EnableCrossValidation(bool enable) { m_use_cross_validation = enable; }
    void EnableRegularization(bool enable) { m_use_regularization = enable; }
    
    // Informações
    string GetAdvancedModelInfo();
    double GetEnsembleAccuracy() { return m_ensemble.ensemble_accuracy; }
};

//+------------------------------------------------------------------+
//| IMPLEMENTAÇÃO                                                    |
//+------------------------------------------------------------------+

CAdvancedMLSystem::CAdvancedMLSystem()
{
    m_sample_count = 0;
    m_use_ensemble = true;
    m_use_cross_validation = true;
    m_use_feature_selection = true;
    m_use_regularization = true;
    
    // Inicializar normalizer
    ZeroMemory(m_normalizer);
    ZeroMemory(m_ensemble);
    
    // Configurar ensemble
    for(int i = 0; i < ENSEMBLE_MODELS; i++)
    {
        m_ensemble.model_weights[i] = 1.0 / ENSEMBLE_MODELS;
        
        // Configurar cada modelo com parâmetros diferentes
        m_ensemble.models[i].learning_rate = 0.001 + (i * 0.002);
        m_ensemble.models[i].momentum = 0.9 - (i * 0.1);
        m_ensemble.models[i].l1_regularization = 0.001 * (i + 1);
        m_ensemble.models[i].l2_regularization = 0.01 * (i + 1);
    }
}

CAdvancedMLSystem::~CAdvancedMLSystem()
{
    SaveAdvancedModel();
}

bool CAdvancedMLSystem::Initialize(string symbol, int magic)
{
    m_model_file = "AdvancedML_" + symbol + "_Model.dat";
    m_features_file = "AdvancedML_" + symbol + "_Features.csv";
    
    // Tentar carregar modelo existente
    if(!LoadAdvancedModel())
    {
        Print("🧠 Iniciando treinamento avançado...");
        
        // Coletar dados históricos
        if(!CollectHistoricalData())
        {
            Print("❌ Falha ao coletar dados históricos");
            return false;
        }
        
        // Treinar ensemble
        if(!TrainEnsembleModel())
        {
            Print("❌ Falha no treinamento do ensemble");
            return false;
        }
    }
    
    Print("✅ Sistema ML Avançado inicializado - Precisão: ", DoubleToString(m_ensemble.ensemble_accuracy, 2), "%");
    return true;
}

// Feature engineering extremamente avançado
AdvancedFeatures CAdvancedMLSystem::ExtractAdvancedFeatures()
{
    AdvancedFeatures features;
    ZeroMemory(features);
    
    // Obter dados de mercado
    MqlRates rates[];
    MqlTick ticks[];
    int rates_count = CopyRates(_Symbol, PERIOD_M1, 0, 100, rates);
    
    if(rates_count < 50) return features;
    
    // 1. FEATURES BÁSICAS APRIMORADAS
    double volumes[50];
    for(int i = 0; i < 50 && i < rates_count; i++)
        volumes[i] = (double)rates[rates_count - 50 + i].tick_volume;
    
    features.volume_ratio = volumes[49] / CalculateMean(volumes, 49);
    features.volume_acceleration = CalculateAcceleration(volumes, 49);
    
    // 2. FEATURES DE PREÇO AVANÇADAS
    double prices[50];
    for(int i = 0; i < 50 && i < rates_count; i++)
        prices[i] = rates[rates_count - 50 + i].close;
    
    features.price_velocity = CalculateVelocity(prices, 49);
    features.price_acceleration = CalculateAcceleration(prices, 49);
    features.trend_strength = CalculateTrendStrength(prices, 49);
    
    // 3. FEATURES DE VOLATILIDADE
    double volatilities[20];
    for(int i = 0; i < 20; i++)
    {
        if(i < rates_count)
            volatilities[i] = rates[rates_count - 20 + i].high - rates[rates_count - 20 + i].low;
    }
    features.volatility_ratio = volatilities[19] / CalculateMean(volatilities, 19);
    
    // 4. FEATURES TÉCNICAS
    features.rsi_value = CalculateRSI(prices, 49, 14);
    features.bollinger_position = CalculateBollingerPosition(prices, 49);
    features.macd_divergence = CalculateMACDDivergence(prices, 49);
    features.stochastic_value = CalculateStochastic(rates, rates_count);
    
    // 5. FEATURES TEMPORAIS
    MqlDateTime dt;
    TimeCurrent(dt);
    features.intraday_pattern = CalculateIntradayPattern(dt.hour, dt.min);
    features.weekday_factor = CalculateWeekdayFactor(dt.day_of_week);
    features.month_seasonality = CalculateMonthSeasonality(dt.mon);
    features.hour_volatility = CalculateHourVolatility(dt.hour);
    
    // 6. FEATURES DE MICROESTRUTURA
    features.bid_ask_imbalance = CalculateBidAskImbalance();
    features.tick_imbalance = CalculateTickImbalance();
    features.liquidity_depth = CalculateLiquidityDepth();
    
    // 7. FEATURES DE PADRÕES
    features.candlestick_pattern = AnalyzeCandlestickPatterns(rates, rates_count);
    features.fibonacci_level = CalculateFibonacciProximity(prices, 49);
    features.support_resistance = CalculateSupportResistance(prices, 49);
    
    return features;
}

// Treinamento do ensemble com cross-validation
bool CAdvancedMLSystem::TrainEnsembleModel()
{
    if(m_sample_count < 100)
    {
        Print("❌ Dados insuficientes para treinamento avançado");
        return false;
    }
    
    Print("🧠 Treinando ensemble com ", ENSEMBLE_MODELS, " modelos...");
    
    // Normalizar features
    FitNormalizer();
    
    // Cross-validation se habilitado
    CrossValidationResult cv_result;
    if(m_use_cross_validation)
    {
        cv_result = PerformCrossValidation();
        Print("📊 CV Médio: ", DoubleToString(cv_result.mean_accuracy, 2), "% ±", DoubleToString(cv_result.std_accuracy, 2));
    }
    
    // Treinar cada modelo do ensemble
    double total_accuracy = 0;
    for(int i = 0; i < ENSEMBLE_MODELS; i++)
    {
        Print("🎯 Treinando modelo ", i + 1, "/", ENSEMBLE_MODELS);
        
        if(TrainNeuralNetwork(m_ensemble.models[i]))
        {
            double accuracy = CalculateAccuracy(m_ensemble.models[i]);
            total_accuracy += accuracy;
            
            // Ajustar peso do modelo baseado na performance
            m_ensemble.model_weights[i] = accuracy / 100.0;
            
            Print("✅ Modelo ", i + 1, " - Precisão: ", DoubleToString(accuracy, 2), "%");
        }
    }
    
    // Normalizar pesos
    double weight_sum = 0;
    for(int i = 0; i < ENSEMBLE_MODELS; i++)
        weight_sum += m_ensemble.model_weights[i];
    
    for(int i = 0; i < ENSEMBLE_MODELS; i++)
        m_ensemble.model_weights[i] /= weight_sum;
    
    m_ensemble.ensemble_accuracy = total_accuracy / ENSEMBLE_MODELS;
    
    Print("🏆 Ensemble treinado - Precisão média: ", DoubleToString(m_ensemble.ensemble_accuracy, 2), "%");
    
    return true;
}

// Treinamento de rede neural com backpropagation avançado
bool CAdvancedMLSystem::TrainNeuralNetwork(NeuralNetwork &nn, int fold = -1)
{
    // Inicializar pesos aleatoriamente com Xavier initialization
    for(int i = 0; i < MAX_FEATURES; i++)
    {
        for(int j = 0; j < HIDDEN_NEURONS; j++)
        {
            nn.input_weights[i][j] = (MathRand() / 16383.5 - 1.0) * MathSqrt(2.0 / MAX_FEATURES);
        }
    }
    
    for(int i = 0; i < HIDDEN_NEURONS; i++)
    {
        nn.hidden_weights[i] = (MathRand() / 16383.5 - 1.0) * MathSqrt(2.0 / HIDDEN_NEURONS);
        nn.input_bias[i] = 0.0;
    }
    nn.output_bias = 0.0;
    
    // Parâmetros adaptativos
    double initial_lr = nn.learning_rate;
    double best_accuracy = 0;
    int patience = 20;
    int no_improvement = 0;
    
    // Treinamento com early stopping
    for(int epoch = 0; epoch < EPOCHS; epoch++)
    {
        double epoch_loss = 0;
        int correct_predictions = 0;
        
        // Embaralhar dados (simulado)
        for(int sample = 0; sample < m_sample_count; sample++)
        {
            // Forward pass
            // Hidden layer
            for(int h = 0; h < HIDDEN_NEURONS; h++)
            {
                double sum = nn.input_bias[h];
                
                // Converter features para array
                double feature_array[MAX_FEATURES];
                ConvertFeaturesToArray(m_training_features[sample], feature_array);
                
                for(int f = 0; f < MAX_FEATURES; f++)
                {
                    sum += feature_array[f] * nn.input_weights[f][h];
                }
                
                nn.hidden_activations[h] = 1.0 / (1.0 + MathExp(-sum)); // Sigmoid
            }
            
            // Output layer
            double output_sum = nn.output_bias;
            for(int h = 0; h < HIDDEN_NEURONS; h++)
            {
                output_sum += nn.hidden_activations[h] * nn.hidden_weights[h];
            }
            nn.output_activation = 1.0 / (1.0 + MathExp(-output_sum));
            
            // Calcular erro
            double target = m_training_labels[sample];
            double error = nn.output_activation - target;
            epoch_loss += error * error;
            
            // Verificar predição
            bool predicted_positive = nn.output_activation > 0.5;
            bool actual_positive = target > 0.5;
            if(predicted_positive == actual_positive)
                correct_predictions++;
            
            // Backward pass
            // Output gradient
            nn.output_gradient = error * nn.output_activation * (1.0 - nn.output_activation);
            
            // Hidden gradients
            for(int h = 0; h < HIDDEN_NEURONS; h++)
            {
                nn.hidden_gradients[h] = nn.output_gradient * nn.hidden_weights[h] * 
                                       nn.hidden_activations[h] * (1.0 - nn.hidden_activations[h]);
            }
            
            // Atualizar pesos com regularização
            // Hidden to output weights
            for(int h = 0; h < HIDDEN_NEURONS; h++)
            {
                double weight_update = nn.learning_rate * nn.output_gradient * nn.hidden_activations[h];
                
                // L1 regularization
                if(m_use_regularization)
                    weight_update += nn.l1_regularization * (nn.hidden_weights[h] > 0 ? 1.0 : -1.0);
                
                // L2 regularization
                if(m_use_regularization)
                    weight_update += nn.l2_regularization * nn.hidden_weights[h];
                
                nn.hidden_weights[h] -= weight_update;
            }
            
            // Input to hidden weights
            double feature_array[MAX_FEATURES];
            ConvertFeaturesToArray(m_training_features[sample], feature_array);
            
            for(int f = 0; f < MAX_FEATURES; f++)
            {
                for(int h = 0; h < HIDDEN_NEURONS; h++)
                {
                    double weight_update = nn.learning_rate * nn.hidden_gradients[h] * feature_array[f];
                    
                    if(m_use_regularization)
                    {
                        weight_update += nn.l1_regularization * (nn.input_weights[f][h] > 0 ? 1.0 : -1.0);
                        weight_update += nn.l2_regularization * nn.input_weights[f][h];
                    }
                    
                    nn.input_weights[f][h] -= weight_update;
                }
            }
            
            // Atualizar bias
            nn.output_bias -= nn.learning_rate * nn.output_gradient;
            for(int h = 0; h < HIDDEN_NEURONS; h++)
            {
                nn.input_bias[h] -= nn.learning_rate * nn.hidden_gradients[h];
            }
        }
        
        // Calcular precisão da época
        double epoch_accuracy = (double)correct_predictions / m_sample_count * 100.0;
        
        // Early stopping
        if(epoch_accuracy > best_accuracy)
        {
            best_accuracy = epoch_accuracy;
            no_improvement = 0;
        }
        else
        {
            no_improvement++;
            if(no_improvement >= patience)
            {
                Print("🛑 Early stopping na época ", epoch);
                break;
            }
        }
        
        // Learning rate decay
        if(epoch % 50 == 0 && epoch > 0)
        {
            nn.learning_rate *= 0.95;
        }
        
        // Log progresso
        if(epoch % 50 == 0)
        {
            Print("📈 Época ", epoch, " - Precisão: ", DoubleToString(epoch_accuracy, 2), 
                  "% - Loss: ", DoubleToString(epoch_loss / m_sample_count, 6));
        }
    }
    
    nn.accuracy = best_accuracy;
    nn.training_epochs = EPOCHS;
    
    return true;
}

// Predição do ensemble
double CAdvancedMLSystem::PredictEnsemble(const AdvancedFeatures &features)
{
    if(!m_use_ensemble)
        return PredictNeuralNetwork(m_ensemble.models[0], features);
    
    double weighted_prediction = 0;
    double total_weight = 0;
    
    for(int i = 0; i < ENSEMBLE_MODELS; i++)
    {
        double prediction = PredictNeuralNetwork(m_ensemble.models[i], features);
        weighted_prediction += prediction * m_ensemble.model_weights[i];
        total_weight += m_ensemble.model_weights[i];
    }
    
    return (total_weight > 0) ? weighted_prediction / total_weight : 0.5;
}

// Predição de rede neural individual
double CAdvancedMLSystem::PredictNeuralNetwork(const NeuralNetwork &nn, const AdvancedFeatures &features)
{
    // Converter features para array
    double feature_array[MAX_FEATURES];
    ConvertFeaturesToArray(features, feature_array);
    
    // Forward pass
    double hidden_activations[HIDDEN_NEURONS];
    
    // Hidden layer
    for(int h = 0; h < HIDDEN_NEURONS; h++)
    {
        double sum = nn.input_bias[h];
        for(int f = 0; f < MAX_FEATURES; f++)
        {
            sum += feature_array[f] * nn.input_weights[f][h];
        }
        hidden_activations[h] = 1.0 / (1.0 + MathExp(-sum));
    }
    
    // Output layer
    double output_sum = nn.output_bias;
    for(int h = 0; h < HIDDEN_NEURONS; h++)
    {
        output_sum += hidden_activations[h] * nn.hidden_weights[h];
    }
    
    return 1.0 / (1.0 + MathExp(-output_sum));
}

// Funções utilitárias auxiliares (implementação básica)
double CalculateMean(const double &array[], int size)
{
    double sum = 0;
    for(int i = 0; i < size; i++)
        sum += array[i];
    return sum / size;
}

double CalculateAcceleration(const double &array[], int size)
{
    if(size < 3) return 0;
    double v1 = array[size-1] - array[size-2];
    double v2 = array[size-2] - array[size-3];
    return v1 - v2;
}

double CalculateVelocity(const double &array[], int size)
{
    if(size < 2) return 0;
    return array[size-1] - array[size-2];
}

// ... [Outras funções auxiliares implementadas de forma básica]

//+------------------------------------------------------------------+
//| FUNÇÕES AUXILIARES DE FEATURE ENGINEERING                        |
//+------------------------------------------------------------------+

double CalculateTrendStrength(const double &prices[], int size)
{
    if(size < 10) return 0.5;
    
    double upward_moves = 0;
    for(int i = 1; i < size; i++)
    {
        if(prices[i] > prices[i-1])
            upward_moves++;
    }
    
    return upward_moves / (size - 1);
}

double CalculateRSI(const double &prices[], int size, int period)
{
    if(size < period + 1) return 50.0;
    
    double gain = 0, loss = 0;
    
    for(int i = size - period; i < size; i++)
    {
        if(i <= 0) continue;
        double diff = prices[i] - prices[i-1];
        if(diff > 0) gain += diff;
        else loss -= diff;
    }
    
    gain /= period;
    loss /= period;
    
    if(loss == 0) return 100.0;
    if(gain == 0) return 0.0;
    
    double rs = gain / loss;
    return 100.0 - (100.0 / (1.0 + rs));
}

double CalculateBollingerPosition(const double &prices[], int size)
{
    if(size < 20) return 0.5;
    
    double sma = CalculateMean(prices, 20);
    double std = CalculateStandardDeviation(prices, 20);
    
    double upper_band = sma + (2.0 * std);
    double lower_band = sma - (2.0 * std);
    
    double current_price = prices[size-1];
    
    if(upper_band == lower_band) return 0.5;
    
    return (current_price - lower_band) / (upper_band - lower_band);
}

double CalculateStandardDeviation(const double &array[], int size)
{
    double mean = CalculateMean(array, size);
    double sum_sq_diff = 0;
    
    for(int i = 0; i < size; i++)
    {
        double diff = array[i] - mean;
        sum_sq_diff += diff * diff;
    }
    
    return MathSqrt(sum_sq_diff / size);
}

// Mais funções auxiliares implementadas de forma otimizada... 