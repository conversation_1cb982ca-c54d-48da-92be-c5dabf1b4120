//+------------------------------------------------------------------+
//|                                          MLTrainingSystem.mqh   |
//|                   Sistema de Treinamento ML para Ultra Scalper  |
//|                   Treinamento inicial + Retreinamento contínuo  |
//+------------------------------------------------------------------+

#include <Arrays/ArrayObj.mqh>
#include <Files/File.mqh>
#include <Files/FileTxt.mqh>

//+------------------------------------------------------------------+
//| ESTRUTURAS PARA SISTEMA DE ML                                   |
//+------------------------------------------------------------------+

//--- Estrutura para dados de treinamento
struct MLTrainingData
{
    // Features de entrada
    double volume_ratio;             // Ratio volume atual/médio
    double direction_ratio;          // Ratio direção dos ticks
    double spread_points;            // Spread em pontos
    double rsi_value;                // RSI ultrarrápido
    double price_velocity;           // Velocidade do preço
    double volatility_score;         // Score de volatilidade
    double time_factor;              // Fator temporal
    double momentum_score;           // Score de momentum
    double liquidity_score;          // Score de liquidez
    double tick_frequency;           // Frequência de ticks
    
    // Labels de saída (resultado real)
    bool was_profitable;             // Se foi lucrativo
    int operation_time;              // Tempo real da operação
    double profit_points;            // Lucro em pontos
    bool direction_correct;          // Se direção foi correta
    double success_score;            // Score de sucesso (0-1)
    
    // Metadados
    datetime timestamp;              // Timestamp da operação
    string operation_id;             // ID da operação
};

//--- Estrutura para modelo ML
struct MLModel
{
    // Pesos das features (treinados)
    double weights[10];              // Pesos para as 10 features
    double bias;                     // Bias do modelo
    
    // Métricas de performance
    double accuracy;                 // Precisão do modelo
    double precision;                // Precisão positiva
    double recall;                   // Recall
    double f1_score;                 // F1 Score
    
    // Controle de treinamento
    int training_samples;            // Número de amostras de treinamento
    datetime last_training;          // Último treinamento
    int model_version;               // Versão do modelo
    bool is_trained;                 // Se modelo foi treinado
};

//--- Estrutura para estatísticas de treinamento
struct MLTrainingStats
{
    int total_samples;               // Total de amostras
    int positive_samples;            // Amostras positivas
    int negative_samples;            // Amostras negativas
    double avg_accuracy;             // Precisão média
    double best_accuracy;            // Melhor precisão
    datetime last_retrain;           // Último retreinamento
    int retrain_count;               // Número de retreinamentos
};

//+------------------------------------------------------------------+
//| CLASSE PRINCIPAL DO SISTEMA ML                                  |
//+------------------------------------------------------------------+

class CMLTrainingSystem
{
private:
    // Dados de treinamento
    MLTrainingData m_training_data[1000];        // Array de dados de treinamento
    int m_training_count;                        // Contador de dados
    
    // Modelo ML
    MLModel m_model;                             // Modelo atual
    MLTrainingStats m_stats;                     // Estatísticas
    
    // Configurações
    int m_max_training_samples;                  // Máximo de amostras
    int m_retrain_threshold;                     // Threshold para retreinamento
    double m_learning_rate;                      // Taxa de aprendizado
    
    // Controle de arquivos
    string m_data_file;                          // Arquivo de dados
    string m_model_file;                         // Arquivo do modelo
    string m_stats_file;                         // Arquivo de estatísticas
    
    // Flags de controle
    bool m_initial_training_done;                // Treinamento inicial feito
    bool m_auto_retrain_enabled;                 // Retreinamento automático
    
public:
    // Construtor e destrutor
    CMLTrainingSystem();
    ~CMLTrainingSystem();
    
    // Inicialização
    bool Initialize(string symbol, int magic_number);
    void Shutdown();
    
    // Treinamento inicial
    bool PerformInitialTraining();
    bool LoadHistoricalData();
    bool TrainInitialModel();
    
    // Retreinamento contínuo
    bool AddRealOperationData(const MLTrainingData &data);
    bool CheckRetrainConditions();
    bool PerformRetrain();
    
    // Predição
    double PredictProbability(const MLTrainingData &features);
    double GetModelAccuracy() { return m_model.accuracy; }
    bool IsModelTrained() { return m_model.is_trained; }
    
    // Persistência
    bool SaveTrainingData();
    bool LoadTrainingData();
    bool SaveModel();
    bool LoadModel();
    
    // Estatísticas
    MLTrainingStats GetTrainingStats() { return m_stats; }
    string GetModelInfo();
    
    // Configurações
    void SetMaxTrainingSamples(int samples) { m_max_training_samples = samples; }
    void SetRetrainThreshold(int threshold) { m_retrain_threshold = threshold; }
    void SetLearningRate(double rate) { m_learning_rate = rate; }
    void EnableAutoRetrain(bool enable) { m_auto_retrain_enabled = enable; }
};

//+------------------------------------------------------------------+
//| IMPLEMENTAÇÃO DA CLASSE                                          |
//+------------------------------------------------------------------+

//--- Construtor
CMLTrainingSystem::CMLTrainingSystem()
{
    m_training_count = 0;
    m_max_training_samples = 1000;
    m_retrain_threshold = 50;
    m_learning_rate = 0.01;
    m_initial_training_done = false;
    m_auto_retrain_enabled = true;
    
    // Inicializar modelo
    ZeroMemory(m_model);
    m_model.model_version = 1;
    
    // Inicializar estatísticas
    ZeroMemory(m_stats);
    
    // Redimensionar array
    ArrayResize(m_training_data, m_max_training_samples);
}

//--- Destrutor
CMLTrainingSystem::~CMLTrainingSystem()
{
    Shutdown();
}

//--- Inicialização
bool CMLTrainingSystem::Initialize(string symbol, int magic_number)
{
    // Definir nomes dos arquivos
    m_data_file = "MLTraining_" + symbol + "_" + IntegerToString(magic_number) + "_Data.csv";
    m_model_file = "MLTraining_" + symbol + "_" + IntegerToString(magic_number) + "_Model.dat";
    m_stats_file = "MLTraining_" + symbol + "_" + IntegerToString(magic_number) + "_Stats.dat";
    
    // Tentar carregar dados existentes
    LoadTrainingData();
    LoadModel();
    
    // Se não há modelo treinado, fazer treinamento inicial
    if(!m_model.is_trained)
    {
        Print("🧠 ML: Iniciando treinamento inicial...");
        if(!PerformInitialTraining())
        {
            Print("❌ ML: Falha no treinamento inicial");
            return false;
        }
    }
    else
    {
        Print("🧠 ML: Modelo carregado - Precisão: ", DoubleToString(m_model.accuracy, 2), "%");
    }
    
    return true;
}

//--- Shutdown
void CMLTrainingSystem::Shutdown()
{
    // Salvar dados antes de finalizar
    SaveTrainingData();
    SaveModel();
}

//--- Treinamento inicial com dados históricos
bool CMLTrainingSystem::PerformInitialTraining()
{
    Print("🧠 ML: Coletando dados históricos...");
    
    // Coletar dados históricos
    if(!LoadHistoricalData())
    {
        Print("❌ ML: Falha ao coletar dados históricos");
        return false;
    }
    
    Print("🧠 ML: Dados coletados: ", m_training_count, " amostras");
    
    // Treinar modelo inicial
    if(!TrainInitialModel())
    {
        Print("❌ ML: Falha no treinamento inicial");
        return false;
    }
    
    m_initial_training_done = true;
    Print("✅ ML: Treinamento inicial concluído - Precisão: ", DoubleToString(m_model.accuracy, 2), "%");
    
    return true;
}

//--- Carregar dados históricos
bool CMLTrainingSystem::LoadHistoricalData()
{
    // Obter dados históricos dos últimos 30 dias
    datetime start_time = TimeCurrent() - (30 * 24 * 3600); // 30 dias atrás
    datetime end_time = TimeCurrent();
    
    // Copiar dados históricos
    MqlRates rates[];
    int rates_count = CopyRates(_Symbol, PERIOD_M1, start_time, end_time, rates);
    
    if(rates_count <= 0)
    {
        Print("❌ ML: Não foi possível obter dados históricos");
        return false;
    }
    
    Print("🧠 ML: Processando ", rates_count, " candles históricos...");
    
    // Processar dados históricos para criar amostras de treinamento
    for(int i = 20; i < rates_count - 10; i++) // Deixar margem para cálculos
    {
        if(m_training_count >= m_max_training_samples) break;
        
        // Criar amostra de treinamento
        MLTrainingData sample;
        ZeroMemory(sample);
        
        // Calcular features baseadas nos dados históricos
        sample.volume_ratio = CalculateHistoricalVolumeRatio(rates, i);
        sample.direction_ratio = CalculateHistoricalDirectionRatio(rates, i);
        sample.spread_points = 1.0; // Spread padrão para dados históricos
        sample.rsi_value = CalculateHistoricalRSI(rates, i);
        sample.price_velocity = CalculateHistoricalVelocity(rates, i);
        sample.volatility_score = CalculateHistoricalVolatility(rates, i);
        sample.time_factor = CalculateHistoricalTimeFactor(rates[i].time);
        sample.momentum_score = CalculateHistoricalMomentum(rates, i);
        sample.liquidity_score = 0.7; // Score padrão para dados históricos
        sample.tick_frequency = 5.0; // Frequência padrão
        
        // Calcular labels baseados no movimento real
        double entry_price = rates[i].close;
        double exit_price_5s = rates[i + 1].close; // Próximo candle como proxy
        double exit_price_10s = rates[i + 2].close; // 2 candles como proxy
        
        // Determinar se seria lucrativo (simplificado)
        bool would_be_buy = sample.direction_ratio > 0.6;
        double profit_5s = would_be_buy ? (exit_price_5s - entry_price) : (entry_price - exit_price_5s);
        double profit_10s = would_be_buy ? (exit_price_10s - entry_price) : (entry_price - exit_price_10s);
        
        // Considerar custos
        double costs = 2.0; // Spread + slippage estimado
        
        sample.was_profitable = (profit_5s > costs) || (profit_10s > costs);
        sample.operation_time = (profit_5s > costs) ? 5 : 10;
        sample.profit_points = MathMax(profit_5s, profit_10s) - costs;
        sample.direction_correct = (would_be_buy && profit_5s > 0) || (!would_be_buy && profit_5s > 0);
        sample.success_score = sample.was_profitable ? 1.0 : 0.0;
        
        sample.timestamp = rates[i].time;
        sample.operation_id = "HIST_" + IntegerToString(i);
        
        // Adicionar amostra
        m_training_data[m_training_count] = sample;
        m_training_count++;
    }
    
    return m_training_count > 0;
}

//--- Treinar modelo inicial
bool CMLTrainingSystem::TrainInitialModel()
{
    if(m_training_count < 50)
    {
        Print("❌ ML: Dados insuficientes para treinamento (mínimo 50, atual: ", m_training_count, ")");
        return false;
    }
    
    Print("🧠 ML: Treinando modelo com ", m_training_count, " amostras...");
    
    // Inicializar pesos aleatoriamente
    for(int i = 0; i < 10; i++)
    {
        m_model.weights[i] = (MathRand() / 32767.0 - 0.5) * 0.1; // Pesos pequenos aleatórios
    }
    m_model.bias = 0.0;
    
    // Treinamento por gradient descent simplificado
    int epochs = 100;
    double best_accuracy = 0.0;
    
    for(int epoch = 0; epoch < epochs; epoch++)
    {
        double total_loss = 0.0;
        int correct_predictions = 0;
        
        // Iterar sobre todas as amostras
        for(int i = 0; i < m_training_count; i++)
        {
            // Forward pass
            double prediction = PredictProbability(m_training_data[i]);
            double target = m_training_data[i].success_score;
            
            // Calcular erro
            double error = prediction - target;
            total_loss += error * error;
            
            // Verificar se predição está correta
            bool predicted_positive = prediction > 0.5;
            bool actual_positive = target > 0.5;
            if(predicted_positive == actual_positive)
                correct_predictions++;
            
            // Backward pass (atualizar pesos)
            double gradient = 2.0 * error * prediction * (1.0 - prediction); // Derivada da sigmoid
            
            // Atualizar pesos
            m_model.weights[0] -= m_learning_rate * gradient * m_training_data[i].volume_ratio;
            m_model.weights[1] -= m_learning_rate * gradient * m_training_data[i].direction_ratio;
            m_model.weights[2] -= m_learning_rate * gradient * m_training_data[i].spread_points;
            m_model.weights[3] -= m_learning_rate * gradient * m_training_data[i].rsi_value;
            m_model.weights[4] -= m_learning_rate * gradient * m_training_data[i].price_velocity;
            m_model.weights[5] -= m_learning_rate * gradient * m_training_data[i].volatility_score;
            m_model.weights[6] -= m_learning_rate * gradient * m_training_data[i].time_factor;
            m_model.weights[7] -= m_learning_rate * gradient * m_training_data[i].momentum_score;
            m_model.weights[8] -= m_learning_rate * gradient * m_training_data[i].liquidity_score;
            m_model.weights[9] -= m_learning_rate * gradient * m_training_data[i].tick_frequency;
            
            m_model.bias -= m_learning_rate * gradient;
        }
        
        // Calcular precisão da época
        double epoch_accuracy = (double)correct_predictions / m_training_count * 100.0;
        
        if(epoch_accuracy > best_accuracy)
        {
            best_accuracy = epoch_accuracy;
        }
        
        // Log progresso a cada 20 épocas
        if(epoch % 20 == 0)
        {
            Print("🧠 ML: Época ", epoch, " - Precisão: ", DoubleToString(epoch_accuracy, 2), "%");
        }
    }
    
    // Finalizar modelo
    m_model.accuracy = best_accuracy;
    m_model.training_samples = m_training_count;
    m_model.last_training = TimeCurrent();
    m_model.is_trained = true;
    
    // Atualizar estatísticas
    m_stats.total_samples = m_training_count;
    m_stats.avg_accuracy = best_accuracy;
    m_stats.best_accuracy = best_accuracy;
    m_stats.last_retrain = TimeCurrent();
    
    Print("✅ ML: Modelo treinado - Precisão final: ", DoubleToString(best_accuracy, 2), "%");
    
    return true;
}

//--- Predição usando o modelo treinado
double CMLTrainingSystem::PredictProbability(const MLTrainingData &features)
{
    if(!m_model.is_trained)
        return 0.5; // Probabilidade neutra se não treinado
    
    // Calcular soma ponderada
    double sum = m_model.bias;
    sum += m_model.weights[0] * features.volume_ratio;
    sum += m_model.weights[1] * features.direction_ratio;
    sum += m_model.weights[2] * features.spread_points;
    sum += m_model.weights[3] * features.rsi_value;
    sum += m_model.weights[4] * features.price_velocity;
    sum += m_model.weights[5] * features.volatility_score;
    sum += m_model.weights[6] * features.time_factor;
    sum += m_model.weights[7] * features.momentum_score;
    sum += m_model.weights[8] * features.liquidity_score;
    sum += m_model.weights[9] * features.tick_frequency;
    
    // Aplicar função sigmoid
    double probability = 1.0 / (1.0 + MathExp(-sum));
    
    return probability * 100.0; // Retornar em porcentagem
}

//--- Adicionar dados de operação real
bool CMLTrainingSystem::AddRealOperationData(const MLTrainingData &data)
{
    if(m_training_count >= m_max_training_samples)
    {
        // Remover dados mais antigos (FIFO)
        for(int i = 0; i < m_max_training_samples - 1; i++)
        {
            m_training_data[i] = m_training_data[i + 1];
        }
        m_training_count = m_max_training_samples - 1;
    }
    
    // Adicionar nova amostra
    m_training_data[m_training_count] = data;
    m_training_count++;
    
    // Verificar se precisa retreinar
    if(m_auto_retrain_enabled && CheckRetrainConditions())
    {
        PerformRetrain();
    }
    
    return true;
}

//--- Verificar condições para retreinamento
bool CMLTrainingSystem::CheckRetrainConditions()
{
    // Retreinar a cada X novas amostras
    int samples_since_last_train = m_training_count - m_model.training_samples;
    
    if(samples_since_last_train >= m_retrain_threshold)
        return true;
    
    // Retreinar se precisão caiu muito
    if(m_model.accuracy < 60.0)
        return true;
    
    // Retreinar uma vez por dia
    if(TimeCurrent() - m_model.last_training > 24 * 3600)
        return true;
    
    return false;
}

//--- Retreinamento
bool CMLTrainingSystem::PerformRetrain()
{
    Print("🧠 ML: Iniciando retreinamento...");
    
    // Usar mesma lógica do treinamento inicial mas com dados atuais
    bool result = TrainInitialModel();
    
    if(result)
    {
        m_model.model_version++;
        m_stats.retrain_count++;
        m_stats.last_retrain = TimeCurrent();
        
        Print("✅ ML: Retreinamento concluído - Nova precisão: ", DoubleToString(m_model.accuracy, 2), "%");
    }
    
    return result;
}

//--- Salvar dados de treinamento
bool CMLTrainingSystem::SaveTrainingData()
{
    CFileTxt file;
    
    if(!file.Open(m_data_file, FILE_WRITE | FILE_CSV))
    {
        Print("❌ ML: Erro ao salvar dados de treinamento");
        return false;
    }
    
    // Cabeçalho
    file.WriteString("timestamp,volume_ratio,direction_ratio,spread_points,rsi_value,price_velocity,volatility_score,time_factor,momentum_score,liquidity_score,tick_frequency,was_profitable,operation_time,profit_points,direction_correct,success_score,operation_id\n");
    
    // Dados
    for(int i = 0; i < m_training_count; i++)
    {
        string line = "";
        line += TimeToString(m_training_data[i].timestamp) + ",";
        line += DoubleToString(m_training_data[i].volume_ratio, 4) + ",";
        line += DoubleToString(m_training_data[i].direction_ratio, 4) + ",";
        line += DoubleToString(m_training_data[i].spread_points, 2) + ",";
        line += DoubleToString(m_training_data[i].rsi_value, 2) + ",";
        line += DoubleToString(m_training_data[i].price_velocity, 4) + ",";
        line += DoubleToString(m_training_data[i].volatility_score, 4) + ",";
        line += DoubleToString(m_training_data[i].time_factor, 4) + ",";
        line += DoubleToString(m_training_data[i].momentum_score, 4) + ",";
        line += DoubleToString(m_training_data[i].liquidity_score, 4) + ",";
        line += DoubleToString(m_training_data[i].tick_frequency, 2) + ",";
        line += IntegerToString(m_training_data[i].was_profitable ? 1 : 0) + ",";
        line += IntegerToString(m_training_data[i].operation_time) + ",";
        line += DoubleToString(m_training_data[i].profit_points, 2) + ",";
        line += IntegerToString(m_training_data[i].direction_correct ? 1 : 0) + ",";
        line += DoubleToString(m_training_data[i].success_score, 2) + ",";
        line += m_training_data[i].operation_id + "\n";
        
        file.WriteString(line);
    }
    
    file.Close();
    return true;
}

//--- Carregar dados de treinamento
bool CMLTrainingSystem::LoadTrainingData()
{
    CFileTxt file;
    
    if(!file.Open(m_data_file, FILE_READ | FILE_CSV))
    {
        Print("ℹ️ ML: Arquivo de dados não encontrado - primeiro uso");
        return false;
    }
    
    string line;
    bool first_line = true;
    m_training_count = 0;
    
    while(!file.IsEnding() && m_training_count < m_max_training_samples)
    {
        line = file.ReadString();
        
        if(first_line)
        {
            first_line = false;
            continue; // Pular cabeçalho
        }
        
        if(line == "") continue;
        
        // Parsear linha (implementação simplificada)
        // Na prática, você implementaria um parser CSV completo
        
        m_training_count++;
    }
    
    file.Close();
    
    if(m_training_count > 0)
    {
        Print("✅ ML: Carregados ", m_training_count, " dados de treinamento");
        return true;
    }
    
    return false;
}

//--- Salvar modelo
bool CMLTrainingSystem::SaveModel()
{
    CFile file;
    
    if(!file.Open(m_model_file, FILE_WRITE | FILE_BIN))
    {
        Print("❌ ML: Erro ao salvar modelo");
        return false;
    }
    
    // Salvar estrutura do modelo
    file.WriteStruct(m_model);
    file.Close();
    
    return true;
}

//--- Carregar modelo
bool CMLTrainingSystem::LoadModel()
{
    CFile file;
    
    if(!file.Open(m_model_file, FILE_READ | FILE_BIN))
    {
        Print("ℹ️ ML: Modelo não encontrado - primeiro uso");
        return false;
    }
    
    // Carregar estrutura do modelo
    file.ReadStruct(m_model);
    file.Close();
    
    if(m_model.is_trained)
    {
        Print("✅ ML: Modelo carregado - Versão: ", m_model.model_version, " - Precisão: ", DoubleToString(m_model.accuracy, 2), "%");
        return true;
    }
    
    return false;
}

//--- Obter informações do modelo
string CMLTrainingSystem::GetModelInfo()
{
    string info = "";
    info += "Modelo ML v" + IntegerToString(m_model.model_version) + "\n";
    info += "Treinado: " + (m_model.is_trained ? "SIM" : "NÃO") + "\n";
    info += "Precisão: " + DoubleToString(m_model.accuracy, 2) + "%\n";
    info += "Amostras: " + IntegerToString(m_model.training_samples) + "\n";
    info += "Retreinamentos: " + IntegerToString(m_stats.retrain_count) + "\n";
    info += "Última atualização: " + TimeToString(m_model.last_training);
    
    return info;
}

//+------------------------------------------------------------------+
//| FUNÇÕES AUXILIARES PARA DADOS HISTÓRICOS                        |
//+------------------------------------------------------------------+

//--- Calcular ratio de volume histórico
double CalculateHistoricalVolumeRatio(const MqlRates &rates[], int index)
{
    if(index < 10) return 1.0;
    
    long current_volume = rates[index].tick_volume;
    long avg_volume = 0;
    
    for(int i = index - 10; i < index; i++)
    {
        avg_volume += rates[i].tick_volume;
    }
    
    avg_volume /= 10;
    
    return (avg_volume > 0) ? (double)current_volume / avg_volume : 1.0;
}

//--- Calcular ratio de direção histórico
double CalculateHistoricalDirectionRatio(const MqlRates &rates[], int index)
{
    if(index < 5) return 0.5;
    
    int up_moves = 0;
    
    for(int i = index - 5; i < index; i++)
    {
        if(rates[i].close > rates[i - 1].close)
            up_moves++;
    }
    
    return (double)up_moves / 5.0;
}

//--- Calcular RSI histórico
double CalculateHistoricalRSI(const MqlRates &rates[], int index)
{
    if(index < 14) return 50.0;
    
    double gain = 0, loss = 0;
    
    for(int i = index - 13; i <= index; i++)
    {
        if(i <= 0) continue;
        
        double diff = rates[i].close - rates[i - 1].close;
        if(diff > 0)
            gain += diff;
        else
            loss -= diff;
    }
    
    gain /= 14;
    loss /= 14;
    
    if(loss == 0) return 100.0;
    if(gain == 0) return 0.0;
    
    double rs = gain / loss;
    return 100.0 - (100.0 / (1.0 + rs));
}

//--- Calcular velocidade histórica
double CalculateHistoricalVelocity(const MqlRates &rates[], int index)
{
    if(index < 5) return 0.0;
    
    double price_change = rates[index].close - rates[index - 5].close;
    int time_change = (int)(rates[index].time - rates[index - 5].time);
    
    return (time_change > 0) ? price_change / time_change : 0.0;
}

//--- Calcular volatilidade histórica
double CalculateHistoricalVolatility(const MqlRates &rates[], int index)
{
    if(index < 10) return 0.5;
    
    double sum = 0, sum_sq = 0;
    
    for(int i = index - 9; i <= index; i++)
    {
        double range = rates[i].high - rates[i].low;
        sum += range;
        sum_sq += range * range;
    }
    
    double mean = sum / 10;
    double variance = (sum_sq / 10) - (mean * mean);
    double std_dev = MathSqrt(variance);
    
    return MathMin(1.0, std_dev / mean);
}

//--- Calcular fator temporal histórico
double CalculateHistoricalTimeFactor(datetime time)
{
    MqlDateTime dt;
    TimeToStruct(time, dt);
    
    if((dt.hour >= 9 && dt.hour < 11) || (dt.hour >= 14 && dt.hour < 16))
        return 1.0;
    else if(dt.hour >= 11 && dt.hour < 14)
        return 0.6;
    else
        return 0.3;
}

//--- Calcular momentum histórico
double CalculateHistoricalMomentum(const MqlRates &rates[], int index)
{
    if(index < 5) return 0.5;
    
    double momentum = 0;
    
    for(int i = index - 4; i <= index; i++)
    {
        if(i <= 0) continue;
        
        double movement = rates[i].close - rates[i - 1].close;
        momentum += (movement > 0) ? 0.2 : -0.2;
    }
    
    return (momentum + 1.0) / 2.0;
} 