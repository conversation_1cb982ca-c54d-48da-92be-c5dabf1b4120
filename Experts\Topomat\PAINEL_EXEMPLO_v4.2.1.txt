╔══════════════════════════════════════════════════════════════════╗
║                🚀 SCALPER ESGOTAMENTO WIN M5 v4.2.1                ║
╠══════════════════════════════════════════════════════════════════╣
║ 📊 Status: Em posição (aguardando TP)                               ║
║ ⏰ Horário: 14:35 | Operação: PERMITIDA                             ║
║ 🚨 Falso Esgotamento: MONITORANDO (3 candles) | Reversão: ON       ║
║ 📈 Entradas: 2 | Volume: 20.0 | Preço Médio: 118825 | ⏱️ 2:15/6min ║
║ ⏱️ CRONÔMETRO: 2:15/6min (NORMAL)                                   ║
║ 🎯 Métodos: 2 | Vol.Base: 10.0 | Lucro/Contrato: R$1.00            ║
║ 🌊 Ondas: OFF | 🧠 ML: ON (47 trades) | Precisão: 82% | ⏰ Saída: 6min ║
║ 📊 Spread: 1.2 | Slippage: 1.8 | Execuções: 127                     ║
║ 🎮 Debug: ON | 📱 Push: ON | 📈 Vol.Análise: ON | 🛡️ SL: OFF        ║
╚══════════════════════════════════════════════════════════════════╝

═══════════════════════════════════════════════════════════════════

EXEMPLO QUANDO SISTEMA DE FALSO ESGOTAMENTO TEM HISTÓRICO:

╔══════════════════════════════════════════════════════════════════╗
║                🚀 SCALPER ESGOTAMENTO WIN M5 v4.2.1                ║
╠══════════════════════════════════════════════════════════════════╣
║ 📊 Status: Aguardando esgotamento                                   ║
║ ⏰ Horário: 15:42 | Operação: PERMITIDA                             ║
║ 🚨 Falso Esgotamento: ATIVO (3 candles) | Histórico: 8 detecções   ║
║     | R$234 economizadas | Reversões: 6 (75%) | Reversão: ON       ║
║ ⚙️ Config: Min.2 métodos | SL:OFF | Vol.Base: 10.0 | Lucro/Contrato: R$1.00 ║
║ 📅 Horário Permitido: 9:05 até 18:20                                ║
║ 🌊 Ondas: OFF | 🧠 ML: ON (73 trades) | Precisão: 85% | ⏰ Saída: 6min ║
║ 📊 Spread: 0.8 | Slippage: 1.5 | Execuções: 203                     ║
║ 🎮 Debug: ON | 📱 Push: ON | 📈 Vol.Análise: ON | 🛡️ SL: OFF        ║
╚══════════════════════════════════════════════════════════════════╝

═══════════════════════════════════════════════════════════════════

EXEMPLO QUANDO SISTEMA DE FALSO ESGOTAMENTO ESTÁ DESATIVADO:

╔══════════════════════════════════════════════════════════════════╗
║                🚀 SCALPER ESGOTAMENTO WIN M5 v4.2.1                ║
╠══════════════════════════════════════════════════════════════════╣
║ 📊 Status: Aguardando esgotamento                                   ║
║ ⏰ Horário: 16:28 | Operação: PERMITIDA                             ║
║ 🚨 Falso Esgotamento: DESATIVADO                                    ║
║ ⚙️ Config: Min.1 métodos | SL:15pts | Vol.Base: 5.0 | Lucro/Contrato: R$2.00 ║
║ 📅 Horário Permitido: 9:05 até 18:20                                ║
║ 🌊 Ondas: ON | 🧠 ML: Inicializando... | ⏰ Saída: OFF              ║
║ 📊 Spread: 1.0 | Slippage: 2.2 | Execuções: 45                      ║
║ 🎮 Debug: OFF | 📱 Push: ON | 📈 Vol.Análise: ON | 🛡️ SL: 15pts     ║
╚══════════════════════════════════════════════════════════════════╝

═══════════════════════════════════════════════════════════════════

MELHORIAS IMPLEMENTADAS v4.2.1:

✅ RESOLVIDO: Texto "Precisão" não é mais cortado
✅ NOVO: Linha dedicada ao sistema de falso esgotamento sempre visível
✅ MELHOR: Painel expandido (520x300px) com mais espaço
✅ VISUAL: Emojis para identificação rápida de cada seção
✅ CORES: Sistema de cores dinâmicas por status
✅ INFO: Todas as configurações importantes visíveis
✅ REAL-TIME: Estatísticas em tempo real do falso esgotamento
✅ HISTÓRICO: Performance acumulada das detecções e reversões

═══════════════════════════════════════════════════════════════════

CORES DO SISTEMA DE FALSO ESGOTAMENTO:

🟢 VERDE (clrLime): Sistema ativo e funcionando
🟡 AMARELO (clrYellow): Monitorando posição em tempo real  
🔴 VERMELHO (clrRed): Sistema desativado
🔵 AZUL (clrLightBlue): Configurações e informações gerais

═══════════════════════════════════════════════════════════════════ 