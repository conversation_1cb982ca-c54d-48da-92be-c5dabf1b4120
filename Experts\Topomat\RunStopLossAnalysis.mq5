//+------------------------------------------------------------------+
//| RunStopLossAnalysis.mq5                                          |
//| Script para executar análise de Stop Loss do Topomat EA         |
//+------------------------------------------------------------------+

#property copyright "Topomat EA"
#property version   "4.32"
#property script_show_inputs

//+------------------------------------------------------------------+
//| Parâmetros de entrada                                            |
//+------------------------------------------------------------------+
input bool InpConfirmAnalysis = true; // Confirmar execução da análise

//+------------------------------------------------------------------+
//| Função principal do script                                       |
//+------------------------------------------------------------------+
void OnStart()
{
    if(!InpConfirmAnalysis)
    {
        Print("❌ Análise cancelada pelo usuário");
        return;
    }
    
    Print("🚀 Solicitando análise de Stop Loss...");
    
    // Definir GlobalVariable para solicitar análise
    if(GlobalVariableSet("TOPOMAT_ANALYZE_SL", 1))
    {
        Print("✅ Solicitação enviada!");
        Print("📊 O EA executará a análise no próximo tick");
        Print("📄 Relatório HTML será gerado na pasta TopoLogs");
        Print("💡 Aguarde a notificação de conclusão");
        
        // Mostrar instruções
        MessageBox(
            "Análise de Stop Loss solicitada!\n\n" +
            "✅ O EA executará a análise automaticamente\n" +
            "📄 Relatório HTML será gerado em TopoLogs\n" +
            "📱 Você receberá notificação quando concluir\n\n" +
            "💡 Para implementar o SL recomendado:\n" +
            "1. Aguarde o relatório\n" +
            "2. Analise as recomendações\n" +
            "3. Altere manualmente o parâmetro InpStopLossPoints\n" +
            "4. Reinicie o EA com novo SL",
            "Análise de Stop Loss - Topomat EA",
            MB_OK | MB_ICONINFORMATION
        );
    }
    else
    {
        Print("❌ Falha ao solicitar análise");
        MessageBox(
            "Erro ao solicitar análise!\n\n" +
            "Verifique se:\n" +
            "• O EA está rodando\n" +
            "• A análise está habilitada\n" +
            "• Há dados históricos suficientes",
            "Erro - Análise de Stop Loss",
            MB_OK | MB_ICONERROR
        );
    }
}
