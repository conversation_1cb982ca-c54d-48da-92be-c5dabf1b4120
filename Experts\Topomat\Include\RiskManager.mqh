//+------------------------------------------------------------------+
//|                                                  RiskManager.mqh |
//|                        Copyright 2024, Topomat Trading Systems   |
//|                                             https://topomat.com   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Topomat Trading Systems"
#property link      "https://topomat.com"

//--- Includes necessários
#include <Trade/Trade.mqh>
#include <Trade/PositionInfo.mqh>
#include "LogManager.mqh"
#include "DataValidator.mqh"

//+------------------------------------------------------------------+
//| SISTEMA DE GESTÃO DE RISCO                                       |
//+------------------------------------------------------------------+

//--- Tipos de limite de risco
enum ENUM_RISK_LIMIT_TYPE
{
   RISK_LIMIT_DAILY_LOSS = 0,      // Limite de perda diária
   RISK_LIMIT_DAILY_PROFIT = 1,    // Limite de lucro diário
   RISK_LIMIT_MAX_EXPOSURE = 2,    // Exposição máxima simultânea
   RISK_LIMIT_MAX_TRADES = 3,      // Máximo de trades por dia
   RISK_LIMIT_DRAWDOWN = 4,        // Drawdown máximo
   RISK_LIMIT_MARGIN = 5,          // Margem mínima
   RISK_LIMIT_EXPOSURE = 6,        // Limite de exposição
   RISK_LIMIT_TRADE_COUNT = 7      // Limite de contagem de trades
};

//--- Estado do controle de risco
enum ENUM_RISK_STATE
{
   RISK_STATE_NORMAL = 0,          // Estado normal
   RISK_STATE_WARNING = 1,         // Estado de aviso
   RISK_STATE_CRITICAL = 2,        // Estado crítico
   RISK_STATE_BLOCKED = 3          // Trading bloqueado
};

//--- Estrutura de configuração de risco
struct SRiskConfig
{
   //--- Limites percentuais
   double            max_risk_per_trade;     // Risco máximo por trade (% da conta)
   double            max_daily_risk;         // Risco máximo diário (% da conta)
   double            max_drawdown;           // Drawdown máximo permitido (%)
   double            min_margin_level;       // Nível mínimo de margem (%)
   
   //--- Limites absolutos
   double            max_daily_loss;         // Perda máxima diária (valor absoluto)
   double            max_daily_profit;       // Lucro máximo diário (valor absoluto)
   
   //--- Controles de volume/contratos
   double            base_volume;            // Volume base de entrada
   double            max_contracts_per_day;  // Máximo contratos por dia (pyramiding)
   double            max_total_contracts;    // Máximo contratos total acumulado
   double            min_volume;             // Volume mínimo por trade
   double            max_volume;             // Volume máximo por trade
   
   //--- Configurações temporais
   bool              enable_daily_reset;     // Reset diário automático
   string            daily_reset_time;       // Horário do reset diário
   int               grace_period_minutes;   // Período de carência após limite
   
   //--- Flags de controle
   bool              enable_loss_limit;      // Habilitar limite de perda
   bool              enable_profit_limit;    // Habilitar limite de lucro
   bool              enable_exposure_limit;  // Habilitar limite de exposição
   bool              enable_margin_control;  // Habilitar controle de margem
};

//--- Estrutura de estatísticas de risco
struct SRiskStats
{
   //--- Estatísticas do dia
   datetime          day_start;             // Início do dia de trading
   int               trades_today;          // Trades realizados hoje
   double            pnl_today;             // P&L do dia
   double            contracts_today;       // Contratos adicionados hoje
   double            total_contracts;       // Total de contratos acumulados
   double            max_exposure_today;    // Exposição máxima hoje
   int               max_positions_today;   // Máximo de posições hoje
   int               winning_trades_today;  // Trades vencedores hoje
   int               losing_trades_today;   // Trades perdedores hoje
   
   //--- Controle de drawdown
   double            account_start_balance; // Saldo inicial da conta
   double            account_peak_balance;  // Pico de saldo
   double            current_drawdown;      // Drawdown atual
   double            max_drawdown_today;    // Drawdown máximo hoje
   double            max_drawdown_session;  // Drawdown máximo da sessão
   
   //--- Contadores de limite
   int               loss_limit_hits;       // Quantas vezes atingiu limite de perda
   int               profit_limit_hits;     // Quantas vezes atingiu limite de lucro
   int               exposure_limit_hits;   // Quantas vezes atingiu limite de exposição
   
   //--- Estado atual
   ENUM_RISK_STATE   current_state;         // Estado atual de risco
   datetime          last_state_change;     // Última mudança de estado
   string            state_reason;          // Razão da mudança de estado
};

//+------------------------------------------------------------------+
//| Classe para gestão de risco                                      |
//+------------------------------------------------------------------+

class CRiskManager
{
private:
   //--- Configurações
   SRiskConfig       m_config;
   SRiskStats        m_stats;
   string            m_symbol;
   int               m_magic_number;
   
   //--- Controle de estado
   bool              m_initialized;
   bool              m_trading_blocked;
   datetime          m_block_until;
   
   //--- Referências externas
   CLogManager      *m_logger;
   CDataValidator   *m_validator;
   
   //--- Cache de dados
   double            m_account_balance;
   double            m_account_equity;
   double            m_account_margin_free;
   double            m_account_margin_level;
   datetime          m_last_account_update;
   
public:
   //--- Construtor/Destrutor
                     CRiskManager(void);
                    ~CRiskManager(void);
   
   //--- Inicialização
   bool              Initialize(string symbol, int magic_number, SRiskConfig &config);
   void              SetLogger(CLogManager *logger) { m_logger = logger; }
   void              SetValidator(CDataValidator *validator) { m_validator = validator; }
   
   //--- Validações principais
   bool              CanOpenTrade(ENUM_ORDER_TYPE type, double volume, double price, string &reason);
   bool              CanIncreasePosition(ENUM_ORDER_TYPE type, double volume, string &reason);
   bool              ShouldClosePositions(string &reason);
   
   //--- Controle de risco
   bool              ValidateTradeRisk(ENUM_ORDER_TYPE type, double volume, double price, 
                                      double sl, double tp, string &reason);
   double            CalculateOptimalVolume(double risk_percent, double entry_price, double sl_price);
   double            CalculatePositionRisk(ENUM_ORDER_TYPE type, double volume, double price, double sl_price);
   
   //--- Monitoramento
   void              UpdateRiskStats(void);
   void              CheckRiskLimits(void);
   void              ProcessRiskEvents(void);
   
   //--- Gestão de posições
   double            GetCurrentExposure(void);
   int               GetCurrentPositionCount(void);
   double            GetTotalVolume(void);
   double            GetDailyPnL(void);
   
   //--- Estado e controle
   ENUM_RISK_STATE   GetRiskState(void) { return m_stats.current_state; }
   bool              IsTradingAllowed(void);
   bool              IsRiskLimitReached(ENUM_RISK_LIMIT_TYPE limit_type);
   
   //--- Reset e manutenção
   void              ResetDailyStats(void);
   void              RecalculateDrawdown(void);
   void              ForceUnblock(void);
   
   //--- Configuração
   void              SetConfig(SRiskConfig &config) { m_config = config; }
   SRiskConfig       GetConfig(void) { return m_config; }
   SRiskStats        GetStats(void) { return m_stats; }
   double            GetBaseVolume(void) { return m_config.base_volume; }
   
   //--- Notificações de eventos
   void              OnTradeOpened(ulong ticket, ENUM_ORDER_TYPE type, double volume, double price);
   void              OnTradeClosed(ulong ticket, double profit);
   void              OnTradeModified(ulong ticket, double sl, double tp);
   
private:
   //--- Métodos auxiliares privados
   bool              UpdateAccountInfo(void);
   bool              ValidateAccountState(string &reason);
   
   //--- Cálculos de risco
   double            CalculateTradeRisk(ENUM_ORDER_TYPE type, double volume, double price, double sl_price);
   double            CalculateMarginRequired(ENUM_ORDER_TYPE type, double volume, double price);
   double            CalculatePotentialDrawdown(double additional_risk);
   
   //--- Verificações de limite
   bool              CheckDailyLossLimit(string &reason);
   bool              CheckDailyProfitLimit(string &reason);
   bool              CheckDailyContractsLimit(double additional_contracts, string &reason);
   bool              CheckTotalContractsLimit(double additional_contracts, string &reason);
   bool              CheckMarginLevel(double additional_margin, string &reason);
   bool              CheckDrawdownLimit(double additional_risk, string &reason);
   
   //--- Gestão de estado
   void              SetRiskState(ENUM_RISK_STATE new_state, string reason);
   void              ProcessStateChange(ENUM_RISK_STATE old_state, ENUM_RISK_STATE new_state);
   
   //--- Utilitários
   bool              IsNewTradingDay(void);
   datetime          GetTradingDayStart(void);
   string            RiskStateToString(ENUM_RISK_STATE state);
   string            RiskLimitTypeToString(ENUM_RISK_LIMIT_TYPE type);
   
   //--- Métodos de posição
   double            GetCurrentPnL(void);
   int               CountPositions(void);
   double            SumPositionVolumes(void);
};

//+------------------------------------------------------------------+
//| Construtor                                                        |
//+------------------------------------------------------------------+
CRiskManager::CRiskManager(void)
{
   m_symbol = _Symbol;
   m_magic_number = 0;
   m_initialized = false;
   m_trading_blocked = false;
   m_block_until = 0;
   m_logger = NULL;
   m_validator = NULL;
   m_last_account_update = 0;
   
   // Configurações padrão conservadoras
   m_config.max_risk_per_trade = 2.0;        // 2% por trade
   m_config.max_daily_risk = 5.0;            // 5% por dia
   m_config.max_drawdown = 10.0;             // 10% drawdown máximo
   m_config.min_margin_level = 200.0;        // 200% margem mínima
   
   m_config.max_daily_loss = 0;              // Desabilitado por padrão
   m_config.max_daily_profit = 0;            // Desabilitado por padrão
   
   m_config.base_volume = 1.0;               // Volume base de entrada
   m_config.max_contracts_per_day = 10.0;    // Máximo contratos por dia
   m_config.max_total_contracts = 30.0;      // Máximo contratos total
   m_config.min_volume = 0.1;
   m_config.max_volume = 10.0;
   
   m_config.enable_daily_reset = true;
   m_config.daily_reset_time = "00:00";
   m_config.grace_period_minutes = 30;
   
   m_config.enable_loss_limit = true;
   m_config.enable_profit_limit = false;
   m_config.enable_exposure_limit = true;
   m_config.enable_margin_control = true;
   
   // Inicializar estatísticas
   ZeroMemory(m_stats);
   m_stats.current_state = RISK_STATE_NORMAL;
   m_stats.day_start = GetTradingDayStart();
   m_stats.account_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   m_stats.account_peak_balance = m_stats.account_start_balance;
}

//+------------------------------------------------------------------+
//| Destrutor                                                         |
//+------------------------------------------------------------------+
CRiskManager::~CRiskManager(void)
{
   // Log final de estatísticas se logger disponível
   if(m_logger && m_initialized)
   {
      m_logger.LogPerformance(m_stats.trades_today, m_stats.pnl_today, 
                             m_stats.trades_today > 0 ? 
                             (m_stats.pnl_today > 0 ? 100.0 : 0.0) : 0.0);
   }
}

//+------------------------------------------------------------------+
//| Inicialização do gerenciador de risco                            |
//+------------------------------------------------------------------+
bool CRiskManager::Initialize(string symbol, int magic_number, SRiskConfig &config)
{
   m_symbol = symbol;
   m_magic_number = magic_number;
   m_config = config;
   
   // Atualizar informações da conta
   if(!UpdateAccountInfo())
   {
      if(m_logger) m_logger.LogError("CRiskManager::Initialize", "Falha ao obter informações da conta");
      return false;
   }
   
   // Inicializar estatísticas
   m_stats.day_start = GetTradingDayStart();
   m_stats.account_start_balance = m_account_balance;
   m_stats.account_peak_balance = m_account_balance;
   m_stats.current_state = RISK_STATE_NORMAL;
   
   // Recalcular estatísticas atuais
   UpdateRiskStats();
   
   m_initialized = true;
   
   if(m_logger)
   {
      m_logger.LogInfo("RiskManager inicializado para " + m_symbol);
      m_logger.LogInfo("Risco máximo por trade: " + DoubleToString(m_config.max_risk_per_trade, 2) + "%");
      m_logger.LogInfo("Risco máximo diário: " + DoubleToString(m_config.max_daily_risk, 2) + "%");
      m_logger.LogInfo("Drawdown máximo: " + DoubleToString(m_config.max_drawdown, 2) + "%");
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Verificar se pode abrir trade                                    |
//+------------------------------------------------------------------+
bool CRiskManager::CanOpenTrade(ENUM_ORDER_TYPE type, double volume, double price, string &reason)
{
   if(!m_initialized)
   {
      reason = "RiskManager não inicializado";
      return false;
   }
   
   // Verificar se trading está bloqueado
   if(!IsTradingAllowed())
   {
      reason = "Trading bloqueado por controle de risco: " + m_stats.state_reason;
      return false;
   }
   
   // Atualizar informações da conta
   UpdateAccountInfo();
   
   // Validar estado da conta
   if(!ValidateAccountState(reason))
      return false;
   
   // Verificar limites de contratos
   
   // Verificar limite de exposição
   if(!CheckDailyContractsLimit(volume, reason))
      return false;
   
   if(!CheckTotalContractsLimit(volume, reason))
      return false;
   
   // Verificar margem necessária
   double margin_required = CalculateMarginRequired(type, volume, price);
   if(!CheckMarginLevel(margin_required, reason))
      return false;
   
   // Log da validação
   if(m_logger)
   {
      m_logger.LogRiskCheck("CanOpenTrade", true, 
                           EnumToString(type) + " Vol:" + DoubleToString(volume, 2) + 
                           " Price:" + DoubleToString(price, _Digits));
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Validar risco do trade                                           |
//+------------------------------------------------------------------+
bool CRiskManager::ValidateTradeRisk(ENUM_ORDER_TYPE type, double volume, double price, 
                                    double sl, double tp, string &reason)
{
   if(!m_initialized)
   {
      reason = "RiskManager não inicializado";
      return false;
   }
   
   // Calcular risco do trade
   double trade_risk = CalculateTradeRisk(type, volume, price, sl);
   double risk_percent = (trade_risk / m_account_balance) * 100.0;
   
   // Verificar risco por trade
   if(risk_percent > m_config.max_risk_per_trade)
   {
      reason = StringFormat("Risco por trade (%.2f%%) excede limite (%.2f%%)", 
                           risk_percent, m_config.max_risk_per_trade);
      
      if(m_logger) m_logger.LogRiskCheck("TradeRisk", false, reason);
      return false;
   }
   
   // Verificar risco diário acumulado
   double daily_risk_percent = ((MathAbs(m_stats.pnl_today) + trade_risk) / m_stats.account_start_balance) * 100.0;
   if(daily_risk_percent > m_config.max_daily_risk)
   {
      reason = StringFormat("Risco diário acumulado (%.2f%%) excederia limite (%.2f%%)", 
                           daily_risk_percent, m_config.max_daily_risk);
      
      if(m_logger) m_logger.LogRiskCheck("DailyRisk", false, reason);
      return false;
   }
   
   // Verificar drawdown potencial
   if(!CheckDrawdownLimit(trade_risk, reason))
      return false;
   
   // Log da validação bem-sucedida
   if(m_logger)
   {
      m_logger.LogRiskCheck("ValidateTradeRisk", true, 
                           StringFormat("Risk: %.2f%% (%.2f)", risk_percent, trade_risk));
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Calcular volume ótimo baseado no risco                           |
//+------------------------------------------------------------------+
double CRiskManager::CalculateOptimalVolume(double risk_percent, double entry_price, double sl_price)
{
   if(!m_initialized || entry_price <= 0 || sl_price <= 0)
      return 0.0;
   
   // Limitar percentual de risco ao máximo configurado
   if(risk_percent > m_config.max_risk_per_trade)
      risk_percent = m_config.max_risk_per_trade;
   
   // Calcular valor em risco
   double risk_amount = (m_account_balance * risk_percent) / 100.0;
   
   // Calcular distância do stop loss em pontos
   double sl_distance = MathAbs(entry_price - sl_price);
   if(sl_distance <= 0)
      return 0.0;
   
   // Calcular valor do ponto
   double tick_value = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_SIZE);
   
   if(tick_value <= 0 || tick_size <= 0)
      return 0.0;
   
   double point_value = tick_value * (sl_distance / tick_size);
   
   // Calcular volume
   double volume = risk_amount / point_value;
   
   // Normalizar volume
   double volume_min = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   double volume_max = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   double volume_step = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
   
   // Ajustar para step
   if(volume_step > 0)
      volume = MathFloor(volume / volume_step) * volume_step;
   
   // Aplicar limites
   volume = MathMax(volume, volume_min);
   volume = MathMin(volume, volume_max);
   volume = MathMin(volume, m_config.max_volume);
   
   return volume;
}

//+------------------------------------------------------------------+
//| Atualizar estatísticas de risco                                  |
//+------------------------------------------------------------------+
void CRiskManager::UpdateRiskStats(void)
{
   if(!m_initialized)
      return;
   
   // Verificar se é novo dia
   if(IsNewTradingDay())
      ResetDailyStats();
   
   // Atualizar informações da conta
   UpdateAccountInfo();
   
   // Recalcular P&L diário
   m_stats.pnl_today = GetDailyPnL();
   
   // Atualizar pico de saldo
   if(m_account_equity > m_stats.account_peak_balance)
      m_stats.account_peak_balance = m_account_equity;
   
   // Recalcular drawdown
   RecalculateDrawdown();
   
   // Atualizar estatísticas de contratos (já são atualizadas no OnTradeOpened)
   
   double current_exposure = GetCurrentExposure();
   if(current_exposure > m_stats.max_exposure_today)
      m_stats.max_exposure_today = current_exposure;
   
   // Verificar limites
   CheckRiskLimits();
}

//+------------------------------------------------------------------+
//| Verificar limites de risco                                       |
//+------------------------------------------------------------------+
void CRiskManager::CheckRiskLimits(void)
{
   string reason = "";
   ENUM_RISK_STATE new_state = RISK_STATE_NORMAL;
   
   // Verificar limite de perda diária
   if(m_config.enable_loss_limit)
   {
      if(!CheckDailyLossLimit(reason))
      {
         new_state = RISK_STATE_BLOCKED;
         SetRiskState(new_state, reason);
         return;
      }
   }
   
   // Verificar limite de lucro diário
   if(m_config.enable_profit_limit)
   {
      if(!CheckDailyProfitLimit(reason))
      {
         new_state = RISK_STATE_BLOCKED;
         SetRiskState(new_state, reason);
         return;
      }
   }
   
   // Verificar drawdown
   if(m_stats.current_drawdown > m_config.max_drawdown * 0.8) // 80% do limite
   {
      if(m_stats.current_drawdown > m_config.max_drawdown)
      {
         new_state = RISK_STATE_BLOCKED;
         reason = StringFormat("Drawdown (%.2f%%) excede limite (%.2f%%)", 
                              m_stats.current_drawdown, m_config.max_drawdown);
      }
      else
      {
         new_state = RISK_STATE_WARNING;
         reason = StringFormat("Drawdown (%.2f%%) próximo do limite (%.2f%%)", 
                              m_stats.current_drawdown, m_config.max_drawdown);
      }
   }
   
   // Verificar margem
   if(m_config.enable_margin_control && m_account_margin_level > 0)
   {
      if(m_account_margin_level < m_config.min_margin_level)
      {
         new_state = RISK_STATE_CRITICAL;
         reason = StringFormat("Nível de margem (%.2f%%) abaixo do mínimo (%.2f%%)", 
                              m_account_margin_level, m_config.min_margin_level);
      }
   }
   
   // Atualizar estado se necessário
   if(new_state != m_stats.current_state)
      SetRiskState(new_state, reason);
}

//+------------------------------------------------------------------+
//| MÉTODOS AUXILIARES PRIVADOS                                       |
//+------------------------------------------------------------------+

bool CRiskManager::UpdateAccountInfo(void)
{
   datetime current_time = TimeCurrent();
   
   // Atualizar apenas se necessário (cada 10 segundos)
   if(current_time - m_last_account_update < 10)
      return true;
   
   m_account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   m_account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   m_account_margin_free = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
   m_account_margin_level = AccountInfoDouble(ACCOUNT_MARGIN_LEVEL);
   
   m_last_account_update = current_time;
   
   return (m_account_balance > 0 && m_account_equity > 0);
}

double CRiskManager::CalculateTradeRisk(ENUM_ORDER_TYPE type, double volume, double price, double sl_price)
{
   if(sl_price <= 0 || price <= 0 || volume <= 0)
      return 0.0;
   
   double sl_distance = MathAbs(price - sl_price);
   double tick_value = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(m_symbol, SYMBOL_TRADE_TICK_SIZE);
   
   if(tick_value <= 0 || tick_size <= 0)
      return 0.0;
   
   double point_value = tick_value * (sl_distance / tick_size);
   return point_value * volume;
}

bool CRiskManager::CheckDailyLossLimit(string &reason)
{
   if(!m_config.enable_loss_limit)
      return true;
   
   // Verificar limite percentual
   double loss_percent = 0.0;
   if(m_stats.pnl_today < 0)
      loss_percent = (MathAbs(m_stats.pnl_today) / m_stats.account_start_balance) * 100.0;
   
   if(loss_percent > m_config.max_daily_risk)
   {
      reason = StringFormat("Perda diária (%.2f%%) excede limite (%.2f%%)", 
                           loss_percent, m_config.max_daily_risk);
      m_stats.loss_limit_hits++;
      return false;
   }
   
   // Verificar limite absoluto se configurado
   if(m_config.max_daily_loss > 0 && m_stats.pnl_today < -m_config.max_daily_loss)
   {
      reason = StringFormat("Perda diária (%.2f) excede limite absoluto (%.2f)", 
                           m_stats.pnl_today, -m_config.max_daily_loss);
      m_stats.loss_limit_hits++;
      return false;
   }
   
   return true;
}

void CRiskManager::SetRiskState(ENUM_RISK_STATE new_state, string reason)
{
   if(new_state == m_stats.current_state)
      return;
   
   ENUM_RISK_STATE old_state = m_stats.current_state;
   m_stats.current_state = new_state;
   m_stats.last_state_change = TimeCurrent();
   m_stats.state_reason = reason;
   
   // Atualizar bloqueio de trading
   if(new_state == RISK_STATE_BLOCKED || new_state == RISK_STATE_CRITICAL)
   {
      m_trading_blocked = true;
      m_block_until = TimeCurrent() + (m_config.grace_period_minutes * 60);
   }
   else
   {
      m_trading_blocked = false;
      m_block_until = 0;
   }
   
   // Log da mudança de estado
   if(m_logger)
   {
      m_logger.LogWarning("RiskManager", 
                         StringFormat("Estado alterado: %s -> %s | Razão: %s", 
                                     RiskStateToString(old_state), 
                                     RiskStateToString(new_state), reason));
   }
   
   // Processar mudança de estado
   ProcessStateChange(old_state, new_state);
}

bool CRiskManager::IsTradingAllowed(void)
{
   // Verificar se está bloqueado temporariamente
   if(m_trading_blocked && TimeCurrent() < m_block_until)
      return false;
   
   // Desbloquear se período de carência passou
   if(m_trading_blocked && TimeCurrent() >= m_block_until)
   {
      m_trading_blocked = false;
      m_block_until = 0;
      
      if(m_logger)
         m_logger.LogInfo("Trading desbloqueado automaticamente após período de carência");
   }
   
   return !m_trading_blocked && m_stats.current_state != RISK_STATE_BLOCKED;
}

datetime CRiskManager::GetTradingDayStart(void)
{
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   dt.hour = 0;
   dt.min = 0;
   dt.sec = 0;
   return StructToTime(dt);
}

string CRiskManager::RiskStateToString(ENUM_RISK_STATE state)
{
   switch(state)
   {
      case RISK_STATE_NORMAL: return "Normal";
      case RISK_STATE_WARNING: return "Aviso";
      case RISK_STATE_CRITICAL: return "Crítico";
      case RISK_STATE_BLOCKED: return "Bloqueado";
      default: return "Desconhecido";
   }
}

//+------------------------------------------------------------------+
//| Verificar se deve fechar posições                               |
//+------------------------------------------------------------------+
bool CRiskManager::ShouldClosePositions(string &reason)
{
   if(!m_initialized)
   {
      reason = "RiskManager não inicializado";
      return false;
   }
   
   // Atualizar informações
   UpdateAccountInfo();
   UpdateRiskStats();
   
   // Verificar drawdown crítico
   if(m_stats.current_drawdown > m_config.max_drawdown)
   {
      reason = StringFormat("Drawdown crítico (%.2f%%) - fechando todas as posições", 
                           m_stats.current_drawdown);
      return true;
   }
   
   // Verificar perda diária crítica
   if(m_config.enable_loss_limit)
   {
      double loss_percent = 0.0;
      if(m_stats.pnl_today < 0)
         loss_percent = (MathAbs(m_stats.pnl_today) / m_stats.account_start_balance) * 100.0;
      
      if(loss_percent > m_config.max_daily_risk * 1.2) // 120% do limite
      {
         reason = StringFormat("Perda diária crítica (%.2f%%) - fechando posições", loss_percent);
         return true;
      }
      
      if(m_config.max_daily_loss > 0 && m_stats.pnl_today < -m_config.max_daily_loss * 1.2)
      {
         reason = StringFormat("Perda absoluta crítica (%.2f) - fechando posições", m_stats.pnl_today);
         return true;
      }
   }
   
   // Verificar nível de margem crítico
   if(m_config.enable_margin_control && m_account_margin_level > 0)
   {
      if(m_account_margin_level < m_config.min_margin_level * 0.5) // 50% do mínimo
      {
         reason = StringFormat("Nível de margem crítico (%.2f%%) - fechando posições", 
                              m_account_margin_level);
         return true;
      }
   }
   
   // Verificar se equity está muito abaixo do balance
   double equity_ratio = m_account_equity / m_account_balance;
   if(equity_ratio < 0.8) // Equity 20% abaixo do balance
   {
      reason = StringFormat("Equity muito baixa (%.2f%% do balance) - fechando posições", 
                           equity_ratio * 100);
      return true;
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Obter exposição atual total                                     |
//+------------------------------------------------------------------+
double CRiskManager::GetCurrentExposure(void)
{
   double total_exposure = 0.0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0 && PositionSelectByTicket(ticket))
      {
         // Verificar se é nossa posição (mesmo símbolo e magic number)
         if(PositionGetString(POSITION_SYMBOL) == m_symbol && 
            PositionGetInteger(POSITION_MAGIC) == m_magic_number)
         {
            double volume = PositionGetDouble(POSITION_VOLUME);
            double price = PositionGetDouble(POSITION_PRICE_OPEN);
            total_exposure += volume * price;
         }
      }
   }
   
   return total_exposure;
}

//+------------------------------------------------------------------+
//| Obter contagem de posições atuais                               |
//+------------------------------------------------------------------+
int CRiskManager::GetCurrentPositionCount(void)
{
   int count = 0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0 && PositionSelectByTicket(ticket))
      {
         // Verificar se é nossa posição (mesmo símbolo e magic number)
         if(PositionGetString(POSITION_SYMBOL) == m_symbol && 
            PositionGetInteger(POSITION_MAGIC) == m_magic_number)
         {
            count++;
         }
      }
   }
   
   return count;
}

//+------------------------------------------------------------------+
//| Obter volume total das posições                                 |
//+------------------------------------------------------------------+
double CRiskManager::GetTotalVolume(void)
{
   double total_volume = 0.0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0 && PositionSelectByTicket(ticket))
      {
         // Verificar se é nossa posição (mesmo símbolo e magic number)
         if(PositionGetString(POSITION_SYMBOL) == m_symbol && 
            PositionGetInteger(POSITION_MAGIC) == m_magic_number)
         {
            total_volume += PositionGetDouble(POSITION_VOLUME);
         }
      }
   }
   
   return total_volume;
}

//+------------------------------------------------------------------+
//| Obter P&L diário                                               |
//+------------------------------------------------------------------+
double CRiskManager::GetDailyPnL(void)
{
   double daily_pnl = 0.0;
   datetime day_start = GetTradingDayStart();
   
   // Somar P&L das posições abertas
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0 && PositionSelectByTicket(ticket))
      {
         // Verificar se é nossa posição (mesmo símbolo e magic number)
         if(PositionGetString(POSITION_SYMBOL) == m_symbol && 
            PositionGetInteger(POSITION_MAGIC) == m_magic_number)
         {
            datetime open_time = (datetime)PositionGetInteger(POSITION_TIME);
            if(open_time >= day_start)
            {
               daily_pnl += PositionGetDouble(POSITION_PROFIT);
            }
         }
      }
   }
   
   // Somar P&L das posições fechadas hoje
   HistorySelect(day_start, TimeCurrent());
   int deals_total = HistoryDealsTotal();
   
   for(int i = 0; i < deals_total; i++)
   {
      ulong ticket = HistoryDealGetTicket(i);
      if(ticket > 0)
      {
         if(HistoryDealGetString(ticket, DEAL_SYMBOL) == m_symbol &&
            HistoryDealGetInteger(ticket, DEAL_MAGIC) == m_magic_number &&
            HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
         {
            daily_pnl += HistoryDealGetDouble(ticket, DEAL_PROFIT);
         }
      }
   }
   
   return daily_pnl;
}

//+------------------------------------------------------------------+
//| Verificar limite de lucro diário                                |
//+------------------------------------------------------------------+
bool CRiskManager::CheckDailyProfitLimit(string &reason)
{
   if(!m_config.enable_profit_limit)
      return true;
   
   // Verificar limite percentual
   double profit_percent = 0.0;
   if(m_stats.pnl_today > 0)
      profit_percent = (m_stats.pnl_today / m_stats.account_start_balance) * 100.0;
   
   if(profit_percent > m_config.max_daily_risk)
   {
      reason = StringFormat("Lucro diário (%.2f%%) excede limite (%.2f%%) - parando trading", 
                           profit_percent, m_config.max_daily_risk);
      return false;
   }
   
   // Verificar limite absoluto se configurado
   if(m_config.max_daily_profit > 0 && m_stats.pnl_today > m_config.max_daily_profit)
   {
      reason = StringFormat("Lucro diário (%.2f) excede limite absoluto (%.2f) - parando trading", 
                           m_stats.pnl_today, m_config.max_daily_profit);
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Verificar limite de contratos diários                           |
//+------------------------------------------------------------------+
bool CRiskManager::CheckDailyContractsLimit(double additional_contracts, string &reason)
{
   if(m_config.max_contracts_per_day <= 0)
      return true;
   
   double contracts_after = m_stats.contracts_today + additional_contracts;
   
   if(contracts_after > m_config.max_contracts_per_day)
   {
      reason = StringFormat("Contratos hoje (%.2f + %.2f = %.2f) excede limite diário (%.2f)", 
                           m_stats.contracts_today, additional_contracts, contracts_after, m_config.max_contracts_per_day);
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Verificar limite de contratos totais                            |
//+------------------------------------------------------------------+
bool CRiskManager::CheckTotalContractsLimit(double additional_contracts, string &reason)
{
   if(m_config.max_total_contracts <= 0)
      return true;
   
   double total_after = m_stats.total_contracts + additional_contracts;
   
   if(total_after > m_config.max_total_contracts)
   {
      reason = StringFormat("Contratos totais (%.2f + %.2f = %.2f) excede limite total (%.2f)", 
                           m_stats.total_contracts, additional_contracts, total_after, m_config.max_total_contracts);
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Verificar nível de margem                                       |
//+------------------------------------------------------------------+
bool CRiskManager::CheckMarginLevel(double additional_margin, string &reason)
{
   if(!m_config.enable_margin_control)
      return true;
   
   double current_margin = AccountInfoDouble(ACCOUNT_MARGIN);
   double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
   
   if(free_margin < additional_margin)
   {
      reason = StringFormat("Margem livre (%.2f) insuficiente para margem adicional (%.2f)", 
                           free_margin, additional_margin);
      return false;
   }
   
   // Simular novo nível de margem
   double new_margin_level = 0;
   if(current_margin + additional_margin > 0)
   {
      new_margin_level = (AccountInfoDouble(ACCOUNT_EQUITY) / (current_margin + additional_margin)) * 100.0;
   }
   
   if(new_margin_level < m_config.min_margin_level)
   {
      reason = StringFormat("Novo nível de margem (%.2f%%) ficaria abaixo do mínimo (%.2f%%)", 
                           new_margin_level, m_config.min_margin_level);
      return false;
   }
   
   return true;
}



//+------------------------------------------------------------------+
//| Verificar limite de drawdown                                    |
//+------------------------------------------------------------------+
bool CRiskManager::CheckDrawdownLimit(double additional_risk, string &reason)
{
   double potential_drawdown = CalculatePotentialDrawdown(additional_risk);
   
   if(potential_drawdown > m_config.max_drawdown)
   {
      reason = StringFormat("Drawdown potencial (%.2f%%) excederia limite (%.2f%%)", 
                           potential_drawdown, m_config.max_drawdown);
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Processar eventos de risco                                      |
//+------------------------------------------------------------------+
void CRiskManager::ProcessRiskEvents(void)
{
   // Esta função pode ser expandida para processar eventos específicos
   // Por enquanto, apenas verifica se precisa resetar estatísticas diárias
   
   if(m_config.enable_daily_reset && IsNewTradingDay())
   {
      ResetDailyStats();
      if(m_logger)
         m_logger.LogInfo("Estatísticas diárias resetadas automaticamente");
   }
}

//+------------------------------------------------------------------+
//| Processar mudança de estado                                     |
//+------------------------------------------------------------------+
void CRiskManager::ProcessStateChange(ENUM_RISK_STATE old_state, ENUM_RISK_STATE new_state)
{
   // Implementar ações específicas baseadas na mudança de estado
   
   if(new_state == RISK_STATE_BLOCKED || new_state == RISK_STATE_CRITICAL)
   {
      // Estados críticos - podem requer ações especiais
      if(m_logger)
         m_logger.LogWarning("ProcessStateChange", 
                            "Estado crítico detectado: " + RiskStateToString(new_state));
   }
   
   if(old_state == RISK_STATE_BLOCKED && new_state == RISK_STATE_NORMAL)
   {
      // Retornando ao normal após bloqueio
      if(m_logger)
         m_logger.LogInfo("Trading desbloqueado - retornando ao estado normal");
   }
   
   // Aqui podem ser adicionadas notificações, emails, etc.
}

//+------------------------------------------------------------------+
//| Calcular drawdown potencial                                     |
//+------------------------------------------------------------------+
double CRiskManager::CalculatePotentialDrawdown(double additional_risk)
{
   double current_drawdown = m_stats.current_drawdown;
   double risk_percent = (additional_risk / m_stats.account_start_balance) * 100.0;
   
   return current_drawdown + risk_percent;
}

//+------------------------------------------------------------------+
//| Calcular margem necessária                                      |
//+------------------------------------------------------------------+
double CRiskManager::CalculateMarginRequired(ENUM_ORDER_TYPE type, double volume, double price)
{
   // Calcular margem usando informações do símbolo
   double margin_required = 0.0;
   
   if(!OrderCalcMargin(type, m_symbol, volume, price, margin_required))
   {
      // Se falhar, usar cálculo aproximado
      double margin_rate = SymbolInfoDouble(m_symbol, SYMBOL_MARGIN_INITIAL);
      if(margin_rate > 0)
         margin_required = volume * price * margin_rate;
      else
         margin_required = volume * price * 0.01; // 1% como fallback
   }
   
   return margin_required;
}

//+------------------------------------------------------------------+
//| Verificar se é um novo dia de trading                           |
//+------------------------------------------------------------------+
bool CRiskManager::IsNewTradingDay(void)
{
   datetime current_day_start = GetTradingDayStart();
   return (current_day_start > m_stats.day_start);
}

//+------------------------------------------------------------------+
//| Resetar estatísticas diárias                                    |
//+------------------------------------------------------------------+
void CRiskManager::ResetDailyStats(void)
{
   m_stats.day_start = GetTradingDayStart();
   m_stats.trades_today = 0;
   m_stats.pnl_today = 0.0;
   m_stats.contracts_today = 0.0;
   m_stats.max_positions_today = 0;
   m_stats.winning_trades_today = 0;
   m_stats.losing_trades_today = 0;
   m_stats.account_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   
   if(m_logger)
      m_logger.LogInfo("Estatísticas diárias resetadas - contratos hoje: 0");
}

//+------------------------------------------------------------------+
//| Recalcular drawdown atual                                       |
//+------------------------------------------------------------------+
void CRiskManager::RecalculateDrawdown(void)
{
   double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double peak_balance = m_stats.account_peak_balance;
   
   // Atualizar pico se necessário
   if(current_balance > peak_balance)
   {
      m_stats.account_peak_balance = current_balance;
      peak_balance = current_balance;
   }
   
   // Calcular drawdown atual
   if(peak_balance > 0)
   {
      m_stats.current_drawdown = ((peak_balance - current_balance) / peak_balance) * 100.0;
   }
   else
   {
      m_stats.current_drawdown = 0.0;
   }
   
   // Atualizar máximo drawdown se necessário
   if(m_stats.current_drawdown > m_stats.max_drawdown_session)
      m_stats.max_drawdown_session = m_stats.current_drawdown;
}

//+------------------------------------------------------------------+
//| Forçar desbloqueio do trading                                   |
//+------------------------------------------------------------------+
void CRiskManager::ForceUnblock(void)
{
   m_trading_blocked = false;
   m_block_until = 0;
   
   // Apenas desbloquear, não mudar o estado de risco
   if(m_logger)
      m_logger.LogWarning("ForceUnblock", "Trading desbloqueado manualmente");
}

//+------------------------------------------------------------------+
//| Validar estado da conta                                         |
//+------------------------------------------------------------------+
bool CRiskManager::ValidateAccountState(string &reason)
{
   // Verificar se conta está ativa
   if(!AccountInfoInteger(ACCOUNT_TRADE_ALLOWED))
   {
      reason = "Trading não permitido na conta";
      return false;
   }
   
   // Verificar se há dinheiro suficiente
   double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
   if(free_margin <= 0)
   {
      reason = "Margem livre insuficiente";
      return false;
   }
   
   // Verificar se terminal está conectado
   if(!TerminalInfoInteger(TERMINAL_CONNECTED))
   {
      reason = "Terminal não conectado";
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Notificação de trade aberto                                     |
//+------------------------------------------------------------------+
void CRiskManager::OnTradeOpened(ulong ticket, ENUM_ORDER_TYPE type, double volume, double price)
{
   m_stats.trades_today++;
   m_stats.contracts_today += volume;
   m_stats.total_contracts += volume;
   
   int current_positions = GetCurrentPositionCount();
   if(current_positions > m_stats.max_positions_today)
      m_stats.max_positions_today = current_positions;
   
   if(m_logger)
   {
      m_logger.LogPositionUpdate("OPENED", ticket, price, volume, 0.0);
      m_logger.LogInfo(StringFormat("Contratos: hoje=%.2f, total=%.2f", m_stats.contracts_today, m_stats.total_contracts));
   }
   
   // Atualizar estatísticas
   UpdateRiskStats();
}

//+------------------------------------------------------------------+
//| Notificação de trade fechado                                    |
//+------------------------------------------------------------------+
void CRiskManager::OnTradeClosed(ulong ticket, double profit)
{
   m_stats.pnl_today += profit;
   
   if(profit > 0)
      m_stats.winning_trades_today++;
   else if(profit < 0)
      m_stats.losing_trades_today++;
   
   if(m_logger)
   {
      m_logger.LogPositionUpdate("CLOSED", ticket, 0.0, 0.0, profit);
   }
   
   // Recalcular drawdown após fechamento
   RecalculateDrawdown();
   
   // Atualizar estatísticas
   UpdateRiskStats();
}

//+------------------------------------------------------------------+
//| Notificação de trade modificado                                 |
//+------------------------------------------------------------------+
void CRiskManager::OnTradeModified(ulong ticket, double sl, double tp)
{
   if(m_logger)
   {
      m_logger.LogPositionUpdate("MODIFIED", ticket, 0.0, 0.0, 0.0);
   }
   
   // Pode afetar o risco, então atualizar estatísticas
   UpdateRiskStats();
}

//+------------------------------------------------------------------+
//| Converter tipo de limite de risco para string                   |
//+------------------------------------------------------------------+
string CRiskManager::RiskLimitTypeToString(ENUM_RISK_LIMIT_TYPE type)
{
   switch(type)
   {
      case RISK_LIMIT_DAILY_LOSS: return "Perda Diária";
      case RISK_LIMIT_DAILY_PROFIT: return "Lucro Diário";
      case RISK_LIMIT_MAX_EXPOSURE: return "Exposição Máxima";
      case RISK_LIMIT_MAX_TRADES: return "Máximo Trades";
      case RISK_LIMIT_DRAWDOWN: return "Drawdown";
      case RISK_LIMIT_MARGIN: return "Margem";
      case RISK_LIMIT_EXPOSURE: return "Limite Exposição";
      case RISK_LIMIT_TRADE_COUNT: return "Contagem de Trades";
      default: return "Desconhecido";
   }
}

//+------------------------------------------------------------------+
//| Obter P&L atual de todas as posições                           |
//+------------------------------------------------------------------+
double CRiskManager::GetCurrentPnL(void)
{
   double total_pnl = 0.0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0 && PositionSelectByTicket(ticket))
      {
         // Verificar se é nossa posição (mesmo símbolo e magic number)
         if(PositionGetString(POSITION_SYMBOL) == m_symbol && 
            PositionGetInteger(POSITION_MAGIC) == m_magic_number)
         {
            total_pnl += PositionGetDouble(POSITION_PROFIT);
         }
      }
   }
   
   return total_pnl;
}

//+------------------------------------------------------------------+
//| Contar posições atuais                                         |
//+------------------------------------------------------------------+
int CRiskManager::CountPositions(void)
{
   // Esta função é idêntica à GetCurrentPositionCount, mantida por compatibilidade
   return GetCurrentPositionCount();
}

//+------------------------------------------------------------------+
//| Somar volumes das posições                                     |
//+------------------------------------------------------------------+
double CRiskManager::SumPositionVolumes(void)
{
   // Esta função é idêntica à GetTotalVolume, mantida por compatibilidade
   return GetTotalVolume();
} 