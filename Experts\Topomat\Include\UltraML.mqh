//+------------------------------------------------------------------+
//|                                            UltraML.mqh           |
//|                  Sistema ML Ultra Avançado Otimizado             |
//|                   Rede Neural + Ensemble + Deep Learning         |
//+------------------------------------------------------------------+

#include <Files/File.mqh>
#include <Math/Stat/Math.mqh>

//+------------------------------------------------------------------+
//| CONFIGURAÇÕES ULTRA AVANÇADAS                                    |
//+------------------------------------------------------------------+
#define ULTRA_FEATURES 15
#define ULTRA_HIDDEN 10
#define ULTRA_MODELS 3
#define ULTRA_SAMPLES 1000

//+------------------------------------------------------------------+
//| ESTRUTURAS OTIMIZADAS                                            |
//+------------------------------------------------------------------+
struct UltraFeatures
{
    double volume_ratio, price_velocity, price_acceleration;
    double volatility_ratio, trend_strength, rsi_value;
    double bollinger_pos, macd_signal, stochastic_val;
    double support_resistance, fibonacci_level, candlestick_pattern;
    double bid_ask_imbalance, intraday_pattern, momentum_score;
};

struct UltraModel
{
    double input_weights[ULTRA_FEATURES][ULTRA_HIDDEN];
    double hidden_weights[ULTRA_HIDDEN];
    double hidden_bias[ULTRA_HIDDEN];
    double output_bias;
    double learning_rate;
    double accuracy;
};

struct UltraEnsemble
{
    UltraModel models[ULTRA_MODELS];
    double model_weights[ULTRA_MODELS];
    double ensemble_accuracy;
    bool is_trained;
};

//+------------------------------------------------------------------+
//| CLASSE ULTRA ML                                                  |
//+------------------------------------------------------------------+
class CUltraML
{
private:
    UltraFeatures m_features[ULTRA_SAMPLES];
    double m_labels[ULTRA_SAMPLES];
    int m_count;
    UltraEnsemble m_ensemble;
    string m_symbol;
    
public:
    CUltraML() { m_count = 0; ZeroMemory(m_ensemble); }
    
    bool Initialize(string symbol)
    {
        m_symbol = symbol;
        Print("🚀 Inicializando Ultra ML System...");
        
        if(!LoadModel())
        {
            CollectData();
            TrainSystem();
            SaveModel();
        }
        
        Print("✅ Ultra ML inicializado - Precisão: ", 
              DoubleToString(m_ensemble.ensemble_accuracy, 2), "%");
        return true;
    }
    
    double PredictUltra(const UltraFeatures &features)
    {
        if(!m_ensemble.is_trained) return 0.5;
        
        double prediction = 0.0;
        for(int i = 0; i < ULTRA_MODELS; i++)
        {
            prediction += PredictModel(m_ensemble.models[i], features) * m_ensemble.model_weights[i];
        }
        return prediction;
    }
    
    UltraFeatures ExtractFeatures()
    {
        UltraFeatures features;
        ZeroMemory(features);
        
        MqlRates rates[];
        if(CopyRates(m_symbol, PERIOD_M1, 0, 50, rates) < 50) return features;
        
        double prices[50], volumes[50];
        for(int i = 0; i < 50; i++)
        {
            prices[i] = rates[i].close;
            volumes[i] = (double)rates[i].tick_volume;
        }
        
        features.volume_ratio = volumes[49] / CalcMean(volumes, 50);
        features.price_velocity = prices[49] - prices[48];
        features.price_acceleration = (prices[49] - prices[48]) - (prices[48] - prices[47]);
        features.volatility_ratio = (rates[49].high - rates[49].low) / CalcATR(rates, 49, 14);
        features.trend_strength = CalcTrend(prices, 50);
        features.rsi_value = CalcRSI(prices, 50, 14);
        features.bollinger_pos = CalcBollinger(prices, 50);
        features.macd_signal = CalcMACD(prices, 50);
        features.stochastic_val = CalcStochastic(rates, 49, 14);
        features.support_resistance = CalcSR(prices, 50);
        features.fibonacci_level = CalcFib(prices, 50);
        features.candlestick_pattern = AnalyzeCandles(rates, 49);
        features.bid_ask_imbalance = CalcImbalance();
        features.intraday_pattern = CalcIntradayPattern();
        features.momentum_score = CalcMomentum(prices, 50);
        
        return features;
    }
    
    double GetAccuracy() { return m_ensemble.ensemble_accuracy; }
    double GetEnsembleAccuracy() { return m_ensemble.ensemble_accuracy; }
    bool TrainEnsembleModel() { return TrainSystem(); }
    
    bool SaveModel()
    {
        int file = FileOpen("UltraML_" + m_symbol + ".dat", FILE_WRITE | FILE_BIN);
        if(file == INVALID_HANDLE) return false;
        
        FileWriteStruct(file, m_ensemble);
        FileClose(file);
        return true;
    }
    
private:
    void CollectData()
    {
        MqlRates rates[];
        int count = CopyRates(m_symbol, PERIOD_M1, 0, 5000, rates);
        
        m_count = 0;
        for(int i = 50; i < count - 1 && m_count < ULTRA_SAMPLES; i++)
        {
            UltraFeatures features = ExtractHistoricalFeatures(rates, i);
            double label = (rates[i + 1].close > rates[i].close) ? 1.0 : 0.0;
            
            m_features[m_count] = features;
            m_labels[m_count] = label;
            m_count++;
        }
        
        Print("📊 Coletadas ", m_count, " amostras");
    }
    
    bool TrainSystem()
    {
        Print("🎯 Treinando Ultra ML System...");
        
        double total_acc = 0.0;
        for(int i = 0; i < ULTRA_MODELS; i++)
        {
            InitializeModel(m_ensemble.models[i]);
            TrainModel(m_ensemble.models[i]);
            
            double acc = TestModel(m_ensemble.models[i]);
            m_ensemble.models[i].accuracy = acc;
            total_acc += acc;
            
            Print("🧠 Modelo ", i + 1, " - Precisão: ", DoubleToString(acc, 2), "%");
        }
        
        // Calcular pesos
        for(int i = 0; i < ULTRA_MODELS; i++)
        {
            m_ensemble.model_weights[i] = m_ensemble.models[i].accuracy / total_acc;
        }
        
        m_ensemble.ensemble_accuracy = total_acc / ULTRA_MODELS;
        m_ensemble.is_trained = true;
        
        Print("🏆 Sistema treinado - Precisão: ", DoubleToString(m_ensemble.ensemble_accuracy, 2), "%");
        return true;
    }
    
    void InitializeModel(UltraModel &model)
    {
        for(int i = 0; i < ULTRA_FEATURES; i++)
        {
            for(int j = 0; j < ULTRA_HIDDEN; j++)
            {
                model.input_weights[i][j] = (MathRand() / 16383.5 - 1.0) * 0.5;
            }
        }
        
        for(int i = 0; i < ULTRA_HIDDEN; i++)
        {
            model.hidden_weights[i] = (MathRand() / 16383.5 - 1.0) * 0.5;
            model.hidden_bias[i] = 0.0;
        }
        
        model.output_bias = 0.0;
        model.learning_rate = 0.001;
    }
    
    void TrainModel(UltraModel &model)
    {
        for(int epoch = 0; epoch < 100; epoch++)
        {
            for(int sample = 0; sample < m_count; sample++)
            {
                double features[ULTRA_FEATURES];
                ConvertToArray(m_features[sample], features);
                
                // Forward pass
                double hidden[ULTRA_HIDDEN];
                for(int h = 0; h < ULTRA_HIDDEN; h++)
                {
                    double sum = model.hidden_bias[h];
                    for(int f = 0; f < ULTRA_FEATURES; f++)
                    {
                        sum += features[f] * model.input_weights[f][h];
                    }
                    hidden[h] = Sigmoid(sum);
                }
                
                double output_sum = model.output_bias;
                for(int h = 0; h < ULTRA_HIDDEN; h++)
                {
                    output_sum += hidden[h] * model.hidden_weights[h];
                }
                double output = Sigmoid(output_sum);
                
                // Backward pass
                double target = m_labels[sample];
                double error = output - target;
                double output_grad = error * output * (1.0 - output);
                
                for(int h = 0; h < ULTRA_HIDDEN; h++)
                {
                    double hidden_grad = output_grad * model.hidden_weights[h] * hidden[h] * (1.0 - hidden[h]);
                    
                    model.hidden_weights[h] -= model.learning_rate * output_grad * hidden[h];
                    model.hidden_bias[h] -= model.learning_rate * hidden_grad;
                    
                    for(int f = 0; f < ULTRA_FEATURES; f++)
                    {
                        model.input_weights[f][h] -= model.learning_rate * hidden_grad * features[f];
                    }
                }
                
                model.output_bias -= model.learning_rate * output_grad;
            }
        }
    }
    
    double TestModel(const UltraModel &model)
    {
        int correct = 0;
        for(int i = 0; i < MathMin(m_count, 200); i++)
        {
            double prediction = PredictModel(model, m_features[i]);
            bool pred_class = prediction > 0.5;
            bool actual_class = m_labels[i] > 0.5;
            
            if(pred_class == actual_class) correct++;
        }
        
        return (double)correct / MathMin(m_count, 200) * 100.0;
    }
    
    double PredictModel(const UltraModel &model, const UltraFeatures &features)
    {
        double feature_array[ULTRA_FEATURES];
        ConvertToArray(features, feature_array);
        
        double hidden[ULTRA_HIDDEN];
        for(int h = 0; h < ULTRA_HIDDEN; h++)
        {
            double sum = model.hidden_bias[h];
            for(int f = 0; f < ULTRA_FEATURES; f++)
            {
                sum += feature_array[f] * model.input_weights[f][h];
            }
            hidden[h] = Sigmoid(sum);
        }
        
        double output_sum = model.output_bias;
        for(int h = 0; h < ULTRA_HIDDEN; h++)
        {
            output_sum += hidden[h] * model.hidden_weights[h];
        }
        
        return Sigmoid(output_sum);
    }
    
    void ConvertToArray(const UltraFeatures &features, double &array[])
    {
        array[0] = features.volume_ratio;
        array[1] = features.price_velocity;
        array[2] = features.price_acceleration;
        array[3] = features.volatility_ratio;
        array[4] = features.trend_strength;
        array[5] = features.rsi_value;
        array[6] = features.bollinger_pos;
        array[7] = features.macd_signal;
        array[8] = features.stochastic_val;
        array[9] = features.support_resistance;
        array[10] = features.fibonacci_level;
        array[11] = features.candlestick_pattern;
        array[12] = features.bid_ask_imbalance;
        array[13] = features.intraday_pattern;
        array[14] = features.momentum_score;
    }
    
    UltraFeatures ExtractHistoricalFeatures(const MqlRates &rates[], int index)
    {
        UltraFeatures features;
        ZeroMemory(features);
        
        if(index < 50) return features;
        
        double prices[50], volumes[50];
        for(int i = 0; i < 50; i++)
        {
            prices[i] = rates[index - 49 + i].close;
            volumes[i] = (double)rates[index - 49 + i].tick_volume;
        }
        
        features.volume_ratio = volumes[49] / CalcMean(volumes, 50);
        features.price_velocity = prices[49] - prices[48];
        features.price_acceleration = (prices[49] - prices[48]) - (prices[48] - prices[47]);
        features.volatility_ratio = (rates[index].high - rates[index].low) / CalcATR(rates, index, 14);
        features.trend_strength = CalcTrend(prices, 50);
        features.rsi_value = CalcRSI(prices, 50, 14);
        features.bollinger_pos = CalcBollinger(prices, 50);
        features.macd_signal = CalcMACD(prices, 50);
        features.stochastic_val = CalcStochastic(rates, index, 14);
        features.support_resistance = CalcSR(prices, 50);
        features.fibonacci_level = CalcFib(prices, 50);
        features.candlestick_pattern = AnalyzeCandles(rates, index);
        features.bid_ask_imbalance = CalcImbalance();
        features.intraday_pattern = CalcIntradayPattern();
        features.momentum_score = CalcMomentum(prices, 50);
        
        return features;
    }
    
    double Sigmoid(double x) { return 1.0 / (1.0 + MathExp(-x)); }
    
    bool LoadModel()
    {
        int file = FileOpen("UltraML_" + m_symbol + ".dat", FILE_READ | FILE_BIN);
        if(file == INVALID_HANDLE) return false;
        
        FileReadStruct(file, m_ensemble);
        FileClose(file);
        return true;
    }
};

//+------------------------------------------------------------------+
//| FUNÇÕES AUXILIARES OTIMIZADAS                                    |
//+------------------------------------------------------------------+
double CalcMean(const double &array[], int size)
{
    double sum = 0.0;
    for(int i = 0; i < size; i++) sum += array[i];
    return sum / size;
}

double CalcATR(const MqlRates &rates[], int index, int period)
{
    double atr = 0.0;
    for(int i = MathMax(1, index - period + 1); i <= index; i++)
    {
        double tr = MathMax(rates[i].high - rates[i].low, 
                   MathMax(MathAbs(rates[i].high - rates[i-1].close),
                          MathAbs(rates[i].low - rates[i-1].close)));
        atr += tr;
    }
    return atr / period;
}

double CalcTrend(const double &prices[], int size)
{
    int up = 0;
    for(int i = 1; i < size; i++)
    {
        if(prices[i] > prices[i-1]) up++;
    }
    return (double)up / (size - 1);
}

double CalcRSI(const double &prices[], int size, int period)
{
    double gain = 0, loss = 0;
    for(int i = size - period; i < size; i++)
    {
        if(i <= 0) continue;
        double diff = prices[i] - prices[i-1];
        if(diff > 0) gain += diff;
        else loss -= diff;
    }
    gain /= period;
    loss /= period;
    return (loss == 0) ? 100.0 : 100.0 - (100.0 / (1.0 + gain / loss));
}

double CalcBollinger(const double &prices[], int size)
{
    double sma = CalcMean(prices, 20);
    double std = 0.0;
    for(int i = size - 20; i < size; i++)
    {
        double diff = prices[i] - sma;
        std += diff * diff;
    }
    std = MathSqrt(std / 20);
    
    double upper = sma + 2 * std;
    double lower = sma - 2 * std;
    
    return (upper == lower) ? 0.5 : (prices[size-1] - lower) / (upper - lower);
}

double CalcMACD(const double &prices[], int size)
{
    double ema12 = prices[size-1];
    double ema26 = prices[size-1];
    
    for(int i = size - 12; i < size; i++)
    {
        ema12 = ema12 * 0.8462 + prices[i] * 0.1538;
    }
    
    for(int i = size - 26; i < size; i++)
    {
        ema26 = ema26 * 0.9259 + prices[i] * 0.0741;
    }
    
    return ema12 - ema26;
}

double CalcStochastic(const MqlRates &rates[], int index, int period)
{
    double highest = rates[index].high;
    double lowest = rates[index].low;
    
    for(int i = index - period + 1; i <= index; i++)
    {
        if(rates[i].high > highest) highest = rates[i].high;
        if(rates[i].low < lowest) lowest = rates[i].low;
    }
    
    return (highest == lowest) ? 50.0 : 
           (rates[index].close - lowest) / (highest - lowest) * 100.0;
}

double CalcSR(const double &prices[], int size)
{
    double highest = prices[0];
    double lowest = prices[0];
    
    for(int i = 1; i < size; i++)
    {
        if(prices[i] > highest) highest = prices[i];
        if(prices[i] < lowest) lowest = prices[i];
    }
    
    double range = highest - lowest;
    return (range == 0) ? 0.5 : (prices[size-1] - lowest) / range;
}

double CalcFib(const double &prices[], int size)
{
    double high = prices[0];
    double low = prices[0];
    
    for(int i = 1; i < size; i++)
    {
        if(prices[i] > high) high = prices[i];
        if(prices[i] < low) low = prices[i];
    }
    
    double range = high - low;
    if(range == 0) return 0.5;
    
    double current = prices[size-1];
    double fib618 = low + range * 0.618;
    double fib382 = low + range * 0.382;
    
    double dist618 = MathAbs(current - fib618);
    double dist382 = MathAbs(current - fib382);
    
    return 1.0 - MathMin(dist618, dist382) / range;
}

double AnalyzeCandles(const MqlRates &rates[], int index)
{
    double score = 0.5;
    
    if(index < 2) return score;
    
    double body = MathAbs(rates[index].close - rates[index].open);
    double range = rates[index].high - rates[index].low;
    
    if(range == 0) return score;
    
    // Doji
    if(body < range * 0.1) score = 0.3;
    
    // Hammer
    double lower_shadow = MathMin(rates[index].open, rates[index].close) - rates[index].low;
    if(lower_shadow > body * 2) score = 0.7;
    
    // Shooting star
    double upper_shadow = rates[index].high - MathMax(rates[index].open, rates[index].close);
    if(upper_shadow > body * 2) score = 0.3;
    
    return score;
}

double CalcImbalance()
{
    MqlTick tick;
    if(!SymbolInfoTick(_Symbol, tick)) return 0.0;
    
    double spread = tick.ask - tick.bid;
    double mid = (tick.ask + tick.bid) / 2.0;
    
    return (spread == 0) ? 0.0 : (tick.last - mid) / spread;
}

double CalcIntradayPattern()
{
    MqlDateTime dt;
    TimeCurrent(dt);
    
    if(dt.hour >= 9 && dt.hour <= 10) return 0.8;
    if(dt.hour >= 14 && dt.hour <= 15) return 0.7;
    if(dt.hour >= 11 && dt.hour <= 13) return 0.3;
    return 0.5;
}

double CalcMomentum(const double &prices[], int size)
{
    if(size < 5) return 0.0;
    return (prices[size-1] - prices[size-5]) / prices[size-5];
} 