//+------------------------------------------------------------------+
//| ScalperEsgotamento_EA.mq5 - EA Scalper de Esgotamento para WIN M5|
//+------------------------------------------------------------------+
#include <Trade/Trade.mqh>
#include "Include/EsgotamentoDetector.mqh"
#include "Include/LogManager.mqh"
#include "Include/DataValidator.mqh"

//--- CONFIGURAÇÕES GERAIS
input group "=== CONFIGURAÇÕES DE VOLUME E LUCRO ==="
input double InpVolume = 1.0; // Volume base por contrato (multiplicado pelos métodos: 2-3 contratos)
input double InpProfitTarget = 1.0; // Lucro alvo base em R$ (multiplicado pelos métodos: R$2-R$3)
input int InpMinMetodos = 2; // Mínimo de métodos para entrada (2 ou 3)
input int InpMaxPyramiding = 3; // Máximo de entradas pyramiding

//--- DETECÇÃO DE ESGOTAMENTO
input group "=== PARÂMETROS DE ESGOTAMENTO ==="
input int InpExhaustionPeriod = 20; // Período para análise de esgotamento
input double InpRsiOverbought = 80; // RSI sobrecompra (esgotamento de alta)
input double InpRsiOversold = 20; // RSI sobrevenda (esgotamento de baixa)
input double InpVolumeMultiplier = 2.5; // Volume mínimo vs média (2.5x para esgotamento)
input double InpCandleBodyRatio = 0.3; // Corpo pequeno vs média (30% para esgotamento)
input double InpShadowRatio = 0.4; // Sombra mínima vs range (40% para esgotamento)

//--- SISTEMA INTELIGENTE
input group "=== SISTEMA DE APRENDIZADO ==="
input double InpSlippage = 2.0; // Slippage base esperado (pontos)
input double InpVolumeSlippageAdjust = 0.5; // Ajuste slippage por contrato extra (+0.5 pontos)
input bool InpEnableVolumeAnalysis = true; // Habilitar análise de impacto do volume
input bool InpEnableDetailedLogs = true; // Logs detalhados (debug)

// Instâncias dos módulos
CEsgotamentoDetector esgotamentoDetector;
CLogManager logger;
CDataValidator validator;
CTrade trade;

// Variáveis globais - Sistema inteligente de múltiplos contratos
int g_pyramiding_count = 0;
double g_total_volume = 0;
double g_avg_price = 0;
bool g_in_position = false;
bool g_is_buy = false;
int g_metodos_entrada_inicial = 0; // Quantos métodos detectaram esgotamento na entrada inicial
double g_volume_base_entrada = 0; // Volume base da entrada inicial
double g_lucro_alvo_total = 0; // Lucro alvo total baseado nos métodos detectados
string g_status = "Aguardando esgotamento";

// Sistema inteligente de spread/slippage
double g_spread_medio = 0;
double g_slippage_medio = 0;
int g_total_execucoes = 0;
double g_ultimo_spread = 0;
double g_ultimo_slippage = 0;

//+------------------------------------------------------------------+
//| Calcula TP inteligente baseado em condições de mercado           |
//+------------------------------------------------------------------+
double CalculaTPInteligente(double preco_entrada, double volume_total, double lucro_alvo, bool is_buy_operation)
{
    // Obter condições atuais de mercado
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double spread_atual = ask - bid;
    
    // Atualizar média de spread
    if(g_total_execucoes > 0)
        g_spread_medio = (g_spread_medio * g_total_execucoes + spread_atual) / (g_total_execucoes + 1);
    else
        g_spread_medio = spread_atual;
    
    // Calcular custos esperados
    double custo_spread = MathMax(spread_atual, g_spread_medio);
    double custo_slippage = MathMax(2.0, g_slippage_medio); // Mínimo 2 pontos de slippage
    
    // SISTEMA INTELIGENTE: Ajustar slippage baseado no volume
    // Múltiplos contratos podem ter maior impacto no slippage
    if(volume_total > InpVolume)
    {
        double fator_volume = volume_total / InpVolume; // 2.0 para 2 contratos, 3.0 para 3 contratos
        double ajuste_volume = (fator_volume - 1.0) * InpVolumeSlippageAdjust; // Ajuste configurável para cada contrato extra
        custo_slippage += ajuste_volume;
        
        logger.LogDebug("  AJUSTE VOLUME: " + DoubleToString(volume_total,1) + " contratos | Fator: " + DoubleToString(fator_volume,1) + " | Ajuste slippage: +" + DoubleToString(ajuste_volume,1));
    }
    
    double custo_total = custo_spread + custo_slippage;
    
    // Calcular pontos necessários para lucro líquido alvo
    // Cada ponto do WINQ25 = R$0.20
    double pontos_para_lucro = MathCeil(lucro_alvo / 0.20);
    double pontos_totais = pontos_para_lucro + custo_total;
    
    // CORREÇÃO: WINQ25 só aceita múltiplos de 5 pontos
    // Arredondar para o próximo múltiplo de 5
    pontos_totais = MathCeil(pontos_totais / 5.0) * 5.0;
    
    // Garantir mínimo de 10 pontos (2 ticks)
    pontos_totais = MathMax(10, pontos_totais);
    
    // Calcular TP
    double tp = preco_entrada + (is_buy_operation ? pontos_totais : -pontos_totais);
    
    // Log detalhado e rico (se habilitado)
    if(InpEnableDetailedLogs)
    {
        logger.LogDebug("╔═══ TP INTELIGENTE CALCULADO ═══");
        logger.LogDebug("║ Operação: " + (is_buy_operation ? "COMPRA" : "VENDA") + " | Preço Entrada: " + DoubleToString(preco_entrada,0) + " | Volume: " + DoubleToString(volume_total,1) + " contratos");
        logger.LogDebug("║ Lucro Alvo: R$" + DoubleToString(lucro_alvo,2) + " | Valor por ponto: R$0.20");
        logger.LogDebug("╠═ CUSTOS DE MERCADO ═══");
        logger.LogDebug("║ Spread atual: " + DoubleToString(spread_atual,0) + " pts | Spread médio histórico: " + DoubleToString(g_spread_medio,1) + " pts");
        logger.LogDebug("║ Slippage médio: " + DoubleToString(g_slippage_medio,1) + " pts | Execuções analisadas: " + IntegerToString(g_total_execucoes));
        
        if(volume_total > InpVolume)
        {
            double fator_volume = volume_total / InpVolume;
            double ajuste_volume = (fator_volume - 1.0) * InpVolumeSlippageAdjust;
            logger.LogDebug("║ AJUSTE MÚLTIPLOS CONTRATOS: " + DoubleToString(fator_volume,1) + "x base | +" + DoubleToString(ajuste_volume,1) + " pts slippage");
        }
        
        logger.LogDebug("║ Custo total estimado: " + DoubleToString(custo_total,1) + " pts (spread + slippage + margem)");
        logger.LogDebug("╠═ CÁLCULO TP ═══");
        logger.LogDebug("║ Pontos para lucro: " + DoubleToString(pontos_para_lucro,1) + " | + Custos: " + DoubleToString(custo_total,1) + " = " + DoubleToString(pontos_para_lucro + custo_total,1) + " pts brutos");
        logger.LogDebug("║ Arredondado (múltiplo 5): " + DoubleToString(pontos_totais,0) + " pts | TP final: " + DoubleToString(tp,0));
        logger.LogDebug("╚═══════════════════════════════");
    }
    else
    {
        // Log resumido sempre ativo
        logger.LogInfo("TP: " + (is_buy_operation ? "COMPRA" : "VENDA") + " " + DoubleToString(volume_total,1) + " contratos em " + DoubleToString(preco_entrada,0) + " → " + DoubleToString(tp,0) + " (+" + DoubleToString(pontos_totais,0) + " pts) | Alvo: R$" + DoubleToString(lucro_alvo,2));
    }
    
    g_ultimo_spread = spread_atual;
    return NormalizeDouble(tp, _Digits);
}

//+------------------------------------------------------------------+
//| Monitora execução e atualiza estatísticas de slippage            |
//+------------------------------------------------------------------+
void MonitoraExecucao(double preco_solicitado, double preco_executado, bool is_buy, double volume = 0)
{
    double slippage_ocorrido = 0;
    
    if(is_buy)
        slippage_ocorrido = preco_executado - preco_solicitado; // Positivo = pior para compra
    else
        slippage_ocorrido = preco_solicitado - preco_executado; // Positivo = pior para venda
    
    // Atualizar média de slippage (ponderada por volume se fornecido)
    if(g_total_execucoes > 0)
    {
        // Se volume for fornecido, considera o impacto proporcional
        double peso = (volume > 0) ? volume : 1.0;
        g_slippage_medio = (g_slippage_medio * g_total_execucoes + slippage_ocorrido * peso) / (g_total_execucoes + peso);
    }
    else
        g_slippage_medio = slippage_ocorrido;
    
    g_total_execucoes++;
    g_ultimo_slippage = slippage_ocorrido;
    
    // Log detalhado e rico da execução (se habilitado)
    if(InpEnableDetailedLogs)
    {
        logger.LogDebug("╔═══ EXECUÇÃO MONITORADA ═══");
        logger.LogDebug("║ Tipo: " + (is_buy ? "COMPRA" : "VENDA") + " | Preço Solicitado: " + DoubleToString(preco_solicitado,0) + " | Preço Executado: " + DoubleToString(preco_executado,0));
        
        if(volume > 0)
        {
            logger.LogDebug("║ Volume: " + DoubleToString(volume,1) + " contratos | Valor total: R$" + DoubleToString(volume * preco_executado * 0.20,2));
            
            if(g_metodos_entrada_inicial > 0)
            {
                logger.LogDebug("║ Baseado em " + IntegerToString(g_metodos_entrada_inicial) + " métodos de esgotamento detectados");
            }
        }
        
        logger.LogDebug("╠═ ANÁLISE DE SLIPPAGE ═══");
        logger.LogDebug("║ Slippage desta execução: " + DoubleToString(MathAbs(slippage_ocorrido),1) + " pts (" + (slippage_ocorrido >= 0 ? "desfavorável" : "favorável") + ")");
        logger.LogDebug("║ Slippage médio histórico: " + DoubleToString(g_slippage_medio,1) + " pts | Total execuções: " + IntegerToString(g_total_execucoes));
        
        // Análise comparativa
        if(g_total_execucoes > 1)
        {
            double diferenca_media = slippage_ocorrido - g_slippage_medio;
            string comparacao = (diferenca_media > 0.5) ? "PIOR que média" : 
                               (diferenca_media < -0.5) ? "MELHOR que média" : "DENTRO da média";
            logger.LogDebug("║ Comparação: " + DoubleToString(MathAbs(diferenca_media),1) + " pts " + comparacao);
        }
        
        // Log adicional para múltiplos contratos
        if(volume > InpVolume)
        {
            double fator_volume = volume / InpVolume;
            double impacto_estimado = slippage_ocorrido * fator_volume;
            logger.LogDebug("╠═ IMPACTO MÚLTIPLOS CONTRATOS ═══");
            logger.LogDebug("║ Fator volume: " + DoubleToString(fator_volume,1) + "x base | Impacto estimado: " + DoubleToString(impacto_estimado,1) + " pts");
                      logger.LogDebug("║ Custo adicional estimado: R$" + DoubleToString(MathAbs(impacto_estimado) * volume * 0.20,2));
          }
          
        logger.LogDebug("╚═══════════════════════════════");
    }
    else
    {
        // Log resumido sempre ativo
        string tipo_slippage = (slippage_ocorrido >= 0) ? "desfavorável" : "favorável";
        logger.LogInfo("EXECUÇÃO: " + (is_buy ? "COMPRA" : "VENDA") + " " + DoubleToString(volume > 0 ? volume : 1.0,1) + " contratos | " + DoubleToString(preco_solicitado,0) + "→" + DoubleToString(preco_executado,0) + " | Slippage: " + DoubleToString(MathAbs(slippage_ocorrido),1) + " pts (" + tipo_slippage + ")");
    }
    
    // Analisar padrões de impacto do volume no slippage (se habilitado)
    if(volume > 0 && InpEnableVolumeAnalysis)
    {
        AnalisaImpactoVolume(volume, slippage_ocorrido);
    }
}

//+------------------------------------------------------------------+
//| Analisa padrões de slippage por volume para otimização futura    |
//+------------------------------------------------------------------+
void AnalisaImpactoVolume(double volume, double slippage_ocorrido)
{
    static double volumes_historicos[10];
    static double slippages_historicos[10];
    static int indice_historico = 0;
    static int total_amostras = 0;
    
    // Armazenar dados históricos (circular buffer)
    volumes_historicos[indice_historico] = volume;
    slippages_historicos[indice_historico] = slippage_ocorrido;
    indice_historico = (indice_historico + 1) % 10;
    if(total_amostras < 10) total_amostras++;
    
    // Análise quando tiver pelo menos 5 amostras
    if(total_amostras >= 5)
    {
        double slippage_volume_1 = 0, slippage_volume_2_3 = 0;
        int count_volume_1 = 0, count_volume_2_3 = 0;
        
        // Separar por categoria de volume
        for(int i = 0; i < total_amostras; i++)
        {
            if(volumes_historicos[i] <= 1.5) // Volume base (1 contrato)
            {
                slippage_volume_1 += slippages_historicos[i];
                count_volume_1++;
            }
            else // Volume múltiplo (2-3 contratos)
            {
                slippage_volume_2_3 += slippages_historicos[i];
                count_volume_2_3++;
            }
        }
        
        // Calcular médias e comparar
        if(count_volume_1 > 0 && count_volume_2_3 > 0)
        {
            double media_volume_1 = slippage_volume_1 / count_volume_1;
            double media_volume_2_3 = slippage_volume_2_3 / count_volume_2_3;
            double diferenca = media_volume_2_3 - media_volume_1;
            
            // Log da análise (se habilitado)
            if(MathAbs(diferenca) > 0.5) // Diferença significativa
            {
                if(InpEnableDetailedLogs)
                {
        logger.LogDebug("ANÁLISE VOLUME-SLIPPAGE: Vol.1=" + DoubleToString(media_volume_1,1) + " | Vol.2-3=" + DoubleToString(media_volume_2_3,1) + " | Diferença=" + DoubleToString(diferenca,1));
                }
                
                if(diferenca > 1.0) // Impacto significativo de volume
                {
                    logger.LogInfo("ALERTA: Múltiplos contratos aumentam slippage em " + DoubleToString(diferenca,1) + " pontos");
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Log de estatísticas periódicas do sistema                        |
//+------------------------------------------------------------------+
void LogEstatisticasPeriodicas(int candles_analisados, int total_deteccoes)
{
    datetime agora = TimeCurrent();
    string horario = TimeToString(agora, TIME_MINUTES);
    
    logger.LogDebug("╔═══ ESTATÍSTICAS PERIÓDICAS - " + horario + " ═══");
    logger.LogDebug("║ Candles analisados: " + IntegerToString(candles_analisados) + " | Detecções: " + IntegerToString(total_deteccoes));
    
    if(candles_analisados > 0)
    {
        double taxa_deteccao = (double)total_deteccoes / candles_analisados * 100;
        logger.LogDebug("║ Taxa de detecção: " + DoubleToString(taxa_deteccao,1) + "% dos candles");
    }
    
    logger.LogDebug("║ Execuções realizadas: " + IntegerToString(g_total_execucoes) + " | Spread médio: " + DoubleToString(g_spread_medio,1) + " pts");
    logger.LogDebug("║ Slippage médio: " + DoubleToString(g_slippage_medio,1) + " pts | Último spread: " + DoubleToString(g_ultimo_spread,1) + " pts");
    
    if(g_in_position)
    {
        logger.LogDebug("║ EM POSIÇÃO: " + IntegerToString(g_pyramiding_count) + " entradas | " + DoubleToString(g_total_volume,1) + " contratos total");
        logger.LogDebug("║ Métodos entrada inicial: " + IntegerToString(g_metodos_entrada_inicial) + " | Lucro alvo: R$" + DoubleToString(g_lucro_alvo_total,2));
        
        // Calcular lucro atual
        double lucro_atual = 0;
        int total = PositionsTotal();
        for(int i=0; i<total; i++)
        {
            if(PositionGetTicket(i)>0 && PositionGetString(POSITION_SYMBOL)==_Symbol && 
               PositionGetInteger(POSITION_MAGIC)==logger.GetMagicNumber())
            {
                lucro_atual += PositionGetDouble(POSITION_PROFIT);
            }
        }
        logger.LogDebug("║ Lucro atual: R$" + DoubleToString(lucro_atual,2) + " | Progresso: " + DoubleToString((lucro_atual/g_lucro_alvo_total)*100,1) + "%");
    }
    else
    {
        logger.LogDebug("║ STATUS: Aguardando esgotamento (mín. " + IntegerToString(InpMinMetodos) + " métodos)");
    }
    
    // Análise de performance do sistema
    if(g_total_execucoes > 0)
    {
        double eficiencia_spread = (g_ultimo_spread <= g_spread_medio) ? 100 : (g_spread_medio/g_ultimo_spread)*100;
        logger.LogDebug("║ Eficiência spread: " + DoubleToString(eficiencia_spread,1) + "% | Sistema aprendizado: " + (InpEnableVolumeAnalysis ? "ATIVO" : "INATIVO"));
    }
    
    logger.LogDebug("╚═══════════════════════════════════════");
}

//+------------------------------------------------------------------+
//| Ajusta TP de posição existente se necessário                     |
//+------------------------------------------------------------------+
void AjustaTPSeNecessario(ulong ticket, double preco_entrada, bool is_buy)
{
    // SISTEMA INTELIGENTE: Usar valores proporcionais baseados na entrada inicial
    double volume_para_calculo = g_volume_base_entrada > 0 ? g_volume_base_entrada : InpVolume;
    double lucro_para_calculo = g_lucro_alvo_total > 0 ? g_lucro_alvo_total : InpProfitTarget;
    
    // Calcular TP ideal baseado no preço real de entrada e valores proporcionais
    double tp_ideal = CalculaTPInteligente(preco_entrada, volume_para_calculo, lucro_para_calculo, is_buy);
    double tp_atual = PositionGetDouble(POSITION_TP);
    
    // Se diferença for significativa (>2 pontos), ajusta
    if(MathAbs(tp_ideal - tp_atual) > 2)
    {
        double sl = PositionGetDouble(POSITION_SL);
        if(trade.PositionModify(ticket, sl, tp_ideal))
        {
            logger.LogTrade("TP AJUSTADO PÓS-EXECUÇÃO", _Symbol, preco_entrada, volume_para_calculo, sl, tp_ideal, 
                           "Ajuste: " + DoubleToString(tp_atual,0) + " → " + DoubleToString(tp_ideal,0) + " | Alvo: R$" + DoubleToString(lucro_para_calculo,2));
        }
        else
        {
            logger.LogError("AjustaTPSeNecessario", "Falha ao ajustar TP pós-execução", GetLastError());
        }
    }
}

//+------------------------------------------------------------------+
//| Atualiza painel no gráfico                                       |
//+------------------------------------------------------------------+
void UpdatePanel()
{
    string painel = "[Scalper Esgotamento WIN M5 - Múltiplos Contratos]\n";
    painel += "Status: " + g_status + "\n";
    painel += "Entradas: " + IntegerToString(g_pyramiding_count) + " | Volume: " + DoubleToString(g_total_volume,2) + "\n";
    if(g_in_position)
    {
        painel += "Métodos Entrada: " + IntegerToString(g_metodos_entrada_inicial) + " | Vol.Base: " + DoubleToString(g_volume_base_entrada,1) + "\n";
        painel += "Lucro Alvo: R$" + DoubleToString(g_lucro_alvo_total,2) + " | Preço Médio: " + DoubleToString(g_avg_price,_Digits) + "\n";
    }
    else
    {
        painel += "Config: Min." + IntegerToString(InpMinMetodos) + " métodos | Max." + IntegerToString(InpMaxPyramiding) + " pyramiding\n";
    }
    painel += "Spread: " + DoubleToString(g_ultimo_spread,1) + " | Slippage: " + DoubleToString(g_ultimo_slippage,1) + "\n";
    painel += "Execuções: " + IntegerToString(g_total_execucoes);
    if(InpEnableDetailedLogs) painel += " | Debug: ON";
    if(InpEnableVolumeAnalysis) painel += " | Vol.Análise: ON";
    painel += "\n";
    Comment(painel);
}

//+------------------------------------------------------------------+
//| Inicialização do EA                                              |
//+------------------------------------------------------------------+
int OnInit()
{
    logger.Initialize("ScalperEsgotamento_EA", 20250624);
    trade.SetExpertMagicNumber(logger.GetMagicNumber());
    validator.SetSymbol(_Symbol);
    
    // Validação dos parâmetros de entrada
    if(InpMinMetodos < 2 || InpMinMetodos > 3)
    {
        logger.LogError("OnInit", "InpMinMetodos deve ser 2 ou 3", 0);
        return(INIT_PARAMETERS_INCORRECT);
    }
    
    if(InpVolume <= 0 || InpProfitTarget <= 0)
    {
        logger.LogError("OnInit", "Volume e Lucro Alvo devem ser positivos", 0);
        return(INIT_PARAMETERS_INCORRECT);
    }
    
    if(InpVolumeMultiplier < 1.5 || InpVolumeMultiplier > 5.0)
    {
        logger.LogError("OnInit", "InpVolumeMultiplier deve estar entre 1.5 e 5.0", 0);
        return(INIT_PARAMETERS_INCORRECT);
    }
    
    if(InpCandleBodyRatio < 0.1 || InpCandleBodyRatio > 0.8)
    {
        logger.LogError("OnInit", "InpCandleBodyRatio deve estar entre 0.1 e 0.8", 0);
        return(INIT_PARAMETERS_INCORRECT);
    }
    
    g_status = "Aguardando esgotamento";
    UpdatePanel();
    
    // Log rico de inicialização
    logger.LogInfo("╔═══ EA SCALPER ESGOTAMENTO INICIADO ═══");
    logger.LogInfo("║ Símbolo: " + _Symbol + " | Timeframe: M5");
    logger.LogInfo("║ Magic Number: " + IntegerToString(logger.GetMagicNumber()));
    logger.LogInfo("╠═ CONFIGURAÇÃO INTELIGENTE ═══");
    logger.LogInfo("║ Métodos mínimos: " + IntegerToString(InpMinMetodos) + " de 3 | Max pyramiding: " + IntegerToString(InpMaxPyramiding));
    logger.LogInfo("║ Volume base: " + DoubleToString(InpVolume,1) + " | Lucro base: R$" + DoubleToString(InpProfitTarget,2));
    logger.LogInfo("╠═ PARÂMETROS DE DETECÇÃO ═══");
    logger.LogInfo("║ RSI: " + DoubleToString(InpRsiOversold,0) + "/" + DoubleToString(InpRsiOverbought,0) + " | Volume: " + DoubleToString(InpVolumeMultiplier,1) + "x | Candle: " + DoubleToString(InpCandleBodyRatio*100,0) + "%/" + DoubleToString(InpShadowRatio*100,0) + "%");
    logger.LogInfo("╠═ SISTEMA DE APRENDIZADO ═══");
    logger.LogInfo("║ Slippage base: " + DoubleToString(InpSlippage,1) + " pts | Ajuste volume: +" + DoubleToString(InpVolumeSlippageAdjust,1) + " pts/contrato");
    logger.LogInfo("║ Logs detalhados: " + (InpEnableDetailedLogs ? "ATIVO" : "INATIVO") + " | Análise volume: " + (InpEnableVolumeAnalysis ? "ATIVO" : "INATIVO"));
    logger.LogInfo("╚═══════════════════════════════════════");
    logger.LogInfo("Sistema pronto para detectar esgotamento e executar entradas inteligentes.");

    // Verifica e ajusta timeframe automaticamente para M5
    long chart_id = ChartID();
    if(_Period != PERIOD_M5)
    {
        ChartSetSymbolPeriod(chart_id, _Symbol, PERIOD_M5);
        logger.LogInfo("Timeframe ajustado automaticamente para M5.");
        g_status = "Timeframe ajustado para M5. Por favor, aguarde recarregamento.";
        UpdatePanel();
        return(INIT_FAILED); // Força recarregamento do EA
    }

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Finalização do EA                                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    logger.LogInfo("EA ScalperEsgotamento finalizado.");
}



//+------------------------------------------------------------------+
//| Atualiza o TP de todas as posições do EA (compartimentalizado)   |
//+------------------------------------------------------------------+
void AtualizaTPTodasPosicoes(double novo_tp)
{
    int total = PositionsTotal();
    int atualizacoes = 0;
    for(int i=0; i<total; i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket>0 && PositionGetString(POSITION_SYMBOL)==_Symbol && PositionGetInteger(POSITION_MAGIC)==logger.GetMagicNumber())
        {
            double sl = PositionGetDouble(POSITION_SL);
            double tp_atual = PositionGetDouble(POSITION_TP);
            
            if(MathAbs(tp_atual-novo_tp) > SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE)) // Só atualiza se diferente
            {
                if(trade.PositionModify(ticket, sl, novo_tp))
                {
                    atualizacoes++;
                    logger.LogTrade("TP AJUSTADO", _Symbol, PositionGetDouble(POSITION_PRICE_OPEN), PositionGetDouble(POSITION_VOLUME), sl, novo_tp, 
                                   "TP ajustado para alvo R$" + DoubleToString(g_lucro_alvo_total > 0 ? g_lucro_alvo_total : InpProfitTarget,2));
                }
                else
                    logger.LogError("AtualizaTPTodasPosicoes", "Falha ao ajustar TP ticket=" + IntegerToString((int)ticket), GetLastError());
            }
        }
    }
    logger.LogDebug("AtualizaTPTodasPosicoes: " + IntegerToString(atualizacoes) + " posições atualizadas com TP=" + DoubleToString(novo_tp, _Digits));
}

//+------------------------------------------------------------------+
//| Fecha todas as posições abertas do símbolo e Magic Number do EA  |
//+------------------------------------------------------------------+
void FechaTodasPosicoes()
{
    int total = PositionsTotal();
    int fechamentos = 0;
    for(int i=total-1; i>=0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket>0 && PositionGetString(POSITION_SYMBOL)==_Symbol && PositionGetInteger(POSITION_MAGIC)==logger.GetMagicNumber())
        {
            long type = PositionGetInteger(POSITION_TYPE);
            double volume = PositionGetDouble(POSITION_VOLUME);
            double profit = PositionGetDouble(POSITION_PROFIT);
            
            if(trade.PositionClose(ticket, (int)InpSlippage))
            {
                fechamentos++;
                logger.LogTrade("FECHAMENTO", _Symbol, PositionGetDouble(POSITION_PRICE_OPEN), volume, 0, 0, 
                               "Lucro: R$" + DoubleToString(profit,2));
            }
            else
                logger.LogError("FechaTodasPosicoes", "Erro ao fechar ticket=" + IntegerToString((int)ticket), GetLastError());
        }
    }
    
    logger.LogInfo("Fechou " + IntegerToString(fechamentos) + " posições. Reset de variáveis globais.");
    g_in_position = false;
    g_pyramiding_count = 0;
    g_total_volume = 0;
    g_avg_price = 0;
    g_metodos_entrada_inicial = 0;
    g_volume_base_entrada = 0;
    g_lucro_alvo_total = 0;
    g_status = "Aguardando esgotamento";
    UpdatePanel();
}

//+------------------------------------------------------------------+
//| Lógica principal OnTick                                          |
//+------------------------------------------------------------------+
void OnTick()
{
    static datetime last_candle_time = 0;
    static datetime last_stats_time = 0;
    static int candles_analisados = 0;
    static int total_deteccoes = 0;
    MqlRates rates[];
    
    if(CopyRates(_Symbol, PERIOD_M5, 0, InpExhaustionPeriod+2, rates) <= InpExhaustionPeriod)
    {
        logger.LogError("OnTick", "Erro ao obter rates. Obtido: " + IntegerToString(ArraySize(rates)), GetLastError());
        return;
    }
    
    ArraySetAsSeries(rates, true);
    
    // Processa apenas quando há novo candle
    if(rates[1].time == last_candle_time)
        return;
    last_candle_time = rates[1].time;
    
    candles_analisados++;
    
    // Log de estatísticas a cada 30 minutos (6 candles M5)
    if(InpEnableDetailedLogs && (rates[1].time - last_stats_time) >= 1800) // 30 min
    {
        last_stats_time = rates[1].time;
        LogEstatisticasPeriodicas(candles_analisados, total_deteccoes);
    }
    
    UpdatePanel();

    // Validação de dados rigorosa
    SValidationResult val = validator.ValidateRatesData(rates, InpExhaustionPeriod+2);
    if(!val.is_valid)
    {
        g_status = "Erro de dados: " + val.error_message;
        logger.LogError("OnTick", val.error_message, val.error_code);
        UpdatePanel();
        return;
    }

    // Se não está em posição, busca esgotamento para entrada inicial
    if(!g_in_position)
    {
        // Detecta sinais de esgotamento usando parâmetros configuráveis
        bool esgotamento_candle = esgotamentoDetector.DetectaEsgotamentoCandle(rates, InpExhaustionPeriod, InpCandleBodyRatio, InpShadowRatio);
        bool esgotamento_volume = esgotamentoDetector.DetectaEsgotamentoVolume(rates, InpExhaustionPeriod, InpVolumeMultiplier);
        bool esgotamento_rsi = esgotamentoDetector.DetectaEsgotamentoRSI(rates, InpExhaustionPeriod, InpRsiOverbought, InpRsiOversold);
        
        // SISTEMA INTELIGENTE: Conta quantos métodos detectaram esgotamento
        int metodos_detectados = 0;
        if(esgotamento_candle) metodos_detectados++;
        if(esgotamento_volume) metodos_detectados++;
        if(esgotamento_rsi) metodos_detectados++;
        
        // Atualizar contador de detecções para estatísticas
        if(metodos_detectados > 0) total_deteccoes++;
        
        if(metodos_detectados >= InpMinMetodos) // Mínimo configurável de métodos para entrada
        {
            // Determine a direção da entrada: contrária ao movimento exaurido
            // Se último candle foi de alta (close > open), entra vendido
            // Se último candle foi de baixa (close < open), entra comprado
            bool is_buy = (rates[1].close < rates[1].open); // Direção oposta ao movimento exaurido
            g_is_buy = is_buy;
            
            // SISTEMA INTELIGENTE: Volume e lucro alvo proporcionais aos métodos detectados
            double volume_entrada = InpVolume * metodos_detectados; // 2 ou 3 contratos
            double lucro_alvo_entrada = InpProfitTarget * metodos_detectados; // R$2 ou R$3
            
            // Armazenar informações da entrada inicial
            g_metodos_entrada_inicial = metodos_detectados;
            g_volume_base_entrada = volume_entrada;
            g_lucro_alvo_total = lucro_alvo_entrada;
            
            // Obter preços atuais de mercado
            double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
            double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            double spread = ask - bid;
            double preco_entrada = is_buy ? ask : bid;
            double sl = 0; // Sem SL - usa apenas TP
            
            // Log rico da análise de entrada
            if(InpEnableDetailedLogs)
            {
                logger.LogDebug("╔═══ ANÁLISE DE ENTRADA INTELIGENTE ═══");
                logger.LogDebug("║ MÉTODOS DETECTADOS: " + IntegerToString(metodos_detectados) + " de 3 possíveis (mín: " + IntegerToString(InpMinMetodos) + ")");
                logger.LogDebug("║ • Candle: " + (esgotamento_candle ? "✓ SIM" : "✗ NÃO") + " | Volume: " + (esgotamento_volume ? "✓ SIM" : "✗ NÃO") + " | RSI: " + (esgotamento_rsi ? "✓ SIM" : "✗ NÃO"));
                logger.LogDebug("╠═ CONDIÇÕES DE MERCADO ═══");
                logger.LogDebug("║ BID: " + DoubleToString(bid,0) + " | ASK: " + DoubleToString(ask,0) + " | Spread: " + DoubleToString(spread,0) + " pts");
                logger.LogDebug("║ Direção: " + (is_buy ? "COMPRA" : "VENDA") + " (contrária ao movimento exaurido)");
                logger.LogDebug("║ Último candle: " + (rates[1].close > rates[1].open ? "ALTA" : "BAIXA") + " (close " + DoubleToString(rates[1].close,0) + " vs open " + DoubleToString(rates[1].open,0) + ")");
                logger.LogDebug("╠═ SISTEMA INTELIGENTE ═══");
                logger.LogDebug("║ Volume entrada: " + DoubleToString(volume_entrada,1) + " contratos (" + IntegerToString(metodos_detectados) + "x base)");
                logger.LogDebug("║ Lucro alvo: R$" + DoubleToString(lucro_alvo_entrada,2) + " (" + IntegerToString(metodos_detectados) + "x base de R$" + DoubleToString(InpProfitTarget,2) + ")");
                logger.LogDebug("║ Preço entrada: " + DoubleToString(preco_entrada,0));
                logger.LogDebug("╚═══════════════════════════════════");
            }
            else
            {
                logger.LogInfo("ENTRADA: " + IntegerToString(metodos_detectados) + " métodos detectados | " + (is_buy ? "COMPRA" : "VENDA") + " " + DoubleToString(volume_entrada,1) + " contratos | Alvo: R$" + DoubleToString(lucro_alvo_entrada,2));
            }
            
            // Calcula TP inteligente baseado em condições de mercado e lucro alvo proporcional
            double tp = CalculaTPInteligente(preco_entrada, volume_entrada, lucro_alvo_entrada, is_buy);
            
            if(tp <= 0)
            {
                logger.LogError("OnTick", "TP inválido calculado: " + DoubleToString(tp, _Digits), 0);
                return;
            }
            
            // Verificar se TP é válido (múltiplo de 5 e mínimo 10)
            double distancia_tp = MathAbs(tp - preco_entrada);
            
            if(distancia_tp < 10 || MathMod(distancia_tp, 5) != 0)
            {
                logger.LogError("OnTick", "TP inválido: distância=" + DoubleToString(distancia_tp,0) + " (deve ser múltiplo de 5, min=10)", 0);
                return;
            }
            
            // Log da entrada
            logger.LogDebug("ENTRADA MÚLTIPLA: " + (is_buy ? "COMPRA" : "VENDA") + " " + DoubleToString(volume_entrada,1) + " contratos em " + DoubleToString(preco_entrada,0) + " | TP: " + DoubleToString(tp,0) + " | Métodos: C=" + IntegerToString(esgotamento_candle) + " V=" + IntegerToString(esgotamento_volume) + " R=" + IntegerToString(esgotamento_rsi));
            
            bool result = false;
            if(is_buy)
                result = trade.Buy(volume_entrada, _Symbol, 0, sl, tp, "Esgotamento " + IntegerToString(metodos_detectados) + " métodos");
            else
                result = trade.Sell(volume_entrada, _Symbol, 0, sl, tp, "Esgotamento " + IntegerToString(metodos_detectados) + " métodos");
                
            if(result)
            {
                // Aguardar um pouco para obter dados da execução
                Sleep(100);
                
                // Obter dados da execução real
                ulong ticket = 0;
                double preco_executado = 0;
                int total = PositionsTotal();
                for(int i = 0; i < total; i++)
                {
                    if(PositionGetTicket(i) > 0 && PositionGetString(POSITION_SYMBOL) == _Symbol && 
                       PositionGetInteger(POSITION_MAGIC) == logger.GetMagicNumber())
                    {
                        ticket = PositionGetTicket(i);
                        preco_executado = PositionGetDouble(POSITION_PRICE_OPEN);
                        break;
                    }
                }
                
                if(ticket > 0 && preco_executado > 0)
                {
                    // Monitorar execução para aprender spread/slippage
                    MonitoraExecucao(preco_entrada, preco_executado, is_buy, volume_entrada);
                    
                    // Ajustar TP se necessário baseado no preço real de execução
                    AjustaTPSeNecessario(ticket, preco_executado, is_buy);
                    
                    g_avg_price = preco_executado; // Usar preço real de execução
                }
                else
                {
                    g_avg_price = preco_entrada; // Fallback para preço solicitado
                }
                
                g_in_position = true;
                g_pyramiding_count = 1;
                g_total_volume = volume_entrada;
                g_status = "Em posição inicial (" + (is_buy ? "COMPRA" : "VENDA") + ") - " + IntegerToString(metodos_detectados) + " métodos, " + DoubleToString(volume_entrada,1) + " contratos";
                
                // Log rico do resultado da entrada
                if(InpEnableDetailedLogs)
                {
        logger.LogDebug("╔═══ ENTRADA EXECUTADA COM SUCESSO ═══");
        logger.LogDebug("║ Ticket gerado | Preço: " + DoubleToString(preco_executado,0) + " → " + DoubleToString(tp,0) + " (TP)");
        logger.LogDebug("║ Volume: " + DoubleToString(volume_entrada,1) + " contratos | Valor: R$" + DoubleToString(volume_entrada * preco_executado * 0.20,2));
        logger.LogDebug("║ Distância TP: " + DoubleToString(MathAbs(tp - preco_executado),0) + " pts | Lucro esperado: R$" + DoubleToString(lucro_alvo_entrada,2));
        logger.LogDebug("║ Baseado em " + IntegerToString(metodos_detectados) + " métodos de esgotamento");
        logger.LogDebug("╚═══════════════════════════════════");
                }
                
                logger.LogTrade("ENTRADA MÚLTIPLA", _Symbol, preco_executado, volume_entrada, sl, tp, 
                               IntegerToString(metodos_detectados) + " métodos detectados - Alvo: R$" + DoubleToString(lucro_alvo_entrada,2));
            }
            else
            {
                g_status = "Erro ao abrir posição inicial: " + IntegerToString(GetLastError());
                logger.LogError("OnTick", "Erro ao abrir posição inicial", GetLastError());
            }
            UpdatePanel();
            return;
        }
        else
        {
            g_status = "Aguardando esgotamento";
            UpdatePanel();
            return;
        }
    }
    // Se já está em posição, verifica se faz pyramiding e ajusta TP
    else
    {
        // Calcula estado atual de todas as posições do símbolo e Magic Number do EA
        double lucro_total = 0;
        double preco_medio_posicoes = 0;
        double volume_total_posicoes = 0;
        int contagem_posicoes = 0;
        
        int total = PositionsTotal();
        for(int i=0; i<total; i++)
        {
            if(PositionGetTicket(i)>0 && PositionGetString(POSITION_SYMBOL)==_Symbol && PositionGetInteger(POSITION_MAGIC)==logger.GetMagicNumber())
            {
                double vol = PositionGetDouble(POSITION_VOLUME);
                double preco = PositionGetDouble(POSITION_PRICE_OPEN);
                volume_total_posicoes += vol;
                preco_medio_posicoes += preco * vol;
                lucro_total += PositionGetDouble(POSITION_PROFIT);
                contagem_posicoes++;
            }
        }
        
        if(volume_total_posicoes > 0)
            preco_medio_posicoes /= volume_total_posicoes;
        
        // Atualiza variáveis globais baseado nas posições reais
        g_total_volume = volume_total_posicoes;
        g_avg_price = preco_medio_posicoes;
        g_pyramiding_count = contagem_posicoes;
        
        // Com TP fixo de 10 pontos, não precisa recalcular TP
        
        // Verifica se ainda há posições abertas
        if(contagem_posicoes == 0)
        {
            // Se não há mais posições, reseta tudo
            g_in_position = false;
            g_pyramiding_count = 0;
            g_total_volume = 0;
            g_avg_price = 0;
            g_metodos_entrada_inicial = 0;
            g_volume_base_entrada = 0;
            g_lucro_alvo_total = 0;
            g_status = "Aguardando esgotamento";
            logger.LogInfo("Todas as posições foram fechadas pelo TP. Aguardando novo esgotamento.");
            UpdatePanel();
            return;
        }
        
        // Se preço foi contra e novo esgotamento detectado, faz pyramiding
        bool novo_esgotamento = false;
        if(g_pyramiding_count < InpMaxPyramiding)
        {
            bool esgotamento_candle = esgotamentoDetector.DetectaEsgotamentoCandle(rates, InpExhaustionPeriod, InpCandleBodyRatio, InpShadowRatio);
            bool esgotamento_volume = esgotamentoDetector.DetectaEsgotamentoVolume(rates, InpExhaustionPeriod, InpVolumeMultiplier);
            bool esgotamento_rsi = esgotamentoDetector.DetectaEsgotamentoRSI(rates, InpExhaustionPeriod, InpRsiOverbought, InpRsiOversold);
            novo_esgotamento = (esgotamento_candle || esgotamento_volume || esgotamento_rsi);
        }
        
        if(novo_esgotamento && g_pyramiding_count < InpMaxPyramiding)
        {
            // Para pyramiding, mantém a mesma direção da posição inicial
            bool is_buy_pyramid = g_is_buy;
            double preco_entrada = is_buy_pyramid ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
            
            // SISTEMA INTELIGENTE: Volume de pyramiding proporcional à entrada inicial
            // Se entrou com 2 contratos, pyramiding também com 2. Se 3, pyramiding com 3.
            double volume_pyramid = g_volume_base_entrada;
            double lucro_alvo_pyramid = g_lucro_alvo_total;
            double sl = 0;
            
            // TP inteligente para pyramiding proporcional
            double tp = CalculaTPInteligente(preco_entrada, volume_pyramid, lucro_alvo_pyramid, is_buy_pyramid);
            
            if(tp <= 0)
            {
                logger.LogError("OnTick", "TP pyramiding inválido: " + DoubleToString(tp, _Digits), 0);
                return;
            }
            
            if(InpEnableDetailedLogs)
            {
                logger.LogDebug("╔═══ PYRAMIDING INTELIGENTE ═══");
                logger.LogDebug("║ Novo esgotamento detectado | Entrada #" + IntegerToString(g_pyramiding_count + 1));
                logger.LogDebug("║ Direção: " + (is_buy_pyramid ? "COMPRA" : "VENDA") + " (mantendo posição inicial)");
                logger.LogDebug("║ Volume: " + DoubleToString(volume_pyramid,1) + " contratos (igual à entrada inicial)");
                logger.LogDebug("║ Preço: " + DoubleToString(preco_entrada,0) + " | TP: " + DoubleToString(tp,0) + " (+" + DoubleToString(MathAbs(tp - preco_entrada),0) + " pts)");
                logger.LogDebug("║ Lucro alvo mantido: R$" + DoubleToString(lucro_alvo_pyramid,2) + " por entrada");
                logger.LogDebug("╚═══════════════════════════════");
            }
            else
            {
                logger.LogInfo("PYRAMIDING: " + (is_buy_pyramid ? "COMPRA" : "VENDA") + " " + DoubleToString(volume_pyramid,1) + " contratos em " + DoubleToString(preco_entrada,0) + " → " + DoubleToString(tp,0) + " | Entrada #" + IntegerToString(g_pyramiding_count + 1));
            }
            
            bool result = false;
            if(is_buy_pyramid)
                result = trade.Buy(volume_pyramid, _Symbol, 0, sl, tp, "Pyramiding " + DoubleToString(volume_pyramid,1) + " contratos");
            else
                result = trade.Sell(volume_pyramid, _Symbol, 0, sl, tp, "Pyramiding " + DoubleToString(volume_pyramid,1) + " contratos");
                
            if(result)
            {
                g_status = "Pyramiding: " + IntegerToString(g_pyramiding_count + 1) + " entradas (" + DoubleToString(volume_pyramid,1) + " contratos cada)";
                logger.LogTrade("PYRAMIDING INTELIGENTE", _Symbol, preco_entrada, volume_pyramid, sl, tp, 
                               "Novo esgotamento - " + DoubleToString(volume_pyramid,1) + " contratos - Alvo: R$" + DoubleToString(lucro_alvo_pyramid,2));
            }
            else
            {
                g_status = "Erro ao fazer pyramiding: " + IntegerToString(GetLastError());
                logger.LogError("OnTick", "Erro ao fazer pyramiding", GetLastError());
            }
            UpdatePanel();
            return;
        }
        
        // Se não há novo esgotamento, apenas atualiza painel
        g_status = "Em posição (" + IntegerToString(g_pyramiding_count) + " entradas, " + DoubleToString(g_volume_base_entrada,1) + " contratos cada) | Lucro: R$" + DoubleToString(lucro_total,2);
        UpdatePanel();
    }
} 
