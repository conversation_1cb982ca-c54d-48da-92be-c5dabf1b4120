//+------------------------------------------------------------------+
//|                                        UltraAdvancedML.mqh       |
//|                    Sistema ML Ultra Avançado para MT5             |
//|                    Rede Neural + Ensemble + Auto-Optimize        |
//+------------------------------------------------------------------+

#include <Files/File.mqh>
#include <Math/Stat/Math.mqh>

#define ML_FEATURES 20
#define ML_HIDDEN_NEURONS 12
#define ML_ENSEMBLE_SIZE 3
#define ML_MAX_SAMPLES 1500
#define ML_EPOCHS 150

//+------------------------------------------------------------------+
//| ESTRUTURAS ULTRA AVANÇADAS                                       |
//+------------------------------------------------------------------+

struct UltraFeatures
{
    double volume_ratio;
    double price_velocity;
    double price_acceleration;
    double volatility_ratio;
    double trend_strength;
    double rsi_value;
    double bollinger_position;
    double macd_signal;
    double stochastic_value;
    double support_resistance;
    double fibonacci_level;
    double candlestick_pattern;
    double bid_ask_imbalance;
    double tick_imbalance;
    double intraday_pattern;
    double weekday_factor;
    double hour_volatility;
    double market_pressure;
    double liquidity_score;
    double momentum_score;
};

struct UltraModel
{
    double weights_input[ML_FEATURES][ML_HIDDEN_NEURONS];
    double weights_hidden[ML_HIDDEN_NEURONS];
    double bias_hidden[ML_HIDDEN_NEURONS];
    double bias_output;
    double learning_rate;
    double momentum_factor;
    double accuracy;
    int epochs_trained;
};

struct UltraEnsemble
{
    UltraModel models[ML_ENSEMBLE_SIZE];
    double model_weights[ML_ENSEMBLE_SIZE];
    double ensemble_accuracy;
    int best_model_index;
    bool is_trained;
};

//+------------------------------------------------------------------+
//| CLASSE ULTRA AVANÇADA                                            |
//+------------------------------------------------------------------+

class CUltraAdvancedML
{
private:
    UltraFeatures m_features_buffer[ML_MAX_SAMPLES];
    double m_labels_buffer[ML_MAX_SAMPLES];
    int m_sample_count;
    
    UltraEnsemble m_ensemble;
    
    double m_feature_means[ML_FEATURES];
    double m_feature_stds[ML_FEATURES];
    bool m_is_normalized;
    
    string m_symbol;
    int m_magic;
    
public:
    CUltraAdvancedML();
    ~CUltraAdvancedML();
    
    bool Initialize(string symbol, int magic);
    bool TrainUltraSystem();
    double PredictUltra(const UltraFeatures &features);
    UltraFeatures ExtractUltraFeatures();
    
    void AddTrainingSample(const UltraFeatures &features, double label);
    bool SaveUltraModel();
    bool LoadUltraModel();
    
    double GetAccuracy() { return m_ensemble.ensemble_accuracy; }
    string GetModelInfo();
    
private:
    bool CollectHistoricalData();
    void NormalizeFeatures();
    bool TrainSingleModel(UltraModel &model);
    double TestModel(const UltraModel &model);
    void UpdateEnsembleWeights();
    double Sigmoid(double x) { return 1.0 / (1.0 + MathExp(-x)); }
    void ConvertToArray(const UltraFeatures &features, double &array[]);
};

//+------------------------------------------------------------------+
//| IMPLEMENTAÇÃO                                                    |
//+------------------------------------------------------------------+

CUltraAdvancedML::CUltraAdvancedML()
{
    m_sample_count = 0;
    m_is_normalized = false;
    ZeroMemory(m_ensemble);
    ZeroMemory(m_feature_means);
    ZeroMemory(m_feature_stds);
    
    // Inicializar ensemble
    for(int i = 0; i < ML_ENSEMBLE_SIZE; i++)
    {
        m_ensemble.model_weights[i] = 1.0 / ML_ENSEMBLE_SIZE;
        m_ensemble.models[i].learning_rate = 0.001 + (i * 0.0005);
        m_ensemble.models[i].momentum_factor = 0.9 - (i * 0.05);
    }
}

CUltraAdvancedML::~CUltraAdvancedML()
{
    SaveUltraModel();
}

bool CUltraAdvancedML::Initialize(string symbol, int magic)
{
    m_symbol = symbol;
    m_magic = magic;
    
    Print("🧠 Inicializando Ultra Advanced ML System...");
    
    if(!LoadUltraModel())
    {
        Print("📊 Coletando dados históricos...");
        if(!CollectHistoricalData())
        {
            Print("❌ Falha na coleta de dados");
            return false;
        }
        
        Print("🎯 Treinando sistema ultra avançado...");
        if(!TrainUltraSystem())
        {
            Print("❌ Falha no treinamento");
            return false;
        }
    }
    
    Print("✅ Ultra Advanced ML System inicializado - Precisão: ", 
          DoubleToString(m_ensemble.ensemble_accuracy, 2), "%");
    
    return true;
}

bool CUltraAdvancedML::CollectHistoricalData()
{
    MqlRates rates[];
    int count = CopyRates(m_symbol, PERIOD_M1, 0, 30000, rates);
    
    if(count < 1000)
    {
        Print("❌ Dados insuficientes: ", count);
        return false;
    }
    
    m_sample_count = 0;
    
    for(int i = 100; i < count - 1 && m_sample_count < ML_MAX_SAMPLES; i++)
    {
        UltraFeatures features = ExtractHistoricalFeatures(rates, i);
        
        double current_price = rates[i].close;
        double future_price = rates[i + 1].close;
        double label = (future_price > current_price) ? 1.0 : 0.0;
        
        m_features_buffer[m_sample_count] = features;
        m_labels_buffer[m_sample_count] = label;
        m_sample_count++;
        
        if(m_sample_count % 500 == 0)
        {
            Print("📊 Coletado: ", m_sample_count, "/", ML_MAX_SAMPLES);
        }
    }
    
    Print("✅ Coleta concluída: ", m_sample_count, " amostras");
    return true;
}

UltraFeatures CUltraAdvancedML::ExtractHistoricalFeatures(const MqlRates &rates[], int index)
{
    UltraFeatures features;
    ZeroMemory(features);
    
    if(index < 50) return features;
    
    // Arrays para cálculos
    double prices[50], volumes[50];
    for(int i = 0; i < 50; i++)
    {
        prices[i] = rates[index - 49 + i].close;
        volumes[i] = (double)rates[index - 49 + i].tick_volume;
    }
    
    // Calcular features
    features.volume_ratio = volumes[49] / CalculateMean(volumes, 49);
    features.price_velocity = prices[49] - prices[48];
    features.price_acceleration = (prices[49] - prices[48]) - (prices[48] - prices[47]);
    features.volatility_ratio = (rates[index].high - rates[index].low) / CalculateATR(rates, index, 14);
    features.trend_strength = CalculateTrend(prices, 49);
    features.rsi_value = CalculateRSI(prices, 49, 14);
    features.bollinger_position = CalculateBollinger(prices, 49);
    features.macd_signal = CalculateMACD(prices, 49);
    features.stochastic_value = CalculateStochastic(rates, index, 14);
    features.support_resistance = CalculateSR(prices, 49);
    features.fibonacci_level = CalculateFib(prices, 49);
    features.candlestick_pattern = AnalyzeCandles(rates, index);
    features.bid_ask_imbalance = CalculateImbalance();
    features.tick_imbalance = CalculateTickFlow();
    
    // Features temporais
    MqlDateTime dt;
    TimeToStruct(rates[index].time, dt);
    features.intraday_pattern = CalculateIntradayPattern(dt.hour, dt.min);
    features.weekday_factor = CalculateWeekdayFactor(dt.day_of_week);
    features.hour_volatility = CalculateHourVolatility(dt.hour);
    features.market_pressure = CalculateMarketPressure(rates, index);
    features.liquidity_score = CalculateLiquidity(volumes, 49);
    features.momentum_score = CalculateMomentum(prices, 49);
    
    return features;
}

UltraFeatures CUltraAdvancedML::ExtractUltraFeatures()
{
    MqlRates rates[];
    if(CopyRates(m_symbol, PERIOD_M1, 0, 100, rates) < 50)
    {
        UltraFeatures empty;
        ZeroMemory(empty);
        return empty;
    }
    
    return ExtractHistoricalFeatures(rates, ArraySize(rates) - 1);
}

bool CUltraAdvancedML::TrainUltraSystem()
{
    if(m_sample_count < 100)
    {
        Print("❌ Dados insuficientes para treinamento");
        return false;
    }
    
    Print("🎯 Treinando ensemble com ", ML_ENSEMBLE_SIZE, " modelos...");
    
    // Normalizar features
    NormalizeFeatures();
    
    // Treinar cada modelo
    double total_accuracy = 0.0;
    for(int i = 0; i < ML_ENSEMBLE_SIZE; i++)
    {
        Print("🧠 Treinando modelo ", i + 1, "/", ML_ENSEMBLE_SIZE);
        
        if(TrainSingleModel(m_ensemble.models[i]))
        {
            double accuracy = TestModel(m_ensemble.models[i]);
            total_accuracy += accuracy;
            m_ensemble.models[i].accuracy = accuracy;
            
            Print("✅ Modelo ", i + 1, " - Precisão: ", DoubleToString(accuracy, 2), "%");
        }
    }
    
    // Atualizar pesos do ensemble
    UpdateEnsembleWeights();
    
    m_ensemble.ensemble_accuracy = total_accuracy / ML_ENSEMBLE_SIZE;
    m_ensemble.is_trained = true;
    
    Print("🏆 Ensemble treinado - Precisão: ", DoubleToString(m_ensemble.ensemble_accuracy, 2), "%");
    
    return true;
}

bool CUltraAdvancedML::TrainSingleModel(UltraModel &model)
{
    // Inicializar pesos
    for(int i = 0; i < ML_FEATURES; i++)
    {
        for(int j = 0; j < ML_HIDDEN_NEURONS; j++)
        {
            model.weights_input[i][j] = (MathRand() / 16383.5 - 1.0) * 0.5;
        }
    }
    
    for(int i = 0; i < ML_HIDDEN_NEURONS; i++)
    {
        model.weights_hidden[i] = (MathRand() / 16383.5 - 1.0) * 0.5;
        model.bias_hidden[i] = 0.0;
    }
    model.bias_output = 0.0;
    
    // Treinamento
    for(int epoch = 0; epoch < ML_EPOCHS; epoch++)
    {
        double epoch_loss = 0.0;
        
        for(int sample = 0; sample < m_sample_count; sample++)
        {
            // Forward pass
            double feature_array[ML_FEATURES];
            ConvertToArray(m_features_buffer[sample], feature_array);
            
            double hidden[ML_HIDDEN_NEURONS];
            for(int h = 0; h < ML_HIDDEN_NEURONS; h++)
            {
                double sum = model.bias_hidden[h];
                for(int f = 0; f < ML_FEATURES; f++)
                {
                    sum += feature_array[f] * model.weights_input[f][h];
                }
                hidden[h] = Sigmoid(sum);
            }
            
            double output_sum = model.bias_output;
            for(int h = 0; h < ML_HIDDEN_NEURONS; h++)
            {
                output_sum += hidden[h] * model.weights_hidden[h];
            }
            double output = Sigmoid(output_sum);
            
            // Calcular erro
            double target = m_labels_buffer[sample];
            double error = output - target;
            epoch_loss += error * error;
            
            // Backward pass
            double output_gradient = error * output * (1.0 - output);
            
            for(int h = 0; h < ML_HIDDEN_NEURONS; h++)
            {
                double hidden_gradient = output_gradient * model.weights_hidden[h] * hidden[h] * (1.0 - hidden[h]);
                
                // Atualizar pesos
                model.weights_hidden[h] -= model.learning_rate * output_gradient * hidden[h];
                
                for(int f = 0; f < ML_FEATURES; f++)
                {
                    model.weights_input[f][h] -= model.learning_rate * hidden_gradient * feature_array[f];
                }
                
                model.bias_hidden[h] -= model.learning_rate * hidden_gradient;
            }
            
            model.bias_output -= model.learning_rate * output_gradient;
        }
        
        if(epoch % 50 == 0)
        {
            Print("📈 Época ", epoch, " - Loss: ", DoubleToString(epoch_loss / m_sample_count, 6));
        }
    }
    
    model.epochs_trained = ML_EPOCHS;
    return true;
}

double CUltraAdvancedML::TestModel(const UltraModel &model)
{
    int correct = 0;
    int total = MathMin(m_sample_count, 200); // Teste em 200 amostras
    
    for(int i = 0; i < total; i++)
    {
        double feature_array[ML_FEATURES];
        ConvertToArray(m_features_buffer[i], feature_array);
        
        // Forward pass
        double hidden[ML_HIDDEN_NEURONS];
        for(int h = 0; h < ML_HIDDEN_NEURONS; h++)
        {
            double sum = model.bias_hidden[h];
            for(int f = 0; f < ML_FEATURES; f++)
            {
                sum += feature_array[f] * model.weights_input[f][h];
            }
            hidden[h] = Sigmoid(sum);
        }
        
        double output_sum = model.bias_output;
        for(int h = 0; h < ML_HIDDEN_NEURONS; h++)
        {
            output_sum += hidden[h] * model.weights_hidden[h];
        }
        double output = Sigmoid(output_sum);
        
        // Verificar predição
        bool predicted = output > 0.5;
        bool actual = m_labels_buffer[i] > 0.5;
        
        if(predicted == actual) correct++;
    }
    
    return (double)correct / total * 100.0;
}

double CUltraAdvancedML::PredictUltra(const UltraFeatures &features)
{
    if(!m_ensemble.is_trained) return 0.5;
    
    double weighted_prediction = 0.0;
    double total_weight = 0.0;
    
    for(int i = 0; i < ML_ENSEMBLE_SIZE; i++)
    {
        double feature_array[ML_FEATURES];
        ConvertToArray(features, feature_array);
        
        const UltraModel &model = m_ensemble.models[i];
        
        // Forward pass
        double hidden[ML_HIDDEN_NEURONS];
        for(int h = 0; h < ML_HIDDEN_NEURONS; h++)
        {
            double sum = model.bias_hidden[h];
            for(int f = 0; f < ML_FEATURES; f++)
            {
                sum += feature_array[f] * model.weights_input[f][h];
            }
            hidden[h] = Sigmoid(sum);
        }
        
        double output_sum = model.bias_output;
        for(int h = 0; h < ML_HIDDEN_NEURONS; h++)
        {
            output_sum += hidden[h] * model.weights_hidden[h];
        }
        double output = Sigmoid(output_sum);
        
        weighted_prediction += output * m_ensemble.model_weights[i];
        total_weight += m_ensemble.model_weights[i];
    }
    
    return weighted_prediction / total_weight;
}

void CUltraAdvancedML::UpdateEnsembleWeights()
{
    double total_accuracy = 0.0;
    
    for(int i = 0; i < ML_ENSEMBLE_SIZE; i++)
    {
        total_accuracy += m_ensemble.models[i].accuracy;
    }
    
    if(total_accuracy > 0)
    {
        for(int i = 0; i < ML_ENSEMBLE_SIZE; i++)
        {
            m_ensemble.model_weights[i] = m_ensemble.models[i].accuracy / total_accuracy;
        }
    }
}

void CUltraAdvancedML::NormalizeFeatures()
{
    // Calcular médias
    for(int f = 0; f < ML_FEATURES; f++)
    {
        m_feature_means[f] = 0.0;
        for(int s = 0; s < m_sample_count; s++)
        {
            double feature_array[ML_FEATURES];
            ConvertToArray(m_features_buffer[s], feature_array);
            m_feature_means[f] += feature_array[f];
        }
        m_feature_means[f] /= m_sample_count;
    }
    
    // Calcular desvios padrão
    for(int f = 0; f < ML_FEATURES; f++)
    {
        m_feature_stds[f] = 0.0;
        for(int s = 0; s < m_sample_count; s++)
        {
            double feature_array[ML_FEATURES];
            ConvertToArray(m_features_buffer[s], feature_array);
            double diff = feature_array[f] - m_feature_means[f];
            m_feature_stds[f] += diff * diff;
        }
        m_feature_stds[f] = MathSqrt(m_feature_stds[f] / m_sample_count);
        if(m_feature_stds[f] == 0) m_feature_stds[f] = 1.0;
    }
    
    // Normalizar dados
    for(int s = 0; s < m_sample_count; s++)
    {
        double feature_array[ML_FEATURES];
        ConvertToArray(m_features_buffer[s], feature_array);
        
        for(int f = 0; f < ML_FEATURES; f++)
        {
            feature_array[f] = (feature_array[f] - m_feature_means[f]) / m_feature_stds[f];
        }
        
        // Converter de volta
        m_features_buffer[s].volume_ratio = feature_array[0];
        m_features_buffer[s].price_velocity = feature_array[1];
        // ... (continuar para todas as features)
    }
    
    m_is_normalized = true;
}

void CUltraAdvancedML::ConvertToArray(const UltraFeatures &features, double &array[])
{
    array[0] = features.volume_ratio;
    array[1] = features.price_velocity;
    array[2] = features.price_acceleration;
    array[3] = features.volatility_ratio;
    array[4] = features.trend_strength;
    array[5] = features.rsi_value;
    array[6] = features.bollinger_position;
    array[7] = features.macd_signal;
    array[8] = features.stochastic_value;
    array[9] = features.support_resistance;
    array[10] = features.fibonacci_level;
    array[11] = features.candlestick_pattern;
    array[12] = features.bid_ask_imbalance;
    array[13] = features.tick_imbalance;
    array[14] = features.intraday_pattern;
    array[15] = features.weekday_factor;
    array[16] = features.hour_volatility;
    array[17] = features.market_pressure;
    array[18] = features.liquidity_score;
    array[19] = features.momentum_score;
    
    // Aplicar normalização se disponível
    if(m_is_normalized)
    {
        for(int i = 0; i < ML_FEATURES; i++)
        {
            array[i] = (array[i] - m_feature_means[i]) / m_feature_stds[i];
        }
    }
}

bool CUltraAdvancedML::SaveUltraModel()
{
    string filename = "UltraML_" + m_symbol + ".dat";
    int file = FileOpen(filename, FILE_WRITE | FILE_BIN);
    
    if(file == INVALID_HANDLE) return false;
    
    FileWriteStruct(file, m_ensemble);
    FileWriteArray(file, m_feature_means);
    FileWriteArray(file, m_feature_stds);
    FileWriteInteger(file, m_sample_count);
    FileWriteInteger(file, m_is_normalized ? 1 : 0);
    
    FileClose(file);
    return true;
}

bool CUltraAdvancedML::LoadUltraModel()
{
    string filename = "UltraML_" + m_symbol + ".dat";
    int file = FileOpen(filename, FILE_READ | FILE_BIN);
    
    if(file == INVALID_HANDLE) return false;
    
    FileReadStruct(file, m_ensemble);
    FileReadArray(file, m_feature_means);
    FileReadArray(file, m_feature_stds);
    m_sample_count = FileReadInteger(file);
    m_is_normalized = FileReadInteger(file) == 1;
    
    FileClose(file);
    return true;
}

string CUltraAdvancedML::GetModelInfo()
{
    string info = "🧠 ULTRA ADVANCED ML SYSTEM\n";
    info += StringFormat("├─ Ensemble: %d modelos\n", ML_ENSEMBLE_SIZE);
    info += StringFormat("├─ Features: %d\n", ML_FEATURES);
    info += StringFormat("├─ Neurônios: %d\n", ML_HIDDEN_NEURONS);
    info += StringFormat("├─ Épocas: %d\n", ML_EPOCHS);
    info += StringFormat("├─ Amostras: %d\n", m_sample_count);
    info += StringFormat("├─ Precisão: %.2f%%\n", m_ensemble.ensemble_accuracy);
    info += StringFormat("└─ Status: %s", m_ensemble.is_trained ? "TREINADO" : "PENDENTE");
    
    return info;
}

//+------------------------------------------------------------------+
//| FUNÇÕES AUXILIARES DE CÁLCULO                                    |
//+------------------------------------------------------------------+

double CalculateMean(const double &array[], int size)
{
    double sum = 0.0;
    for(int i = 0; i < size; i++) sum += array[i];
    return sum / size;
}

double CalculateATR(const MqlRates &rates[], int index, int period)
{
    double atr = 0.0;
    for(int i = MathMax(0, index - period + 1); i <= index; i++)
    {
        double tr = MathMax(rates[i].high - rates[i].low, 
                   MathMax(MathAbs(rates[i].high - rates[i-1].close),
                          MathAbs(rates[i].low - rates[i-1].close)));
        atr += tr;
    }
    return atr / period;
}

double CalculateTrend(const double &prices[], int size)
{
    int up = 0, down = 0;
    for(int i = 1; i < size; i++)
    {
        if(prices[i] > prices[i-1]) up++;
        else down++;
    }
    return (double)up / (up + down);
}

double CalculateRSI(const double &prices[], int size, int period)
{
    double gain = 0, loss = 0;
    for(int i = size - period; i < size; i++)
    {
        if(i <= 0) continue;
        double diff = prices[i] - prices[i-1];
        if(diff > 0) gain += diff;
        else loss -= diff;
    }
    gain /= period;
    loss /= period;
    return (loss == 0) ? 100.0 : 100.0 - (100.0 / (1.0 + gain / loss));
}

double CalculateBollinger(const double &prices[], int size)
{
    double sma = CalculateMean(prices, 20);
    double std = 0.0;
    for(int i = size - 20; i < size; i++)
    {
        double diff = prices[i] - sma;
        std += diff * diff;
    }
    std = MathSqrt(std / 20);
    
    double upper = sma + 2 * std;
    double lower = sma - 2 * std;
    
    return (upper == lower) ? 0.5 : (prices[size-1] - lower) / (upper - lower);
}

double CalculateMACD(const double &prices[], int size)
{
    double ema12 = prices[size-1];
    double ema26 = prices[size-1];
    
    for(int i = size - 12; i < size; i++)
    {
        ema12 = ema12 * 0.8462 + prices[i] * 0.1538;
    }
    
    for(int i = size - 26; i < size; i++)
    {
        ema26 = ema26 * 0.9259 + prices[i] * 0.0741;
    }
    
    return ema12 - ema26;
}

double CalculateStochastic(const MqlRates &rates[], int index, int period)
{
    double highest = rates[index].high;
    double lowest = rates[index].low;
    
    for(int i = index - period + 1; i <= index; i++)
    {
        if(rates[i].high > highest) highest = rates[i].high;
        if(rates[i].low < lowest) lowest = rates[i].low;
    }
    
    return (highest == lowest) ? 50.0 : 
           (rates[index].close - lowest) / (highest - lowest) * 100.0;
}

double CalculateSR(const double &prices[], int size)
{
    double highest = prices[0];
    double lowest = prices[0];
    
    for(int i = 1; i < size; i++)
    {
        if(prices[i] > highest) highest = prices[i];
        if(prices[i] < lowest) lowest = prices[i];
    }
    
    double range = highest - lowest;
    return (range == 0) ? 0.5 : (prices[size-1] - lowest) / range;
}

double CalculateFib(const double &prices[], int size)
{
    double high = prices[0];
    double low = prices[0];
    
    for(int i = 1; i < size; i++)
    {
        if(prices[i] > high) high = prices[i];
        if(prices[i] < low) low = prices[i];
    }
    
    double range = high - low;
    if(range == 0) return 0.5;
    
    double current = prices[size-1];
    double fib618 = low + range * 0.618;
    double fib382 = low + range * 0.382;
    
    double dist618 = MathAbs(current - fib618);
    double dist382 = MathAbs(current - fib382);
    
    return 1.0 - MathMin(dist618, dist382) / range;
}

double AnalyzeCandles(const MqlRates &rates[], int index)
{
    double score = 0.5;
    
    if(index < 2) return score;
    
    double body = MathAbs(rates[index].close - rates[index].open);
    double range = rates[index].high - rates[index].low;
    
    if(range == 0) return score;
    
    // Doji
    if(body < range * 0.1) score = 0.3;
    
    // Hammer
    double lower_shadow = MathMin(rates[index].open, rates[index].close) - rates[index].low;
    if(lower_shadow > body * 2) score = 0.7;
    
    // Shooting star
    double upper_shadow = rates[index].high - MathMax(rates[index].open, rates[index].close);
    if(upper_shadow > body * 2) score = 0.3;
    
    return score;
}

double CalculateImbalance()
{
    MqlTick tick;
    if(!SymbolInfoTick(_Symbol, tick)) return 0.0;
    
    double spread = tick.ask - tick.bid;
    double mid = (tick.ask + tick.bid) / 2.0;
    
    return (spread == 0) ? 0.0 : (tick.last - mid) / spread;
}

double CalculateTickFlow()
{
    MqlTick ticks[];
    int count = CopyTicks(_Symbol, ticks, COPY_TICKS_ALL, 0, 20);
    
    if(count < 2) return 0.0;
    
    int up = 0, down = 0;
    for(int i = 1; i < count; i++)
    {
        if(ticks[i].last > ticks[i-1].last) up++;
        else if(ticks[i].last < ticks[i-1].last) down++;
    }
    
    return (up + down == 0) ? 0.0 : (double)(up - down) / (up + down);
}

double CalculateIntradayPattern(int hour, int minute)
{
    if(hour >= 9 && hour <= 10) return 0.8;
    if(hour >= 14 && hour <= 15) return 0.7;
    if(hour >= 11 && hour <= 13) return 0.3;
    return 0.5;
}

double CalculateWeekdayFactor(int day)
{
    switch(day)
    {
        case 1: return 0.7; // Segunda
        case 2: return 0.9; // Terça
        case 3: return 0.8; // Quarta
        case 4: return 0.9; // Quinta
        case 5: return 0.6; // Sexta
        default: return 0.2; // Fim de semana
    }
}

double CalculateHourVolatility(int hour)
{
    if(hour >= 9 && hour <= 10) return 0.9;
    if(hour >= 14 && hour <= 16) return 0.8;
    if(hour >= 11 && hour <= 13) return 0.4;
    return 0.3;
}

double CalculateMarketPressure(const MqlRates &rates[], int index)
{
    double pressure = 0.0;
    for(int i = index - 4; i <= index; i++)
    {
        if(i < 0) continue;
        double vol = (double)rates[i].tick_volume;
        double dir = (rates[i].close > rates[i].open) ? 1.0 : -1.0;
        pressure += vol * dir;
    }
    return pressure / 10000.0; // Normalizar
}

double CalculateLiquidity(const double &volumes[], int size)
{
    double total = 0.0;
    for(int i = 0; i < size; i++) total += volumes[i];
    return total / size / 1000.0; // Normalizar
}

double CalculateMomentum(const double &prices[], int size)
{
    if(size < 5) return 0.0;
    return (prices[size-1] - prices[size-5]) / prices[size-5];
} 