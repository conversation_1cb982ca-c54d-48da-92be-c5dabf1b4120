//+------------------------------------------------------------------+
//|                                            EsgotamentoDetector.mqh |
//|         Módulo para detecção de esgotamento de movimento         |
//+------------------------------------------------------------------+
#ifndef __ESGOTAMENTO_DETECTOR_MQH__
#define __ESGOTAMENTO_DETECTOR_MQH__
#include <Trade/Trade.mqh>

//+------------------------------------------------------------------+
//| Classe para detecção de esgotamento                              |
//+------------------------------------------------------------------+
class CEsgotamentoDetector
{
public:
    //--- Construtor
    CEsgotamentoDetector();

    //--- Detecta esgotamento baseado em candle de exaustão
    bool DetectaEsgotamentoCandle(const MqlRates &rates[], int count, double body_ratio = 0.3, double shadow_ratio = 0.4);

    //--- Detecta esgotamento baseado em volume atípico
    bool DetectaEsgotamentoVolume(const MqlRates &rates[], int count, double volume_multiplier = 2.5);

    //--- Detecta esgotamento por indicador RSI (sobrecompra/sobrevenda)
    bool DetectaEsgotamentoRSI(const MqlRates &rates[], int count, double rsi_overbought = 80, double rsi_oversold = 20);

    //--- Detecta esgotamento por outros critérios (ex: RSI, divergência)
    bool DetectaEsgotamentoIndicador(const MqlRates &rates[], int count);
    
    //--- Método público para obter RSI atual (usado para lógica de direção)
    double CalculaRSI(const MqlRates &rates[], int count);

private:
    double MediaCorpoCandle(const MqlRates &rates[], int count, int start_index = 2);
    double MediaVolume(const MqlRates &rates[], int count, int start_index = 2);
};

//+------------------------------------------------------------------+
//| Implementação dos métodos                                        |
//+------------------------------------------------------------------+
CEsgotamentoDetector::CEsgotamentoDetector() {}

// Detecta candle de exaustão: corpo pequeno do penúltimo candle após sequência de candles grandes
bool CEsgotamentoDetector::DetectaEsgotamentoCandle(const MqlRates &rates[], int count, double body_ratio = 0.3, double shadow_ratio = 0.4)
{
    if(count < 5) return false;
    
    // Analisa o penúltimo candle fechado (rates[1] com ArraySetAsSeries=true)
    double corpo_penultimo = MathAbs(rates[1].close - rates[1].open);
    double sombra_superior = rates[1].high - MathMax(rates[1].open, rates[1].close);
    double sombra_inferior = MathMin(rates[1].open, rates[1].close) - rates[1].low;
    double range_total = rates[1].high - rates[1].low;
    
    // Calcula média do corpo dos 10 candles anteriores (não inclui o penúltimo)
    double media_corpo = MediaCorpoCandle(rates, 10, 2);
    if(media_corpo <= 0) return false;
    
    // Critérios de esgotamento configuráveis:
    // 1. Corpo pequeno (< body_ratio da média dos anteriores)
    // 2. Sombras longas (sombra > shadow_ratio do range total)
    // 3. Range total significativo (> 60% da média dos corpos)
    
    bool corpo_pequeno = (corpo_penultimo < body_ratio * media_corpo);
    bool sombras_longas = (sombra_superior > shadow_ratio * range_total) || (sombra_inferior > shadow_ratio * range_total);
    bool range_significativo = (range_total > 0.6 * media_corpo);
    
    bool esgotamento = corpo_pequeno && sombras_longas && range_significativo;
    
    // Log rico para debug
    if(esgotamento)
    {
        Print("╔═══ ESGOTAMENTO POR CANDLE DETECTADO ═══");
        Print("║ Corpo do candle: ", DoubleToString(corpo_penultimo,1), " pts (", 
              DoubleToString((corpo_penultimo/media_corpo)*100,1), "% da média)");
        Print("║ Média histórica: ", DoubleToString(media_corpo,1), " pts | Limite: ", 
              DoubleToString(body_ratio*100,0), "%");
        Print("║ Sombra superior: ", DoubleToString(sombra_superior,1), " pts (", 
              DoubleToString((sombra_superior/range_total)*100,1), "% do range)");
        Print("║ Sombra inferior: ", DoubleToString(sombra_inferior,1), " pts (", 
              DoubleToString((sombra_inferior/range_total)*100,1), "% do range)");
        Print("║ Range total: ", DoubleToString(range_total,1), " pts | Limite sombra: ", 
              DoubleToString(shadow_ratio*100,0), "%");
        Print("║ ✓ Critérios: Corpo pequeno + Sombras longas + Range significativo");
        Print("╚═══════════════════════════════════════");
    }
    
    return esgotamento;
}

// Detecta volume atípico: volume do penúltimo candle > multiplicador x média dos anteriores
bool CEsgotamentoDetector::DetectaEsgotamentoVolume(const MqlRates &rates[], int count, double volume_multiplier = 2.5)
{
    if(count < 10) return false;
    
    // Verificar se real volume está disponível, senão usar tick volume
    bool usar_real_volume = (rates[1].real_volume > 0);
    double vol_penultimo = usar_real_volume ? double(rates[1].real_volume) : double(rates[1].tick_volume);
    
    // Se nem tick volume está disponível, não pode detectar esgotamento por volume
    if(vol_penultimo <= 0)
    {
        static bool aviso_mostrado = false;
        if(!aviso_mostrado)
        {
            Print("⚠️ AVISO: Dados de volume indisponíveis - Método de esgotamento por volume DESABILITADO");
            aviso_mostrado = true;
        }
        return false;
    }
    
    // Média dos 15 candles anteriores (não inclui o penúltimo)
    double media_vol = MediaVolume(rates, 15, 2);
    if(media_vol <= 0) return false;
    
    // Esgotamento por volume: volume > volume_multiplier x a média
    bool esgotamento = (vol_penultimo > volume_multiplier * media_vol);
    
    if(esgotamento)
    {
        Print("╔═══ ESGOTAMENTO POR VOLUME DETECTADO ═══");
        Print("║ Tipo de volume: ", usar_real_volume ? "REAL VOLUME" : "TICK VOLUME (fallback)");
        Print("║ Volume atual: ", DoubleToString(vol_penultimo,0), usar_real_volume ? " contratos" : " ticks");
        Print("║ Média histórica: ", DoubleToString(media_vol,0), usar_real_volume ? " contratos" : " ticks", " (15 candles)");
        Print("║ Multiplicador: ", DoubleToString(vol_penultimo/media_vol,2), "x (limite: ", 
              DoubleToString(volume_multiplier,1), "x)");
        Print("║ Excesso de volume: +", DoubleToString(((vol_penultimo/media_vol)-1)*100,1), "%");
        if(!usar_real_volume)
        {
            Print("║ ⚠️ ATENÇÃO: Usando tick volume - precisão pode ser reduzida");
        }
        Print("║ ✓ Critério: Volume atípico indica exaustão de movimento");
        Print("╚═══════════════════════════════════════");
    }
    
    return esgotamento;
}

// Detecta esgotamento por RSI: sobrecompra/sobrevenda extrema
bool CEsgotamentoDetector::DetectaEsgotamentoRSI(const MqlRates &rates[], int count, double rsi_overbought, double rsi_oversold)
{
    if(count < 15) return false;
    
    double rsi = CalculaRSI(rates, count);
    
    // Esgotamento em níveis extremos
    bool esgotamento = (rsi > rsi_overbought || rsi < rsi_oversold);
    
    if(esgotamento)
    {
        string zona = (rsi > rsi_overbought) ? "SOBRECOMPRA" : "SOBREVENDA";
        string direcao_esperada = (rsi > rsi_overbought) ? "BAIXA" : "ALTA";
        
        Print("╔═══ ESGOTAMENTO POR RSI DETECTADO ═══");
        Print("║ RSI atual: ", DoubleToString(rsi,1));
        Print("║ Zona: ", zona, " (limite: ", 
              (rsi > rsi_overbought) ? DoubleToString(rsi_overbought,0) : DoubleToString(rsi_oversold,0), ")");
        Print("║ Excesso: ", DoubleToString(MathAbs(rsi - ((rsi > rsi_overbought) ? rsi_overbought : rsi_oversold)),1), " pontos");
        Print("║ Reversão esperada: ", direcao_esperada);
        Print("║ ✓ Critério: Níveis extremos indicam esgotamento");
        Print("╚═══════════════════════════════════════");
    }
    
    return esgotamento;
}

// Calcula média do corpo dos candles (excluindo índices iniciais)
double CEsgotamentoDetector::MediaCorpoCandle(const MqlRates &rates[], int count, int start_index = 2)
{
    if(count <= start_index) return 0;
    
    double soma = 0;
    int candles_analisados = 0;
    
    for(int i = start_index; i < start_index + count; i++)
    {
        soma += MathAbs(rates[i].close - rates[i].open);
        candles_analisados++;
    }
    
    return (candles_analisados > 0) ? soma / candles_analisados : 0;
}

// Calcula média de volume (excluindo índices iniciais)
double CEsgotamentoDetector::MediaVolume(const MqlRates &rates[], int count, int start_index = 2)
{
    if(count <= start_index) return 0;
    
    // Verificar se real volume está disponível no primeiro candle a ser analisado
    bool usar_real_volume = (rates[start_index].real_volume > 0);
    
    double soma = 0;
    int candles_analisados = 0;
    
    for(int i = start_index; i < start_index + count; i++)
    {
        double volume = usar_real_volume ? double(rates[i].real_volume) : double(rates[i].tick_volume);
        if(volume > 0) // Só inclui candles com volume válido
        {
            soma += volume;
            candles_analisados++;
        }
    }
    
    return (candles_analisados > 0) ? soma / candles_analisados : 0;
}

// Calcula RSI simples (fechamento dos últimos 14 períodos)
double CEsgotamentoDetector::CalculaRSI(const MqlRates &rates[], int count)
{
    if(count < 15) return 50; // Retorna neutro se não há dados suficientes
    
    int periodo_rsi = 14;
    double gain = 0, loss = 0;
    
    // Calcula ganhos e perdas médias dos últimos 14 períodos
    for(int i = 1; i <= periodo_rsi; i++)
    {
        double diff = rates[i-1].close - rates[i].close;
        if(diff > 0) 
            gain += diff;
        else 
            loss -= diff; // loss sempre positivo
    }
    
    // Médias
    gain /= periodo_rsi;
    loss /= periodo_rsi;
    
    if(loss == 0) return 100; // Se não há perdas, RSI = 100
    if(gain == 0) return 0;   // Se não há ganhos, RSI = 0
    
    double rs = gain / loss;
    double rsi = 100 - (100 / (1 + rs));
    
    return rsi;
}

bool CEsgotamentoDetector::DetectaEsgotamentoIndicador(const MqlRates &rates[], int count)
{
    // TODO: Implementar lógica de esgotamento por indicador adicional
    return false;
}

#endif // __ESGOTAMENTO_DETECTOR_MQH__ 