//+------------------------------------------------------------------+
//|                                                SlippageHunter.mq5 |
//|                                  Copyright 2024, Augment Trading |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Augment Trading"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "EA que detecta e aproveita slippage para ganhos adicionais"

//--- Includes
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Input parameters
input group "=== CONFIGURAÇÕES PRINCIPAIS ==="
input bool     InpEnabled = true;                    // Ativar EA
input int      InpLotSize = 1;                       // Tamanho do lote (B3 - lotes inteiros)
input int      InpMagicNumber = 123456;              // Número mágico
input int      InpTargetPoints = 10;                 // Alvo em pontos (múltiplo de 5)
input int      InpStopLossPoints = 15;               // Stop loss em pontos (múltiplo de 5)

input group "=== DETECÇÃO DE SLIPPAGE ==="
input int      InpSlippageHistorySize = 20;          // Histórico de slippage (trades)
input int      InpMinSlippageThreshold = 5;          // Slippage mínimo para trigger (pontos - múltiplo de 5)
input double   InpSlippageConfidenceLevel = 70.0;    // Nível de confiança % para entrada
input int      InpTickSpeedThreshold = 5;            // Velocidade mínima de ticks/segundo
input int      InpLearningTrades = 5;                // Trades de aprendizado (confiança reduzida)
input int      InpHistoryDays = 7;                   // Dias de histórico para análise inicial
input bool     InpUseAllTrades = true;               // Usar todas as ordens (não só do EA)
input double   InpEstimatedSlippage = 3.0;           // Slippage estimado inicial (pontos)

input group "=== ANÁLISE DE MERCADO ==="
input int      InpBookDepthLevels = 5;               // Níveis do book para análise
input double   InpBookImbalanceThreshold = 75.0;     // % desequilíbrio do book
input int      InpVolatilityPeriod = 14;             // Período para cálculo de volatilidade
input double   InpMaxSpreadPoints = 10.0;            // Spread máximo permitido (pontos - múltiplo de 5)

input group "=== GESTÃO DE RISCO ==="
input int      InpMaxPositions = 1;                  // Máximo de posições simultâneas
input double   InpMaxDailyLoss = 100.0;              // Perda máxima diária (pontos)
input int      InpCooldownSeconds = 30;              // Cooldown entre trades (segundos)

input group "=== PAINEL E NOTIFICAÇÕES ==="
input bool     InpShowPanel = true;                  // Mostrar painel informativo
input bool     InpSendNotifications = true;          // Enviar notificações push
input bool     InpVerboseLogging = true;             // Log detalhado

//--- Global variables
CTrade         trade;
CPositionInfo  position;
COrderInfo     order;

struct SlippageData
{
   datetime time;
   double   requested_price;
   double   executed_price;
   double   slippage_points;
   ENUM_ORDER_TYPE order_type;
   bool     is_valid;
};

SlippageData   slippage_history[];
int            slippage_history_count = 0;
datetime       last_trade_time = 0;
double         daily_loss_points = 0;
datetime       daily_reset_time = 0;
int            tick_count = 0;
datetime       tick_count_time = 0;

//--- Market analysis variables
double         last_bid = 0;
double         last_ask = 0;
int            consecutive_up_ticks = 0;
int            consecutive_down_ticks = 0;

//--- Panel variables
string         panel_objects[];
bool           panel_created = false;
const int      PANEL_WIDTH = 300;
const int      PANEL_HEIGHT = 400;
const int      PANEL_X = 20;
const int      PANEL_Y = 50;

//--- System validation variables
bool           book_available = false;
bool           symbol_valid = false;
bool           trading_allowed = false;
string         init_errors = "";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Setup trade object
   trade.SetExpertMagicNumber(InpMagicNumber);
   trade.SetDeviationInPoints(10);
   trade.SetTypeFilling(ORDER_FILLING_IOC);
   
   //--- Initialize arrays
   ArrayResize(slippage_history, InpSlippageHistorySize);

   //--- Initialize struct array manually
   for(int i = 0; i < InpSlippageHistorySize; i++)
   {
      slippage_history[i].time = 0;
      slippage_history[i].requested_price = 0;
      slippage_history[i].executed_price = 0;
      slippage_history[i].slippage_points = 0;
      slippage_history[i].order_type = ORDER_TYPE_BUY;
      slippage_history[i].is_valid = false;
   }

   //--- Reset daily counters
   ResetDailyCounters();

   //--- Load historical slippage data
   LoadHistoricalSlippage();
   
   //--- Validate system requirements
   if(!ValidateSystemRequirements())
   {
      Print("ERRO: Falha na validação do sistema - ", init_errors);
      if(InpShowPanel)
         CreateErrorPanel();
      return(INIT_FAILED);
   }

   //--- Initial market data
   last_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   last_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

   //--- Create panel if enabled
   if(InpShowPanel)
      CreatePanel();

   if(InpVerboseLogging)
      Print("SlippageHunter EA iniciado com sucesso - Versão 1.00 - B3 Edition");

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Remove panel objects
   if(panel_created)
      RemovePanel();

   if(InpVerboseLogging)
      Print("SlippageHunter EA finalizado - Motivo: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   if(!InpEnabled) return;
   
   //--- Check daily reset
   CheckDailyReset();
   
   //--- Check daily loss limit
   if(daily_loss_points >= InpMaxDailyLoss)
   {
      if(InpVerboseLogging)
         Print("Limite de perda diária atingido: ", daily_loss_points, " pontos");
      return;
   }
   
   //--- Update tick analysis
   UpdateTickAnalysis();
   
   //--- Check cooldown
   if(TimeCurrent() - last_trade_time < InpCooldownSeconds)
      return;
   
   //--- Check max positions
   if(CountOpenPositions() >= InpMaxPositions)
      return;
   
   //--- Check spread
   double spread = GetSpreadInPoints();
   if(spread > InpMaxSpreadPoints)
      return;

   //--- Verify book is still available (periodic check)
   static datetime last_book_check = 0;
   static int book_failures = 0;
   if(TimeCurrent() - last_book_check >= 60) // Check every minute
   {
      if(!VerifyBookAvailability())
      {
         book_failures++;
         if(InpVerboseLogging)
            Print("Book temporariamente indisponível (tentativa ", book_failures, "/3)");

         // Only pause after 3 consecutive failures
         if(book_failures >= 3)
         {
            if(InpVerboseLogging)
               Print("Book de ofertas perdido após 3 tentativas - EA pausado");
            return;
         }
      }
      else
      {
         if(book_failures > 0 && InpVerboseLogging)
            Print("Book de ofertas recuperado");
         book_failures = 0; // Reset counter on success
      }
      last_book_check = TimeCurrent();
   }
   
   //--- Analyze market conditions
   if(ShouldEnterTrade())
   {
      ENUM_ORDER_TYPE order_type = GetTradeDirection();
      if(order_type != WRONG_VALUE)
      {
         ExecuteTrade(order_type);
      }
   }

   //--- Update panel
   if(InpShowPanel && panel_created)
      UpdatePanel();
}

//+------------------------------------------------------------------+
//| Trade transaction function                                       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
   //--- Process only our trades
   if(request.magic != InpMagicNumber) return;
   
   //--- Record slippage data
   if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
   {
      RecordSlippageData(trans, request, result);
   }
   
   //--- Update daily loss on position close
   if(trans.type == TRADE_TRANSACTION_HISTORY_ADD)
   {
      UpdateDailyLoss(trans);
   }
}

//+------------------------------------------------------------------+
//| Update tick analysis                                             |
//+------------------------------------------------------------------+
void UpdateTickAnalysis()
{
   double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   
   //--- Count tick speed
   datetime current_time = TimeCurrent();
   if(current_time != tick_count_time)
   {
      if(current_time - tick_count_time == 1) // Same second
         tick_count++;
      else
      {
         tick_count = 1;
         tick_count_time = current_time;
      }
   }
   
   //--- Analyze price movement direction
   if(current_bid > last_bid)
   {
      consecutive_up_ticks++;
      consecutive_down_ticks = 0;
   }
   else if(current_bid < last_bid)
   {
      consecutive_down_ticks++;
      consecutive_up_ticks = 0;
   }
   
   last_bid = current_bid;
   last_ask = current_ask;
}

//+------------------------------------------------------------------+
//| Check if should enter trade                                      |
//+------------------------------------------------------------------+
bool ShouldEnterTrade()
{
   //--- Check if book is available and balanced
   if(!book_available)
      return false;

   //--- Check tick speed
   if(tick_count < InpTickSpeedThreshold)
      return false;

   //--- Check slippage confidence (with learning phase adjustment)
   double slippage_confidence = CalculateSlippageConfidence();
   double required_confidence = InpSlippageConfidenceLevel;

   // During learning phase, reduce confidence requirement
   if(slippage_history_count < InpLearningTrades)
   {
      required_confidence = MathMax(50.0, InpSlippageConfidenceLevel - 30.0);
      if(InpVerboseLogging && slippage_confidence >= required_confidence)
      {
         static datetime last_learning_log = 0;
         if(TimeCurrent() - last_learning_log >= 300) // Log every 5 minutes
         {
            Print("MODO APRENDIZADO: Confiança reduzida ", required_confidence, "% (", slippage_history_count, "/", InpLearningTrades, " trades)");
            last_learning_log = TimeCurrent();
         }
      }
   }

   if(slippage_confidence < required_confidence)
      return false;

   //--- Check book imbalance (but be more tolerant of temporary imbalances)
   double book_imbalance = CalculateBookImbalance();
   if(book_imbalance == 50.0) // This means book is incomplete or has issues
      return false;

   // For actual imbalance, use the configured threshold
   if(book_imbalance < InpBookImbalanceThreshold && book_imbalance > (100 - InpBookImbalanceThreshold))
      return false;

   //--- Check momentum
   if(consecutive_up_ticks < 3 && consecutive_down_ticks < 3)
      return false;

   return true;
}

//+------------------------------------------------------------------+
//| Get trade direction based on analysis                           |
//+------------------------------------------------------------------+
ENUM_ORDER_TYPE GetTradeDirection()
{
   double avg_slippage_buy = GetAverageSlippage(ORDER_TYPE_BUY);
   double avg_slippage_sell = GetAverageSlippage(ORDER_TYPE_SELL);
   
   //--- Favor direction with positive slippage history
   if(consecutive_up_ticks >= 3 && avg_slippage_buy > InpMinSlippageThreshold)
      return ORDER_TYPE_BUY;
   
   if(consecutive_down_ticks >= 3 && avg_slippage_sell > InpMinSlippageThreshold)
      return ORDER_TYPE_SELL;
   
   return WRONG_VALUE;
}

//+------------------------------------------------------------------+
//| Execute trade                                                    |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE order_type)
{
   double price = (order_type == ORDER_TYPE_BUY) ?
                  SymbolInfoDouble(_Symbol, SYMBOL_ASK) :
                  SymbolInfoDouble(_Symbol, SYMBOL_BID);

   //--- Normalize price for B3 (must end in 0 or 5)
   price = NormalizePriceB3(price);

   double sl = 0, tp = 0;

   if(order_type == ORDER_TYPE_BUY)
   {
      sl = NormalizePriceB3(price - InpStopLossPoints * _Point);
      tp = NormalizePriceB3(price + InpTargetPoints * _Point);
   }
   else
   {
      sl = NormalizePriceB3(price + InpStopLossPoints * _Point);
      tp = NormalizePriceB3(price - InpTargetPoints * _Point);
   }
   
   bool result = false;
   if(order_type == ORDER_TYPE_BUY)
      result = trade.Buy((double)InpLotSize, _Symbol, price, sl, tp, "SlippageHunter");
   else
      result = trade.Sell((double)InpLotSize, _Symbol, price, sl, tp, "SlippageHunter");
   
   if(result)
   {
      last_trade_time = TimeCurrent();
      
      if(InpSendNotifications)
      {
         string msg = StringFormat("🎯 SlippageHunter: %s %.2f lotes em %.5f", 
                                  (order_type == ORDER_TYPE_BUY) ? "COMPRA" : "VENDA",
                                  InpLotSize, price);
         SendNotification(msg);
      }
      
      if(InpVerboseLogging)
         Print("Trade executado: ", EnumToString(order_type), " em ", price);
   }
   else
   {
      if(InpVerboseLogging)
         Print("Erro ao executar trade: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
   }
}

//+------------------------------------------------------------------+
//| Record slippage data from trade execution                       |
//+------------------------------------------------------------------+
void RecordSlippageData(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
   if(trans.type != TRADE_TRANSACTION_DEAL_ADD) return;

   double requested_price = request.price;
   double executed_price = trans.price;
   double slippage_points = 0;

   //--- Calculate slippage in points
   if(request.type == ORDER_TYPE_BUY)
      slippage_points = (executed_price - requested_price) / _Point;
   else if(request.type == ORDER_TYPE_SELL)
      slippage_points = (requested_price - executed_price) / _Point;

   //--- Store in history array (circular buffer)
   int index = slippage_history_count % InpSlippageHistorySize;
   slippage_history[index].time = TimeCurrent();
   slippage_history[index].requested_price = requested_price;
   slippage_history[index].executed_price = executed_price;
   slippage_history[index].slippage_points = slippage_points;
   slippage_history[index].order_type = request.type;
   slippage_history[index].is_valid = true;

   slippage_history_count++;

   if(InpVerboseLogging)
   {
      Print("Slippage registrado: ", slippage_points, " pontos - ",
            "Solicitado: ", requested_price, " Executado: ", executed_price);
   }
}

//+------------------------------------------------------------------+
//| Calculate slippage confidence level                             |
//+------------------------------------------------------------------+
double CalculateSlippageConfidence()
{
   if(slippage_history_count == 0)
   {
      // During learning phase, use reduced confidence threshold
      return 50.0; // Allows initial trades for learning
   }

   int positive_slippage = 0;
   int total_valid = 0;
   int max_check = MathMin(slippage_history_count, InpSlippageHistorySize);

   for(int i = 0; i < max_check; i++)
   {
      if(slippage_history[i].is_valid)
      {
         total_valid++;
         if(slippage_history[i].slippage_points > 0)
            positive_slippage++;
      }
   }

   if(total_valid == 0) return 50.0; // Learning phase confidence

   // If we have few trades, be more optimistic to allow learning
   if(total_valid < InpLearningTrades)
   {
      double base_confidence = (double)positive_slippage / total_valid * 100.0;
      // Boost confidence during learning phase (but cap at 80%)
      return MathMin(base_confidence + 20.0, 80.0);
   }

   // Normal calculation after learning phase
   return (double)positive_slippage / total_valid * 100.0;
}

//+------------------------------------------------------------------+
//| Get average slippage for specific order type                    |
//+------------------------------------------------------------------+
double GetAverageSlippage(ENUM_ORDER_TYPE order_type)
{
   double total_slippage = 0;
   int count = 0;
   int max_check = MathMin(slippage_history_count, InpSlippageHistorySize);

   for(int i = 0; i < max_check; i++)
   {
      if(slippage_history[i].is_valid && slippage_history[i].order_type == order_type)
      {
         total_slippage += slippage_history[i].slippage_points;
         count++;
      }
   }

   return (count > 0) ? total_slippage / count : 0.0;
}

//+------------------------------------------------------------------+
//| Calculate book imbalance (simplified version)                   |
//+------------------------------------------------------------------+
double CalculateBookImbalance()
{
   //--- Check if book is available
   if(!book_available)
   {
      if(InpVerboseLogging)
         Print("Book de ofertas não disponível para análise");
      return 50.0; // Neutral
   }

   //--- Get market depth
   MqlBookInfo book[];
   if(!MarketBookGet(_Symbol, book))
   {
      if(InpVerboseLogging)
         Print("Falha ao obter dados do book de ofertas");
      return 50.0; // Neutral if can't get book data
   }

   int book_size = ArraySize(book);
   if(book_size < InpBookDepthLevels * 2)
   {
      if(InpVerboseLogging)
         Print("Book insuficiente: ", book_size, " níveis (mínimo: ", InpBookDepthLevels * 2, ")");
      return 50.0; // Neutral if insufficient levels
   }

   double buy_volume = 0, sell_volume = 0;
   int levels_to_check = MathMin(book_size, InpBookDepthLevels * 2);
   int buy_levels = 0, sell_levels = 0;

   for(int i = 0; i < levels_to_check; i++)
   {
      if(book[i].type == BOOK_TYPE_BUY)
      {
         buy_volume += (double)book[i].volume;
         buy_levels++;
      }
      else if(book[i].type == BOOK_TYPE_SELL)
      {
         sell_volume += (double)book[i].volume;
         sell_levels++;
      }
   }

   //--- Validate we have both sides
   if(buy_levels == 0 || sell_levels == 0)
   {
      static datetime last_imbalance_log = 0;
      if(InpVerboseLogging && TimeCurrent() - last_imbalance_log >= 30) // Log only every 30 seconds
      {
         Print("Book temporariamente desequilibrado: Buy=", buy_levels, " Sell=", sell_levels, " níveis");
         last_imbalance_log = TimeCurrent();
      }
      return 50.0;
   }

   double total_volume = buy_volume + sell_volume;
   if(total_volume == 0)
   {
      if(InpVerboseLogging)
         Print("Volume total do book é zero");
      return 50.0;
   }

   double imbalance = (buy_volume / total_volume) * 100.0;

   static datetime last_strong_imbalance_log = 0;
   if(InpVerboseLogging && (imbalance > 85.0 || imbalance < 15.0) && TimeCurrent() - last_strong_imbalance_log >= 60)
   {
      Print("Book fortemente desequilibrado: ", DoubleToString(imbalance, 1), "% comprador");
      last_strong_imbalance_log = TimeCurrent();
   }

   return imbalance;
}

//+------------------------------------------------------------------+
//| Count open positions with our magic number                      |
//+------------------------------------------------------------------+
int CountOpenPositions()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol && position.Magic() == InpMagicNumber)
            count++;
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| Get current spread in points                                    |
//+------------------------------------------------------------------+
double GetSpreadInPoints()
{
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   return (ask - bid) / _Point;
}

//+------------------------------------------------------------------+
//| Update daily loss tracking                                      |
//+------------------------------------------------------------------+
void UpdateDailyLoss(const MqlTradeTransaction& trans)
{
   if(trans.type != TRADE_TRANSACTION_HISTORY_ADD) return;

   //--- Get deal info
   if(HistoryDealSelect(trans.deal))
   {
      long deal_magic = HistoryDealGetInteger(trans.deal, DEAL_MAGIC);
      if(deal_magic != InpMagicNumber) return;

      double deal_profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
      if(deal_profit < 0)
      {
         daily_loss_points += MathAbs(deal_profit / _Point);

         if(InpVerboseLogging)
            Print("Perda diária atualizada: ", daily_loss_points, " pontos");
      }
   }
}

//+------------------------------------------------------------------+
//| Reset daily counters                                            |
//+------------------------------------------------------------------+
void ResetDailyCounters()
{
   datetime current_time = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(current_time, dt);
   dt.hour = 0;
   dt.min = 0;
   dt.sec = 0;

   datetime today_start = StructToTime(dt);

   if(daily_reset_time != today_start)
   {
      daily_loss_points = 0;
      daily_reset_time = today_start;

      if(InpVerboseLogging)
         Print("Contadores diários resetados");
   }
}

//+------------------------------------------------------------------+
//| Check if daily reset is needed                                  |
//+------------------------------------------------------------------+
void CheckDailyReset()
{
   static datetime last_check = 0;
   datetime current_time = TimeCurrent();

   if(current_time - last_check >= 3600) // Check every hour
   {
      ResetDailyCounters();
      last_check = current_time;
   }
}

//+------------------------------------------------------------------+
//| Normalize price for B3 (must end in 0 or 5)                    |
//+------------------------------------------------------------------+
double NormalizePriceB3(double price)
{
   //--- Convert to points
   long price_points = (long)MathRound(price / _Point);

   //--- Get last digit
   int last_digit = (int)(price_points % 10);

   //--- Adjust to nearest 0 or 5
   if(last_digit < 3)
      price_points = (price_points / 10) * 10; // Round down to 0
   else if(last_digit < 8)
      price_points = (price_points / 10) * 10 + 5; // Round to 5
   else
      price_points = ((price_points / 10) + 1) * 10; // Round up to next 0

   return price_points * _Point;
}

//+------------------------------------------------------------------+
//| Create information panel                                         |
//+------------------------------------------------------------------+
void CreatePanel()
{
   if(panel_created) return;

   //--- Main panel background
   CreateRectangle("SlippagePanel_BG", PANEL_X, PANEL_Y, PANEL_WIDTH, PANEL_HEIGHT,
                   clrDarkSlateGray, BORDER_FLAT, 1, clrSilver);

   //--- Title
   CreateLabel("SlippagePanel_Title", PANEL_X + 10, PANEL_Y + 10,
               "SLIPPAGE HUNTER - B3", clrWhite, 12, "Arial Bold");

   //--- Status labels
   int y_pos = PANEL_Y + 40;
   CreateLabel("SlippagePanel_Status", PANEL_X + 10, y_pos, "Status:", clrLightGray, 9);
   CreateLabel("SlippagePanel_StatusValue", PANEL_X + 80, y_pos, "Iniciando...", clrYellow, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_Confidence", PANEL_X + 10, y_pos, "Confiança:", clrLightGray, 9);
   CreateLabel("SlippagePanel_ConfidenceValue", PANEL_X + 80, y_pos, "0%", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_AvgSlippage", PANEL_X + 10, y_pos, "Slippage Médio:", clrLightGray, 9);
   CreateLabel("SlippagePanel_AvgSlippageValue", PANEL_X + 110, y_pos, "0 pts", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_TickSpeed", PANEL_X + 10, y_pos, "Velocidade:", clrLightGray, 9);
   CreateLabel("SlippagePanel_TickSpeedValue", PANEL_X + 80, y_pos, "0 t/s", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_BookBalance", PANEL_X + 10, y_pos, "Book Balance:", clrLightGray, 9);
   CreateLabel("SlippagePanel_BookBalanceValue", PANEL_X + 100, y_pos, "50%", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_BookStatus", PANEL_X + 10, y_pos, "Book Status:", clrLightGray, 9);
   CreateLabel("SlippagePanel_BookStatusValue", PANEL_X + 90, y_pos, "Verificando...", clrYellow, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_Momentum", PANEL_X + 10, y_pos, "Momentum:", clrLightGray, 9);
   CreateLabel("SlippagePanel_MomentumValue", PANEL_X + 80, y_pos, "Neutro", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_Spread", PANEL_X + 10, y_pos, "Spread:", clrLightGray, 9);
   CreateLabel("SlippagePanel_SpreadValue", PANEL_X + 60, y_pos, "0 pts", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_Positions", PANEL_X + 10, y_pos, "Posições:", clrLightGray, 9);
   CreateLabel("SlippagePanel_PositionsValue", PANEL_X + 70, y_pos, "0", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_DailyLoss", PANEL_X + 10, y_pos, "Perda Diária:", clrLightGray, 9);
   CreateLabel("SlippagePanel_DailyLossValue", PANEL_X + 90, y_pos, "0 pts", clrLime, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_LastTrade", PANEL_X + 10, y_pos, "Último Trade:", clrLightGray, 9);
   CreateLabel("SlippagePanel_LastTradeValue", PANEL_X + 90, y_pos, "Nenhum", clrWhite, 9);

   //--- Separator line
   y_pos += 30;
   CreateRectangle("SlippagePanel_Line", PANEL_X + 10, y_pos, PANEL_WIDTH - 20, 1,
                   clrSilver, BORDER_FLAT, 1, clrSilver);

   //--- Trade history section
   y_pos += 15;
   CreateLabel("SlippagePanel_HistoryTitle", PANEL_X + 10, y_pos, "HISTÓRICO SLIPPAGE:", clrYellow, 10, "Arial Bold");

   y_pos += 25;
   CreateLabel("SlippagePanel_History1", PANEL_X + 10, y_pos, "Aguardando dados...", clrLightGray, 8);

   panel_created = true;

   if(InpVerboseLogging)
      Print("Painel SlippageHunter criado com sucesso");
}

//+------------------------------------------------------------------+
//| Update panel information                                         |
//+------------------------------------------------------------------+
void UpdatePanel()
{
   if(!panel_created) return;

   //--- Status
   string status = InpEnabled ? (ShouldEnterTrade() ? "PRONTO" : "AGUARDANDO") : "DESABILITADO";
   color status_color = InpEnabled ? (ShouldEnterTrade() ? clrLime : clrYellow) : clrRed;
   UpdateLabel("SlippagePanel_StatusValue", status, status_color);

   //--- Confidence with learning mode indicator
   double confidence = CalculateSlippageConfidence();
   string confidence_text = StringFormat("%.1f%%", confidence);
   color confidence_color = clrOrange;

   // Determine required confidence level
   double required_confidence = InpSlippageConfidenceLevel;
   if(slippage_history_count < InpLearningTrades)
   {
      required_confidence = MathMax(50.0, InpSlippageConfidenceLevel - 30.0);
      confidence_text += StringFormat(" (L:%d/%d)", slippage_history_count, InpLearningTrades);
   }

   if(confidence >= required_confidence)
      confidence_color = clrLime;
   else if(slippage_history_count < InpLearningTrades)
      confidence_color = clrYellow; // Learning mode

   UpdateLabel("SlippagePanel_ConfidenceValue", confidence_text, confidence_color);

   //--- Average slippage with learning mode indicator
   double avg_slip_buy = GetAverageSlippage(ORDER_TYPE_BUY);
   double avg_slip_sell = GetAverageSlippage(ORDER_TYPE_SELL);
   double avg_slip = (avg_slip_buy + avg_slip_sell) / 2;

   string slip_text = "";
   color slip_color = clrOrange;

   if(slippage_history_count == 0)
   {
      slip_text = "Aprendendo...";
      slip_color = clrYellow;
   }
   else
   {
      slip_text = StringFormat("%.1f pts", avg_slip);
      slip_color = avg_slip > 0 ? clrLime : clrOrange;
   }

   UpdateLabel("SlippagePanel_AvgSlippageValue", slip_text, slip_color);

   //--- Tick speed
   UpdateLabel("SlippagePanel_TickSpeedValue", StringFormat("%d t/s", tick_count),
               tick_count >= InpTickSpeedThreshold ? clrLime : clrOrange);

   //--- Book balance
   double book_balance = CalculateBookImbalance();
   UpdateLabel("SlippagePanel_BookBalanceValue", StringFormat("%.1f%%", book_balance),
               book_balance > 60 || book_balance < 40 ? clrLime : clrOrange);

   //--- Book status with detailed info
   string book_status = "FALHA";
   color book_color = clrRed;

   MqlBookInfo book[];
   if(MarketBookGet(_Symbol, book))
   {
      int book_size = ArraySize(book);
      int buy_levels = 0, sell_levels = 0;

      // Count levels by type
      for(int i = 0; i < book_size; i++)
      {
         if(book[i].type == BOOK_TYPE_BUY && book[i].volume > 0) buy_levels++;
         else if(book[i].type == BOOK_TYPE_SELL && book[i].volume > 0) sell_levels++;
      }

      if(buy_levels > 0 && sell_levels > 0)
      {
         book_status = StringFormat("OK (B:%d S:%d)", buy_levels, sell_levels);
         book_color = clrLime;
      }
      else if(buy_levels == 0 && sell_levels > 0)
      {
         book_status = StringFormat("SÓ VENDA (%d)", sell_levels);
         book_color = clrOrange;
      }
      else if(buy_levels > 0 && sell_levels == 0)
      {
         book_status = StringFormat("SÓ COMPRA (%d)", buy_levels);
         book_color = clrOrange;
      }
      else
      {
         book_status = "SEM ORDENS";
         book_color = clrRed;
      }
   }
   else
   {
      book_status = "SEM DADOS";
      book_color = clrRed;
   }

   UpdateLabel("SlippagePanel_BookStatusValue", book_status, book_color);

   //--- Momentum
   string momentum = "Neutro";
   color momentum_color = clrWhite;
   if(consecutive_up_ticks >= 3) { momentum = StringFormat("Alta +%d", consecutive_up_ticks); momentum_color = clrLime; }
   else if(consecutive_down_ticks >= 3) { momentum = StringFormat("Baixa -%d", consecutive_down_ticks); momentum_color = clrRed; }
   UpdateLabel("SlippagePanel_MomentumValue", momentum, momentum_color);

   //--- Spread
   double spread = GetSpreadInPoints();
   UpdateLabel("SlippagePanel_SpreadValue", StringFormat("%.0f pts", spread),
               spread <= InpMaxSpreadPoints ? clrLime : clrRed);

   //--- Positions
   int positions = CountOpenPositions();
   UpdateLabel("SlippagePanel_PositionsValue", IntegerToString(positions),
               positions > 0 ? clrYellow : clrWhite);

   //--- Daily loss
   UpdateLabel("SlippagePanel_DailyLossValue", StringFormat("%.0f pts", daily_loss_points),
               daily_loss_points < InpMaxDailyLoss * 0.8 ? clrLime : clrRed);

   //--- Last trade
   if(last_trade_time > 0)
   {
      int seconds_ago = (int)(TimeCurrent() - last_trade_time);
      string time_text = seconds_ago < 60 ? StringFormat("%ds atrás", seconds_ago) :
                        seconds_ago < 3600 ? StringFormat("%dm atrás", seconds_ago/60) :
                        StringFormat("%dh atrás", seconds_ago/3600);
      UpdateLabel("SlippagePanel_LastTradeValue", time_text, clrWhite);
   }

   //--- Update slippage history
   UpdateSlippageHistory();
}

//+------------------------------------------------------------------+
//| Update slippage history display                                 |
//+------------------------------------------------------------------+
void UpdateSlippageHistory()
{
   string history_text = "";
   color history_color = clrLightGray;

   if(slippage_history_count == 0)
   {
      history_text = "Modo aprendizado - Aguardando primeiro trade";
      history_color = clrYellow;
   }
   else
   {
      int max_show = MathMin(slippage_history_count, 5);
      int start_index = (slippage_history_count - 1) % InpSlippageHistorySize;

      for(int i = 0; i < max_show; i++)
      {
         int index = (start_index - i + InpSlippageHistorySize) % InpSlippageHistorySize;
         if(slippage_history[index].is_valid)
         {
            string type_str = (slippage_history[index].order_type == ORDER_TYPE_BUY) ? "C" : "V";
            history_text += StringFormat("%s: %.1f pts | ", type_str, slippage_history[index].slippage_points);
         }
      }

      if(StringLen(history_text) > 0)
      {
         history_text = StringSubstr(history_text, 0, StringLen(history_text) - 3); // Remove last " | "
         history_color = clrLightGray;
      }
      else
      {
         history_text = "Dados carregados mas sem trades válidos";
         history_color = clrOrange;
      }
   }

   UpdateLabel("SlippagePanel_History1", history_text, history_color);
}

//+------------------------------------------------------------------+
//| Create rectangle object                                          |
//+------------------------------------------------------------------+
void CreateRectangle(string name, int x, int y, int width, int height,
                    color bg_color, ENUM_BORDER_TYPE border, int border_width, color border_color)
{
   ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
   ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
   ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg_color);
   ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, border);
   ObjectSetInteger(0, name, OBJPROP_WIDTH, border_width);
   ObjectSetInteger(0, name, OBJPROP_COLOR, border_color);
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| Create label object                                              |
//+------------------------------------------------------------------+
void CreateLabel(string name, int x, int y, string text, color text_color, int font_size, string font_name = "Arial")
{
   ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_COLOR, text_color);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, font_size);
   ObjectSetString(0, name, OBJPROP_FONT, font_name);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| Update label text and color                                     |
//+------------------------------------------------------------------+
void UpdateLabel(string name, string text, color text_color)
{
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_COLOR, text_color);
}

//+------------------------------------------------------------------+
//| Remove all panel objects                                        |
//+------------------------------------------------------------------+
void RemovePanel()
{
   ObjectDelete(0, "SlippagePanel_BG");
   ObjectDelete(0, "SlippagePanel_Title");
   ObjectDelete(0, "SlippagePanel_Status");
   ObjectDelete(0, "SlippagePanel_StatusValue");
   ObjectDelete(0, "SlippagePanel_Confidence");
   ObjectDelete(0, "SlippagePanel_ConfidenceValue");
   ObjectDelete(0, "SlippagePanel_AvgSlippage");
   ObjectDelete(0, "SlippagePanel_AvgSlippageValue");
   ObjectDelete(0, "SlippagePanel_TickSpeed");
   ObjectDelete(0, "SlippagePanel_TickSpeedValue");
   ObjectDelete(0, "SlippagePanel_BookBalance");
   ObjectDelete(0, "SlippagePanel_BookBalanceValue");
   ObjectDelete(0, "SlippagePanel_BookStatus");
   ObjectDelete(0, "SlippagePanel_BookStatusValue");
   ObjectDelete(0, "SlippagePanel_Momentum");
   ObjectDelete(0, "SlippagePanel_MomentumValue");
   ObjectDelete(0, "SlippagePanel_Spread");
   ObjectDelete(0, "SlippagePanel_SpreadValue");
   ObjectDelete(0, "SlippagePanel_Positions");
   ObjectDelete(0, "SlippagePanel_PositionsValue");
   ObjectDelete(0, "SlippagePanel_DailyLoss");
   ObjectDelete(0, "SlippagePanel_DailyLossValue");
   ObjectDelete(0, "SlippagePanel_LastTrade");
   ObjectDelete(0, "SlippagePanel_LastTradeValue");
   ObjectDelete(0, "SlippagePanel_Line");
   ObjectDelete(0, "SlippagePanel_HistoryTitle");
   ObjectDelete(0, "SlippagePanel_History1");

   panel_created = false;

   if(InpVerboseLogging)
      Print("Painel SlippageHunter removido");
}

//+------------------------------------------------------------------+
//| Validate system requirements on initialization                  |
//+------------------------------------------------------------------+
bool ValidateSystemRequirements()
{
   init_errors = "";
   bool all_valid = true;

   //--- Check symbol validity
   if(!SymbolSelect(_Symbol, true))
   {
      init_errors += "Símbolo inválido ou não disponível; ";
      all_valid = false;
   }
   else
   {
      symbol_valid = true;
      if(InpVerboseLogging)
         Print("✓ Símbolo validado: ", _Symbol);
   }

   //--- Check trading permissions
   if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
   {
      init_errors += "Trading não permitido no terminal; ";
      all_valid = false;
   }
   else if(!MQLInfoInteger(MQL_TRADE_ALLOWED))
   {
      init_errors += "Trading não permitido para EAs; ";
      all_valid = false;
   }
   else if(!AccountInfoInteger(ACCOUNT_TRADE_EXPERT))
   {
      init_errors += "Trading automático não permitido na conta; ";
      all_valid = false;
   }
   else
   {
      trading_allowed = true;
      if(InpVerboseLogging)
         Print("✓ Permissões de trading validadas");
   }

   //--- Check market depth availability
   if(!ValidateBookDepth())
   {
      init_errors += "Book de ofertas não disponível ou insuficiente; ";
      all_valid = false;
   }
   else
   {
      book_available = true;
      if(InpVerboseLogging)
         Print("✓ Book de ofertas validado");
   }

   //--- Check symbol specifications
   if(!ValidateSymbolSpecs())
   {
      init_errors += "Especificações do símbolo inadequadas; ";
      all_valid = false;
   }
   else
   {
      if(InpVerboseLogging)
         Print("✓ Especificações do símbolo validadas");
   }

   //--- Check input parameters
   if(!ValidateInputParameters())
   {
      init_errors += "Parâmetros de entrada inválidos; ";
      all_valid = false;
   }
   else
   {
      if(InpVerboseLogging)
         Print("✓ Parâmetros de entrada validados");
   }

   //--- Final validation summary
   if(all_valid)
   {
      Print("✅ SISTEMA VALIDADO - SlippageHunter pronto para operar");
   }
   else
   {
      Print("❌ FALHA NA VALIDAÇÃO - Erros: ", init_errors);
   }

   return all_valid;
}

//+------------------------------------------------------------------+
//| Validate book depth availability and quality                    |
//+------------------------------------------------------------------+
bool ValidateBookDepth()
{
   //--- Try to subscribe to market depth
   if(!MarketBookAdd(_Symbol))
   {
      if(InpVerboseLogging)
         Print("Falha ao subscrever book de ofertas para ", _Symbol);
      return false;
   }

   //--- Wait a moment for data
   Sleep(100);

   //--- Test book data availability
   MqlBookInfo book[];
   if(!MarketBookGet(_Symbol, book))
   {
      if(InpVerboseLogging)
         Print("Não foi possível obter dados do book de ofertas");
      return false;
   }

   int book_size = ArraySize(book);
   int required_levels = InpBookDepthLevels * 2; // Both buy and sell sides

   if(book_size < required_levels)
   {
      if(InpVerboseLogging)
         Print("Book insuficiente: ", book_size, " níveis disponíveis, ", required_levels, " requeridos");
      return false;
   }

   //--- Check if we have both buy and sell levels
   bool has_buy = false, has_sell = false;
   for(int i = 0; i < book_size; i++)
   {
      if(book[i].type == BOOK_TYPE_BUY) has_buy = true;
      if(book[i].type == BOOK_TYPE_SELL) has_sell = true;
   }

   if(!has_buy || !has_sell)
   {
      if(InpVerboseLogging)
         Print("Book incompleto - Buy: ", has_buy, " Sell: ", has_sell);
      return false;
   }

   if(InpVerboseLogging)
      Print("Book validado: ", book_size, " níveis (", required_levels, " requeridos)");

   return true;
}

//+------------------------------------------------------------------+
//| Validate symbol specifications                                  |
//+------------------------------------------------------------------+
bool ValidateSymbolSpecs()
{
   //--- Check if symbol is tradeable
   if(!SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE))
   {
      if(InpVerboseLogging)
         Print("Símbolo não é negociável: ", _Symbol);
      return false;
   }

   //--- Check minimum lot size
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   if(InpLotSize < min_lot)
   {
      if(InpVerboseLogging)
         Print("Lote configurado (", InpLotSize, ") menor que mínimo (", min_lot, ")");
      return false;
   }

   //--- Check lot step
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   if(MathMod(InpLotSize, lot_step) != 0)
   {
      if(InpVerboseLogging)
         Print("Lote (", InpLotSize, ") não é múltiplo do step (", lot_step, ")");
      return false;
   }

   //--- Check point value
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   if(point <= 0)
   {
      if(InpVerboseLogging)
         Print("Valor do ponto inválido: ", point);
      return false;
   }

   //--- Check tick size (should be 5 points for B3)
   double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   double expected_tick = 5 * point;

   if(MathAbs(tick_size - expected_tick) > point * 0.1) // Small tolerance
   {
      if(InpVerboseLogging)
         Print("AVISO: Tick size (", tick_size, ") diferente do esperado para B3 (", expected_tick, ")");
      // Don't fail, just warn
   }

   if(InpVerboseLogging)
   {
      Print("Especificações do símbolo:");
      Print("- Lote mínimo: ", min_lot);
      Print("- Step do lote: ", lot_step);
      Print("- Valor do ponto: ", point);
      Print("- Tick size: ", tick_size);
   }

   return true;
}

//+------------------------------------------------------------------+
//| Validate input parameters                                       |
//+------------------------------------------------------------------+
bool ValidateInputParameters()
{
   //--- Check lot size
   if(InpLotSize <= 0)
   {
      if(InpVerboseLogging)
         Print("Tamanho do lote deve ser positivo: ", InpLotSize);
      return false;
   }

   //--- Check points are multiples of 5 (B3 requirement)
   if(InpTargetPoints % 5 != 0)
   {
      if(InpVerboseLogging)
         Print("Alvo deve ser múltiplo de 5: ", InpTargetPoints);
      return false;
   }

   if(InpStopLossPoints % 5 != 0)
   {
      if(InpVerboseLogging)
         Print("Stop loss deve ser múltiplo de 5: ", InpStopLossPoints);
      return false;
   }

   if(InpMinSlippageThreshold % 5 != 0)
   {
      if(InpVerboseLogging)
         Print("Slippage mínimo deve ser múltiplo de 5: ", InpMinSlippageThreshold);
      return false;
   }

   //--- Check logical relationships
   if(InpTargetPoints <= 0 || InpStopLossPoints <= 0)
   {
      if(InpVerboseLogging)
         Print("Alvo e stop loss devem ser positivos");
      return false;
   }

   if(InpSlippageConfidenceLevel < 50 || InpSlippageConfidenceLevel > 95)
   {
      if(InpVerboseLogging)
         Print("Nível de confiança deve estar entre 50-95%: ", InpSlippageConfidenceLevel);
      return false;
   }

   if(InpBookImbalanceThreshold < 60 || InpBookImbalanceThreshold > 90)
   {
      if(InpVerboseLogging)
         Print("Threshold do book deve estar entre 60-90%: ", InpBookImbalanceThreshold);
      return false;
   }

   if(InpLearningTrades < 3 || InpLearningTrades > 20)
   {
      if(InpVerboseLogging)
         Print("Trades de aprendizado deve estar entre 3-20: ", InpLearningTrades);
      return false;
   }

   if(InpHistoryDays < 1 || InpHistoryDays > 30)
   {
      if(InpVerboseLogging)
         Print("Dias de histórico deve estar entre 1-30: ", InpHistoryDays);
      return false;
   }

   if(InpVerboseLogging)
      Print("Parâmetros validados - Alvo: ", InpTargetPoints, " SL: ", InpStopLossPoints, " Lote: ", InpLotSize);

   return true;
}

//+------------------------------------------------------------------+
//| Create error panel when validation fails                        |
//+------------------------------------------------------------------+
void CreateErrorPanel()
{
   //--- Main panel background (red for error)
   CreateRectangle("SlippagePanel_ErrorBG", PANEL_X, PANEL_Y, PANEL_WIDTH, 200,
                   clrDarkRed, BORDER_FLAT, 2, clrRed);

   //--- Title
   CreateLabel("SlippagePanel_ErrorTitle", PANEL_X + 10, PANEL_Y + 10,
               "SLIPPAGE HUNTER - ERRO", clrWhite, 12, "Arial Bold");

   //--- Error message
   CreateLabel("SlippagePanel_ErrorMsg1", PANEL_X + 10, PANEL_Y + 40,
               "❌ FALHA NA INICIALIZAÇÃO", clrYellow, 10, "Arial Bold");

   //--- Split error message into lines
   string error_lines[];
   int line_count = StringSplit(init_errors, ';', error_lines);

   int y_pos = PANEL_Y + 65;
   for(int i = 0; i < line_count && i < 5; i++) // Max 5 error lines
   {
      string trimmed_error = error_lines[i];
      trimmed_error.TrimLeft();
      trimmed_error.TrimRight();

      if(StringLen(trimmed_error) > 0)
      {
         string obj_name = "SlippagePanel_Error" + IntegerToString(i + 2);
         CreateLabel(obj_name, PANEL_X + 10, y_pos,
                    "• " + trimmed_error, clrWhite, 9);
         y_pos += 20;
      }
   }

   //--- Instructions
   CreateLabel("SlippagePanel_ErrorInstr", PANEL_X + 10, y_pos + 10,
               "Corrija os erros e reinicie o EA", clrLightGray, 9);

   panel_created = true;

   if(InpVerboseLogging)
      Print("Painel de erro criado");
}

//+------------------------------------------------------------------+
//| Verify book availability during runtime                         |
//+------------------------------------------------------------------+
bool VerifyBookAvailability()
{
   MqlBookInfo book[];

   //--- First check if we're still subscribed
   if(!MarketBookGet(_Symbol, book))
   {
      if(InpVerboseLogging)
         Print("Falha ao obter book - tentando resubscrever...");

      //--- Try to resubscribe
      if(MarketBookAdd(_Symbol))
      {
         Sleep(100); // Wait for data
         if(MarketBookGet(_Symbol, book))
         {
            if(InpVerboseLogging)
               Print("Resubscrição ao book bem-sucedida");
         }
         else
         {
            if(InpVerboseLogging)
               Print("Resubscrição falhou - book ainda indisponível");
            book_available = false;
            return false;
         }
      }
      else
      {
         if(InpVerboseLogging)
            Print("Falha na resubscrição ao book");
         book_available = false;
         return false;
      }
   }

   int book_size = ArraySize(book);
   if(book_size < InpBookDepthLevels * 2)
   {
      if(InpVerboseLogging)
         Print("Book com poucos níveis: ", book_size, " (mínimo: ", InpBookDepthLevels * 2, ")");
      book_available = false;
      return false;
   }

   //--- Check for both sides and validate data quality
   bool has_buy = false, has_sell = false;
   int valid_buy_levels = 0, valid_sell_levels = 0;

   for(int i = 0; i < MathMin(book_size, 20); i++) // Check first 20 levels
   {
      if(book[i].type == BOOK_TYPE_BUY && book[i].volume > 0)
      {
         has_buy = true;
         valid_buy_levels++;
      }
      else if(book[i].type == BOOK_TYPE_SELL && book[i].volume > 0)
      {
         has_sell = true;
         valid_sell_levels++;
      }
   }

   if(!has_buy || !has_sell)
   {
      if(InpVerboseLogging)
         Print("Book incompleto - Buy: ", has_buy, " (", valid_buy_levels, " níveis) Sell: ", has_sell, " (", valid_sell_levels, " níveis)");
      book_available = false;
      return false;
   }

   //--- Check if we have minimum required levels on each side
   if(valid_buy_levels < InpBookDepthLevels || valid_sell_levels < InpBookDepthLevels)
   {
      if(InpVerboseLogging)
         Print("Níveis insuficientes - Buy: ", valid_buy_levels, " Sell: ", valid_sell_levels, " (mínimo: ", InpBookDepthLevels, " cada)");
      book_available = false;
      return false;
   }

   book_available = true;
   return true;
}

//+------------------------------------------------------------------+
//| Load historical slippage data from MT5 history                  |
//+------------------------------------------------------------------+
void LoadHistoricalSlippage()
{
   string search_mode = InpUseAllTrades ? "TODAS as ordens" : "apenas ordens do EA";
   if(InpVerboseLogging)
      Print("Carregando histórico de slippage dos últimos ", InpHistoryDays, " dias (", search_mode, ")...");

   //--- Calculate time range
   datetime now = TimeCurrent();
   datetime from = now - InpHistoryDays * 24 * 3600;

   //--- Request history
   if(!HistorySelect(from, now))
   {
      if(InpVerboseLogging)
         Print("Falha ao carregar histórico de deals");
      return;
   }

   int total_deals = HistoryDealsTotal();
   if(total_deals == 0)
   {
      if(InpVerboseLogging)
         Print("Nenhum deal encontrado no histórico");
      return;
   }

   int loaded_count = 0;
   int our_deals = 0;

   //--- Process deals from newest to oldest
   for(int i = total_deals - 1; i >= 0 && loaded_count < InpSlippageHistorySize; i--)
   {
      ulong deal_ticket = HistoryDealGetTicket(i);
      if(deal_ticket == 0) continue;

      //--- Check if it's our deal (or all deals if configured)
      long deal_magic = HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);
      if(!InpUseAllTrades && deal_magic != InpMagicNumber) continue;

      our_deals++;

      //--- Get deal properties
      string deal_symbol = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);
      if(deal_symbol != _Symbol) continue;

      long deal_type = HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
      if(deal_type != DEAL_TYPE_BUY && deal_type != DEAL_TYPE_SELL) continue;

      double deal_price = HistoryDealGetDouble(deal_ticket, DEAL_PRICE);
      datetime deal_time = (datetime)HistoryDealGetInteger(deal_ticket, DEAL_TIME);

      //--- Try to find corresponding order to get requested price
      ulong order_ticket = HistoryDealGetInteger(deal_ticket, DEAL_ORDER);
      if(order_ticket == 0) continue;

      if(!HistoryOrderSelect(order_ticket)) continue;

      double order_price = HistoryOrderGetDouble(order_ticket, ORDER_PRICE_OPEN);
      long order_type = HistoryOrderGetInteger(order_ticket, ORDER_TYPE);

      //--- Skip if prices are invalid
      if(order_price <= 0 || deal_price <= 0) continue;

      //--- Calculate slippage
      double slippage_points = 0;
      ENUM_ORDER_TYPE slip_order_type = ORDER_TYPE_BUY;

      if(order_type == ORDER_TYPE_BUY)
      {
         slippage_points = (deal_price - order_price) / _Point;
         slip_order_type = ORDER_TYPE_BUY;
      }
      else if(order_type == ORDER_TYPE_SELL)
      {
         slippage_points = (order_price - deal_price) / _Point;
         slip_order_type = ORDER_TYPE_SELL;
      }
      else
         continue; // Skip market orders, stops, etc.

      //--- Store in history array
      int index = loaded_count % InpSlippageHistorySize;
      slippage_history[index].time = deal_time;
      slippage_history[index].requested_price = order_price;
      slippage_history[index].executed_price = deal_price;
      slippage_history[index].slippage_points = slippage_points;
      slippage_history[index].order_type = slip_order_type;
      slippage_history[index].is_valid = true;

      loaded_count++;

      if(InpVerboseLogging && loaded_count <= 5) // Show first 5 for verification
      {
         string magic_info = InpUseAllTrades ? StringFormat(" (Magic: %d)", deal_magic) : "";
         Print("Histórico carregado: ", EnumToString(slip_order_type),
               " Solicitado: ", DoubleToString(order_price, 5), " Executado: ", DoubleToString(deal_price, 5),
               " Slippage: ", DoubleToString(slippage_points, 1), " pts", magic_info);
      }
   }

   //--- Update counters
   slippage_history_count = loaded_count;

   if(InpVerboseLogging)
   {
      string deals_info = InpUseAllTrades ? StringFormat(" deals analisados (%d do EA)", our_deals) : " deals do EA encontrados";
      Print("Histórico carregado: ", loaded_count, " trades de slippage de ", total_deals, deals_info);

      if(loaded_count > 0)
      {
         double avg_buy = GetAverageSlippage(ORDER_TYPE_BUY);
         double avg_sell = GetAverageSlippage(ORDER_TYPE_SELL);
         double confidence = CalculateSlippageConfidence();

         Print("Slippage médio histórico - Compras: ", DoubleToString(avg_buy, 1),
               " pts, Vendas: ", DoubleToString(avg_sell, 1), " pts");
         Print("Confiança inicial baseada no histórico: ", DoubleToString(confidence, 1), "%");

         if(InpUseAllTrades)
            Print("ATENÇÃO: Usando dados de TODAS as ordens (manuais + EA) para análise inicial");
      }
   }
}
