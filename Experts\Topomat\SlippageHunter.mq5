//+------------------------------------------------------------------+
//|                                                SlippageHunter.mq5 |
//|                                  Copyright 2024, Augment Trading |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Augment Trading"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "EA que detecta e aproveita slippage para ganhos adicionais"

//--- Includes
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Input parameters
input group "=== CONFIGURAÇÕES PRINCIPAIS ==="
input bool     InpEnabled = true;                    // Ativar EA
input int      InpLotSize = 1;                       // Tamanho do lote (B3 - lotes inteiros)
input int      InpMagicNumber = 123456;              // Número mágico
input int      InpTargetPoints = 10;                 // Alvo em pontos (múltiplo de 5)
input int      InpStopLossPoints = 15;               // Stop loss em pontos (múltiplo de 5)

input group "=== DETECÇÃO DE SLIPPAGE ==="
input int      InpSlippageHistorySize = 20;          // Histórico de slippage (trades)
input double   InpMinSlippageThreshold = 5.0;        // Slippage mínimo para trigger (pontos - múltiplo de 5)
input double   InpSlippageConfidenceLevel = 70.0;    // Nível de confiança % para entrada
input int      InpTickSpeedThreshold = 5;            // Velocidade mínima de ticks/segundo

input group "=== ANÁLISE DE MERCADO ==="
input int      InpBookDepthLevels = 5;               // Níveis do book para análise
input double   InpBookImbalanceThreshold = 75.0;     // % desequilíbrio do book
input int      InpVolatilityPeriod = 14;             // Período para cálculo de volatilidade
input double   InpMaxSpreadPoints = 10.0;            // Spread máximo permitido (pontos - múltiplo de 5)

input group "=== GESTÃO DE RISCO ==="
input int      InpMaxPositions = 1;                  // Máximo de posições simultâneas
input double   InpMaxDailyLoss = 100.0;              // Perda máxima diária (pontos)
input int      InpCooldownSeconds = 30;              // Cooldown entre trades (segundos)

input group "=== PAINEL E NOTIFICAÇÕES ==="
input bool     InpShowPanel = true;                  // Mostrar painel informativo
input bool     InpSendNotifications = true;          // Enviar notificações push
input bool     InpVerboseLogging = true;             // Log detalhado

//--- Global variables
CTrade         trade;
CPositionInfo  position;
COrderInfo     order;

struct SlippageData
{
   datetime time;
   double   requested_price;
   double   executed_price;
   double   slippage_points;
   ENUM_ORDER_TYPE order_type;
   bool     is_valid;
};

SlippageData   slippage_history[];
int            slippage_history_count = 0;
datetime       last_trade_time = 0;
double         daily_loss_points = 0;
datetime       daily_reset_time = 0;
int            tick_count = 0;
datetime       tick_count_time = 0;

//--- Market analysis variables
double         last_bid = 0;
double         last_ask = 0;
int            consecutive_up_ticks = 0;
int            consecutive_down_ticks = 0;

//--- Panel variables
string         panel_objects[];
bool           panel_created = false;
const int      PANEL_WIDTH = 300;
const int      PANEL_HEIGHT = 400;
const int      PANEL_X = 20;
const int      PANEL_Y = 50;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Setup trade object
   trade.SetExpertMagicNumber(InpMagicNumber);
   trade.SetDeviationInPoints(10);
   trade.SetTypeFilling(ORDER_FILLING_IOC);
   
   //--- Initialize arrays
   ArrayResize(slippage_history, InpSlippageHistorySize);

   //--- Initialize struct array manually
   for(int i = 0; i < InpSlippageHistorySize; i++)
   {
      slippage_history[i].time = 0;
      slippage_history[i].requested_price = 0;
      slippage_history[i].executed_price = 0;
      slippage_history[i].slippage_points = 0;
      slippage_history[i].order_type = ORDER_TYPE_BUY;
      slippage_history[i].is_valid = false;
   }
   
   //--- Reset daily counters
   ResetDailyCounters();
   
   //--- Initial market data
   last_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   last_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

   //--- Create panel if enabled
   if(InpShowPanel)
      CreatePanel();

   if(InpVerboseLogging)
      Print("SlippageHunter EA iniciado - Versão 1.00 - B3 Edition");

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Remove panel objects
   if(panel_created)
      RemovePanel();

   if(InpVerboseLogging)
      Print("SlippageHunter EA finalizado - Motivo: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   if(!InpEnabled) return;
   
   //--- Check daily reset
   CheckDailyReset();
   
   //--- Check daily loss limit
   if(daily_loss_points >= InpMaxDailyLoss)
   {
      if(InpVerboseLogging)
         Print("Limite de perda diária atingido: ", daily_loss_points, " pontos");
      return;
   }
   
   //--- Update tick analysis
   UpdateTickAnalysis();
   
   //--- Check cooldown
   if(TimeCurrent() - last_trade_time < InpCooldownSeconds)
      return;
   
   //--- Check max positions
   if(CountOpenPositions() >= InpMaxPositions)
      return;
   
   //--- Check spread
   double spread = GetSpreadInPoints();
   if(spread > InpMaxSpreadPoints)
      return;
   
   //--- Analyze market conditions
   if(ShouldEnterTrade())
   {
      ENUM_ORDER_TYPE order_type = GetTradeDirection();
      if(order_type != WRONG_VALUE)
      {
         ExecuteTrade(order_type);
      }
   }

   //--- Update panel
   if(InpShowPanel && panel_created)
      UpdatePanel();
}

//+------------------------------------------------------------------+
//| Trade transaction function                                       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
   //--- Process only our trades
   if(request.magic != InpMagicNumber) return;
   
   //--- Record slippage data
   if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
   {
      RecordSlippageData(trans, request, result);
   }
   
   //--- Update daily loss on position close
   if(trans.type == TRADE_TRANSACTION_HISTORY_ADD)
   {
      UpdateDailyLoss(trans);
   }
}

//+------------------------------------------------------------------+
//| Update tick analysis                                             |
//+------------------------------------------------------------------+
void UpdateTickAnalysis()
{
   double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   
   //--- Count tick speed
   datetime current_time = TimeCurrent();
   if(current_time != tick_count_time)
   {
      if(current_time - tick_count_time == 1) // Same second
         tick_count++;
      else
      {
         tick_count = 1;
         tick_count_time = current_time;
      }
   }
   
   //--- Analyze price movement direction
   if(current_bid > last_bid)
   {
      consecutive_up_ticks++;
      consecutive_down_ticks = 0;
   }
   else if(current_bid < last_bid)
   {
      consecutive_down_ticks++;
      consecutive_up_ticks = 0;
   }
   
   last_bid = current_bid;
   last_ask = current_ask;
}

//+------------------------------------------------------------------+
//| Check if should enter trade                                      |
//+------------------------------------------------------------------+
bool ShouldEnterTrade()
{
   //--- Check tick speed
   if(tick_count < InpTickSpeedThreshold)
      return false;
   
   //--- Check slippage confidence
   double slippage_confidence = CalculateSlippageConfidence();
   if(slippage_confidence < InpSlippageConfidenceLevel)
      return false;
   
   //--- Check book imbalance
   double book_imbalance = CalculateBookImbalance();
   if(book_imbalance < InpBookImbalanceThreshold)
      return false;
   
   //--- Check momentum
   if(consecutive_up_ticks < 3 && consecutive_down_ticks < 3)
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| Get trade direction based on analysis                           |
//+------------------------------------------------------------------+
ENUM_ORDER_TYPE GetTradeDirection()
{
   double avg_slippage_buy = GetAverageSlippage(ORDER_TYPE_BUY);
   double avg_slippage_sell = GetAverageSlippage(ORDER_TYPE_SELL);
   
   //--- Favor direction with positive slippage history
   if(consecutive_up_ticks >= 3 && avg_slippage_buy > InpMinSlippageThreshold)
      return ORDER_TYPE_BUY;
   
   if(consecutive_down_ticks >= 3 && avg_slippage_sell > InpMinSlippageThreshold)
      return ORDER_TYPE_SELL;
   
   return WRONG_VALUE;
}

//+------------------------------------------------------------------+
//| Execute trade                                                    |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE order_type)
{
   double price = (order_type == ORDER_TYPE_BUY) ?
                  SymbolInfoDouble(_Symbol, SYMBOL_ASK) :
                  SymbolInfoDouble(_Symbol, SYMBOL_BID);

   //--- Normalize price for B3 (must end in 0 or 5)
   price = NormalizePriceB3(price);

   double sl = 0, tp = 0;

   if(order_type == ORDER_TYPE_BUY)
   {
      sl = NormalizePriceB3(price - InpStopLossPoints * _Point);
      tp = NormalizePriceB3(price + InpTargetPoints * _Point);
   }
   else
   {
      sl = NormalizePriceB3(price + InpStopLossPoints * _Point);
      tp = NormalizePriceB3(price - InpTargetPoints * _Point);
   }
   
   bool result = false;
   if(order_type == ORDER_TYPE_BUY)
      result = trade.Buy((double)InpLotSize, _Symbol, price, sl, tp, "SlippageHunter");
   else
      result = trade.Sell((double)InpLotSize, _Symbol, price, sl, tp, "SlippageHunter");
   
   if(result)
   {
      last_trade_time = TimeCurrent();
      
      if(InpSendNotifications)
      {
         string msg = StringFormat("🎯 SlippageHunter: %s %.2f lotes em %.5f", 
                                  (order_type == ORDER_TYPE_BUY) ? "COMPRA" : "VENDA",
                                  InpLotSize, price);
         SendNotification(msg);
      }
      
      if(InpVerboseLogging)
         Print("Trade executado: ", EnumToString(order_type), " em ", price);
   }
   else
   {
      if(InpVerboseLogging)
         Print("Erro ao executar trade: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
   }
}

//+------------------------------------------------------------------+
//| Record slippage data from trade execution                       |
//+------------------------------------------------------------------+
void RecordSlippageData(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
   if(trans.type != TRADE_TRANSACTION_DEAL_ADD) return;

   double requested_price = request.price;
   double executed_price = trans.price;
   double slippage_points = 0;

   //--- Calculate slippage in points
   if(request.type == ORDER_TYPE_BUY)
      slippage_points = (executed_price - requested_price) / _Point;
   else if(request.type == ORDER_TYPE_SELL)
      slippage_points = (requested_price - executed_price) / _Point;

   //--- Store in history array (circular buffer)
   int index = slippage_history_count % InpSlippageHistorySize;
   slippage_history[index].time = TimeCurrent();
   slippage_history[index].requested_price = requested_price;
   slippage_history[index].executed_price = executed_price;
   slippage_history[index].slippage_points = slippage_points;
   slippage_history[index].order_type = request.type;
   slippage_history[index].is_valid = true;

   slippage_history_count++;

   if(InpVerboseLogging)
   {
      Print("Slippage registrado: ", slippage_points, " pontos - ",
            "Solicitado: ", requested_price, " Executado: ", executed_price);
   }
}

//+------------------------------------------------------------------+
//| Calculate slippage confidence level                             |
//+------------------------------------------------------------------+
double CalculateSlippageConfidence()
{
   if(slippage_history_count == 0) return 0.0;

   int positive_slippage = 0;
   int total_valid = 0;
   int max_check = MathMin(slippage_history_count, InpSlippageHistorySize);

   for(int i = 0; i < max_check; i++)
   {
      if(slippage_history[i].is_valid)
      {
         total_valid++;
         if(slippage_history[i].slippage_points > 0)
            positive_slippage++;
      }
   }

   if(total_valid == 0) return 0.0;

   return (double)positive_slippage / total_valid * 100.0;
}

//+------------------------------------------------------------------+
//| Get average slippage for specific order type                    |
//+------------------------------------------------------------------+
double GetAverageSlippage(ENUM_ORDER_TYPE order_type)
{
   double total_slippage = 0;
   int count = 0;
   int max_check = MathMin(slippage_history_count, InpSlippageHistorySize);

   for(int i = 0; i < max_check; i++)
   {
      if(slippage_history[i].is_valid && slippage_history[i].order_type == order_type)
      {
         total_slippage += slippage_history[i].slippage_points;
         count++;
      }
   }

   return (count > 0) ? total_slippage / count : 0.0;
}

//+------------------------------------------------------------------+
//| Calculate book imbalance (simplified version)                   |
//+------------------------------------------------------------------+
double CalculateBookImbalance()
{
   //--- Get market depth
   MqlBookInfo book[];
   if(!MarketBookGet(_Symbol, book))
      return 50.0; // Neutral if can't get book data

   double buy_volume = 0, sell_volume = 0;
   int levels_to_check = MathMin(ArraySize(book), InpBookDepthLevels * 2);

   for(int i = 0; i < levels_to_check; i++)
   {
      if(book[i].type == BOOK_TYPE_BUY)
         buy_volume += (double)book[i].volume;
      else if(book[i].type == BOOK_TYPE_SELL)
         sell_volume += (double)book[i].volume;
   }

   double total_volume = buy_volume + sell_volume;
   if(total_volume == 0) return 50.0;

   return (buy_volume / total_volume) * 100.0;
}

//+------------------------------------------------------------------+
//| Count open positions with our magic number                      |
//+------------------------------------------------------------------+
int CountOpenPositions()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol && position.Magic() == InpMagicNumber)
            count++;
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| Get current spread in points                                    |
//+------------------------------------------------------------------+
double GetSpreadInPoints()
{
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   return (ask - bid) / _Point;
}

//+------------------------------------------------------------------+
//| Update daily loss tracking                                      |
//+------------------------------------------------------------------+
void UpdateDailyLoss(const MqlTradeTransaction& trans)
{
   if(trans.type != TRADE_TRANSACTION_HISTORY_ADD) return;

   //--- Get deal info
   if(HistoryDealSelect(trans.deal))
   {
      long deal_magic = HistoryDealGetInteger(trans.deal, DEAL_MAGIC);
      if(deal_magic != InpMagicNumber) return;

      double deal_profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
      if(deal_profit < 0)
      {
         daily_loss_points += MathAbs(deal_profit / _Point);

         if(InpVerboseLogging)
            Print("Perda diária atualizada: ", daily_loss_points, " pontos");
      }
   }
}

//+------------------------------------------------------------------+
//| Reset daily counters                                            |
//+------------------------------------------------------------------+
void ResetDailyCounters()
{
   datetime current_time = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(current_time, dt);
   dt.hour = 0;
   dt.min = 0;
   dt.sec = 0;

   datetime today_start = StructToTime(dt);

   if(daily_reset_time != today_start)
   {
      daily_loss_points = 0;
      daily_reset_time = today_start;

      if(InpVerboseLogging)
         Print("Contadores diários resetados");
   }
}

//+------------------------------------------------------------------+
//| Check if daily reset is needed                                  |
//+------------------------------------------------------------------+
void CheckDailyReset()
{
   static datetime last_check = 0;
   datetime current_time = TimeCurrent();

   if(current_time - last_check >= 3600) // Check every hour
   {
      ResetDailyCounters();
      last_check = current_time;
   }
}

//+------------------------------------------------------------------+
//| Normalize price for B3 (must end in 0 or 5)                    |
//+------------------------------------------------------------------+
double NormalizePriceB3(double price)
{
   //--- Convert to points
   long price_points = (long)MathRound(price / _Point);

   //--- Get last digit
   int last_digit = (int)(price_points % 10);

   //--- Adjust to nearest 0 or 5
   if(last_digit < 3)
      price_points = (price_points / 10) * 10; // Round down to 0
   else if(last_digit < 8)
      price_points = (price_points / 10) * 10 + 5; // Round to 5
   else
      price_points = ((price_points / 10) + 1) * 10; // Round up to next 0

   return price_points * _Point;
}

//+------------------------------------------------------------------+
//| Create information panel                                         |
//+------------------------------------------------------------------+
void CreatePanel()
{
   if(panel_created) return;

   //--- Main panel background
   CreateRectangle("SlippagePanel_BG", PANEL_X, PANEL_Y, PANEL_WIDTH, PANEL_HEIGHT,
                   clrDarkSlateGray, BORDER_FLAT, 1, clrSilver);

   //--- Title
   CreateLabel("SlippagePanel_Title", PANEL_X + 10, PANEL_Y + 10,
               "SLIPPAGE HUNTER - B3", clrWhite, 12, "Arial Bold");

   //--- Status labels
   int y_pos = PANEL_Y + 40;
   CreateLabel("SlippagePanel_Status", PANEL_X + 10, y_pos, "Status:", clrLightGray, 9);
   CreateLabel("SlippagePanel_StatusValue", PANEL_X + 80, y_pos, "Iniciando...", clrYellow, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_Confidence", PANEL_X + 10, y_pos, "Confiança:", clrLightGray, 9);
   CreateLabel("SlippagePanel_ConfidenceValue", PANEL_X + 80, y_pos, "0%", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_AvgSlippage", PANEL_X + 10, y_pos, "Slippage Médio:", clrLightGray, 9);
   CreateLabel("SlippagePanel_AvgSlippageValue", PANEL_X + 110, y_pos, "0 pts", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_TickSpeed", PANEL_X + 10, y_pos, "Velocidade:", clrLightGray, 9);
   CreateLabel("SlippagePanel_TickSpeedValue", PANEL_X + 80, y_pos, "0 t/s", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_BookBalance", PANEL_X + 10, y_pos, "Book Balance:", clrLightGray, 9);
   CreateLabel("SlippagePanel_BookBalanceValue", PANEL_X + 100, y_pos, "50%", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_Momentum", PANEL_X + 10, y_pos, "Momentum:", clrLightGray, 9);
   CreateLabel("SlippagePanel_MomentumValue", PANEL_X + 80, y_pos, "Neutro", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_Spread", PANEL_X + 10, y_pos, "Spread:", clrLightGray, 9);
   CreateLabel("SlippagePanel_SpreadValue", PANEL_X + 60, y_pos, "0 pts", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_Positions", PANEL_X + 10, y_pos, "Posições:", clrLightGray, 9);
   CreateLabel("SlippagePanel_PositionsValue", PANEL_X + 70, y_pos, "0", clrWhite, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_DailyLoss", PANEL_X + 10, y_pos, "Perda Diária:", clrLightGray, 9);
   CreateLabel("SlippagePanel_DailyLossValue", PANEL_X + 90, y_pos, "0 pts", clrLime, 9);

   y_pos += 25;
   CreateLabel("SlippagePanel_LastTrade", PANEL_X + 10, y_pos, "Último Trade:", clrLightGray, 9);
   CreateLabel("SlippagePanel_LastTradeValue", PANEL_X + 90, y_pos, "Nenhum", clrWhite, 9);

   //--- Separator line
   y_pos += 30;
   CreateRectangle("SlippagePanel_Line", PANEL_X + 10, y_pos, PANEL_WIDTH - 20, 1,
                   clrSilver, BORDER_FLAT, 1, clrSilver);

   //--- Trade history section
   y_pos += 15;
   CreateLabel("SlippagePanel_HistoryTitle", PANEL_X + 10, y_pos, "HISTÓRICO SLIPPAGE:", clrYellow, 10, "Arial Bold");

   y_pos += 25;
   CreateLabel("SlippagePanel_History1", PANEL_X + 10, y_pos, "Aguardando dados...", clrLightGray, 8);

   panel_created = true;

   if(InpVerboseLogging)
      Print("Painel SlippageHunter criado com sucesso");
}

//+------------------------------------------------------------------+
//| Update panel information                                         |
//+------------------------------------------------------------------+
void UpdatePanel()
{
   if(!panel_created) return;

   //--- Status
   string status = InpEnabled ? (ShouldEnterTrade() ? "PRONTO" : "AGUARDANDO") : "DESABILITADO";
   color status_color = InpEnabled ? (ShouldEnterTrade() ? clrLime : clrYellow) : clrRed;
   UpdateLabel("SlippagePanel_StatusValue", status, status_color);

   //--- Confidence
   double confidence = CalculateSlippageConfidence();
   UpdateLabel("SlippagePanel_ConfidenceValue", StringFormat("%.1f%%", confidence),
               confidence >= InpSlippageConfidenceLevel ? clrLime : clrOrange);

   //--- Average slippage
   double avg_slip_buy = GetAverageSlippage(ORDER_TYPE_BUY);
   double avg_slip_sell = GetAverageSlippage(ORDER_TYPE_SELL);
   double avg_slip = (avg_slip_buy + avg_slip_sell) / 2;
   UpdateLabel("SlippagePanel_AvgSlippageValue", StringFormat("%.1f pts", avg_slip),
               avg_slip > 0 ? clrLime : clrOrange);

   //--- Tick speed
   UpdateLabel("SlippagePanel_TickSpeedValue", StringFormat("%d t/s", tick_count),
               tick_count >= InpTickSpeedThreshold ? clrLime : clrOrange);

   //--- Book balance
   double book_balance = CalculateBookImbalance();
   UpdateLabel("SlippagePanel_BookBalanceValue", StringFormat("%.1f%%", book_balance),
               book_balance > 60 || book_balance < 40 ? clrLime : clrOrange);

   //--- Momentum
   string momentum = "Neutro";
   color momentum_color = clrWhite;
   if(consecutive_up_ticks >= 3) { momentum = StringFormat("Alta +%d", consecutive_up_ticks); momentum_color = clrLime; }
   else if(consecutive_down_ticks >= 3) { momentum = StringFormat("Baixa -%d", consecutive_down_ticks); momentum_color = clrRed; }
   UpdateLabel("SlippagePanel_MomentumValue", momentum, momentum_color);

   //--- Spread
   double spread = GetSpreadInPoints();
   UpdateLabel("SlippagePanel_SpreadValue", StringFormat("%.0f pts", spread),
               spread <= InpMaxSpreadPoints ? clrLime : clrRed);

   //--- Positions
   int positions = CountOpenPositions();
   UpdateLabel("SlippagePanel_PositionsValue", IntegerToString(positions),
               positions > 0 ? clrYellow : clrWhite);

   //--- Daily loss
   UpdateLabel("SlippagePanel_DailyLossValue", StringFormat("%.0f pts", daily_loss_points),
               daily_loss_points < InpMaxDailyLoss * 0.8 ? clrLime : clrRed);

   //--- Last trade
   if(last_trade_time > 0)
   {
      int seconds_ago = (int)(TimeCurrent() - last_trade_time);
      string time_text = seconds_ago < 60 ? StringFormat("%ds atrás", seconds_ago) :
                        seconds_ago < 3600 ? StringFormat("%dm atrás", seconds_ago/60) :
                        StringFormat("%dh atrás", seconds_ago/3600);
      UpdateLabel("SlippagePanel_LastTradeValue", time_text, clrWhite);
   }

   //--- Update slippage history
   UpdateSlippageHistory();
}

//+------------------------------------------------------------------+
//| Update slippage history display                                 |
//+------------------------------------------------------------------+
void UpdateSlippageHistory()
{
   if(slippage_history_count == 0) return;

   string history_text = "";
   int max_show = MathMin(slippage_history_count, 5);
   int start_index = (slippage_history_count - 1) % InpSlippageHistorySize;

   for(int i = 0; i < max_show; i++)
   {
      int index = (start_index - i + InpSlippageHistorySize) % InpSlippageHistorySize;
      if(slippage_history[index].is_valid)
      {
         string type_str = (slippage_history[index].order_type == ORDER_TYPE_BUY) ? "C" : "V";
         history_text += StringFormat("%s: %.1f pts | ", type_str, slippage_history[index].slippage_points);
      }
   }

   if(StringLen(history_text) > 0)
      history_text = StringSubstr(history_text, 0, StringLen(history_text) - 3); // Remove last " | "

   UpdateLabel("SlippagePanel_History1", history_text, clrLightGray);
}

//+------------------------------------------------------------------+
//| Create rectangle object                                          |
//+------------------------------------------------------------------+
void CreateRectangle(string name, int x, int y, int width, int height,
                    color bg_color, ENUM_BORDER_TYPE border, int border_width, color border_color)
{
   ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
   ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
   ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg_color);
   ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, border);
   ObjectSetInteger(0, name, OBJPROP_WIDTH, border_width);
   ObjectSetInteger(0, name, OBJPROP_COLOR, border_color);
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| Create label object                                              |
//+------------------------------------------------------------------+
void CreateLabel(string name, int x, int y, string text, color text_color, int font_size, string font_name = "Arial")
{
   ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_COLOR, text_color);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, font_size);
   ObjectSetString(0, name, OBJPROP_FONT, font_name);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| Update label text and color                                     |
//+------------------------------------------------------------------+
void UpdateLabel(string name, string text, color text_color)
{
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_COLOR, text_color);
}

//+------------------------------------------------------------------+
//| Remove all panel objects                                        |
//+------------------------------------------------------------------+
void RemovePanel()
{
   ObjectDelete(0, "SlippagePanel_BG");
   ObjectDelete(0, "SlippagePanel_Title");
   ObjectDelete(0, "SlippagePanel_Status");
   ObjectDelete(0, "SlippagePanel_StatusValue");
   ObjectDelete(0, "SlippagePanel_Confidence");
   ObjectDelete(0, "SlippagePanel_ConfidenceValue");
   ObjectDelete(0, "SlippagePanel_AvgSlippage");
   ObjectDelete(0, "SlippagePanel_AvgSlippageValue");
   ObjectDelete(0, "SlippagePanel_TickSpeed");
   ObjectDelete(0, "SlippagePanel_TickSpeedValue");
   ObjectDelete(0, "SlippagePanel_BookBalance");
   ObjectDelete(0, "SlippagePanel_BookBalanceValue");
   ObjectDelete(0, "SlippagePanel_Momentum");
   ObjectDelete(0, "SlippagePanel_MomentumValue");
   ObjectDelete(0, "SlippagePanel_Spread");
   ObjectDelete(0, "SlippagePanel_SpreadValue");
   ObjectDelete(0, "SlippagePanel_Positions");
   ObjectDelete(0, "SlippagePanel_PositionsValue");
   ObjectDelete(0, "SlippagePanel_DailyLoss");
   ObjectDelete(0, "SlippagePanel_DailyLossValue");
   ObjectDelete(0, "SlippagePanel_LastTrade");
   ObjectDelete(0, "SlippagePanel_LastTradeValue");
   ObjectDelete(0, "SlippagePanel_Line");
   ObjectDelete(0, "SlippagePanel_HistoryTitle");
   ObjectDelete(0, "SlippagePanel_History1");

   panel_created = false;

   if(InpVerboseLogging)
      Print("Painel SlippageHunter removido");
}
