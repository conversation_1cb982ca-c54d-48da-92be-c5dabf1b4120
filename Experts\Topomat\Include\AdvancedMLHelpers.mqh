//+------------------------------------------------------------------+
//|                                        AdvancedMLHelpers.mqh     |
//|                      Funções Auxiliares para ML Avançado        |
//|                       Comple<PERSON> do Sistema Principal           |
//+------------------------------------------------------------------+

#include <Math/Stat/Math.mqh>
#include "AdvancedMLSystem.mqh"

//+------------------------------------------------------------------+
//| IMPLEMENTAÇÃO DAS FUNÇÕES AUXILIARES DO ML AVANÇADO             |
//+------------------------------------------------------------------+

//--- Conversão de features para array
void ConvertFeaturesToArray(const AdvancedFeatures &features, double &array[])
{
    array[0] = features.volume_ratio;
    array[1] = features.direction_ratio;
    array[2] = features.spread_points;
    array[3] = features.rsi_value;
    array[4] = features.price_velocity;
    array[5] = features.volume_acceleration;
    array[6] = features.price_acceleration;
    array[7] = features.volatility_ratio;
    array[8] = features.trend_strength;
    array[9] = features.support_resistance;
    array[10] = features.candlestick_pattern;
    array[11] = features.fibonacci_level;
    array[12] = features.bollinger_position;
    array[13] = features.macd_divergence;
    array[14] = features.stochastic_value;
    array[15] = features.intraday_pattern;
    array[16] = features.weekday_factor;
    array[17] = features.month_seasonality;
    array[18] = features.hour_volatility;
    array[19] = features.session_momentum;
    array[20] = features.bid_ask_imbalance;
    array[21] = features.tick_imbalance;
    array[22] = features.order_flow_pressure;
    array[23] = features.liquidity_depth;
    array[24] = features.market_impact;
}

//--- Cálculo de divergência MACD
double CalculateMACDDivergence(const double &prices[], int size)
{
    if(size < 26) return 0.0;
    
    // Cálculo simplificado do MACD
    double ema12 = CalculateEMA(prices, size, 12);
    double ema26 = CalculateEMA(prices, size, 26);
    double macd = ema12 - ema26;
    
    // Verificar divergência (simplificado)
    if(size < 50) return 0.0;
    
    double prev_macd = CalculateEMA(prices, size-1, 12) - CalculateEMA(prices, size-1, 26);
    return macd - prev_macd;
}

//--- Cálculo de EMA
double CalculateEMA(const double &prices[], int size, int period)
{
    if(size < period) return prices[size-1];
    
    double multiplier = 2.0 / (period + 1.0);
    double ema = prices[size-period];
    
    for(int i = size-period+1; i < size; i++)
    {
        ema = (prices[i] - ema) * multiplier + ema;
    }
    
    return ema;
}

//--- Cálculo de Stochastic
double CalculateStochastic(const MqlRates &rates[], int size)
{
    if(size < 14) return 50.0;
    
    int period = 14;
    double highest = rates[size-period].high;
    double lowest = rates[size-period].low;
    
    for(int i = size-period+1; i < size; i++)
    {
        if(rates[i].high > highest) highest = rates[i].high;
        if(rates[i].low < lowest) lowest = rates[i].low;
    }
    
    double current_close = rates[size-1].close;
    
    if(highest == lowest) return 50.0;
    
    return ((current_close - lowest) / (highest - lowest)) * 100.0;
}

//--- Cálculo de padrão intraday
double CalculateIntradayPattern(int hour, int minute)
{
    double time_factor = (hour * 60 + minute) / 1440.0; // Normalizar para 0-1
    
    // Padrões conhecidos do mercado
    if(hour >= 9 && hour <= 10) return 0.8;      // Abertura forte
    if(hour >= 14 && hour <= 15) return 0.7;     // Movimento pré-fechamento
    if(hour >= 11 && hour <= 13) return 0.3;     // Almoço fraco
    if(hour >= 16 && hour <= 17) return 0.9;     // Fechamento forte
    
    return 0.5 + 0.3 * MathSin(time_factor * 2 * M_PI); // Padrão senoidal
}

//--- Cálculo de fator dia da semana
double CalculateWeekdayFactor(int day_of_week)
{
    switch(day_of_week)
    {
        case 1: return 0.7; // Segunda - moderado
        case 2: return 0.9; // Terça - forte
        case 3: return 0.8; // Quarta - bom
        case 4: return 0.9; // Quinta - forte
        case 5: return 0.6; // Sexta - fraco
        case 6: return 0.2; // Sábado - muito fraco
        case 0: return 0.1; // Domingo - muito fraco
        default: return 0.5;
    }
}

//--- Cálculo de sazonalidade mensal
double CalculateMonthSeasonality(int month)
{
    switch(month)
    {
        case 1: return 0.7;  // Janeiro
        case 2: return 0.8;  // Fevereiro
        case 3: return 0.9;  // Março
        case 4: return 0.8;  // Abril
        case 5: return 0.6;  // Maio
        case 6: return 0.5;  // Junho
        case 7: return 0.4;  // Julho
        case 8: return 0.3;  // Agosto
        case 9: return 0.8;  // Setembro
        case 10: return 0.9; // Outubro
        case 11: return 0.8; // Novembro
        case 12: return 0.5; // Dezembro
        default: return 0.5;
    }
}

//--- Cálculo de volatilidade por hora
double CalculateHourVolatility(int hour)
{
    // Baseado em padrões históricos do mercado
    switch(hour)
    {
        case 9: return 0.9;   // Abertura
        case 10: return 0.8;  // Pós-abertura
        case 11: return 0.6;  // Meio da manhã
        case 12: return 0.4;  // Almoço
        case 13: return 0.4;  // Pós-almoço
        case 14: return 0.7;  // Tarde
        case 15: return 0.9;  // Pré-fechamento
        case 16: return 0.8;  // Fechamento
        case 17: return 0.6;  // Pós-fechamento
        default: return 0.3;  // Outros horários
    }
}

//--- Cálculo de desequilíbrio bid/ask
double CalculateBidAskImbalance()
{
    MqlTick tick;
    if(!SymbolInfoTick(_Symbol, tick))
        return 0.0;
    
    double bid = tick.bid;
    double ask = tick.ask;
    double mid = (bid + ask) / 2.0;
    
    if(mid == 0) return 0.0;
    
    // Simular desequilíbrio baseado no spread
    double spread = ask - bid;
    double imbalance = (tick.last - mid) / spread;
    
    return MathMin(MathMax(imbalance, -1.0), 1.0);
}

//--- Cálculo de desequilíbrio de ticks
double CalculateTickImbalance()
{
    // Obter últimos ticks
    MqlTick ticks[];
    int tick_count = CopyTicks(_Symbol, ticks, COPY_TICKS_ALL, 0, 100);
    
    if(tick_count < 10) return 0.0;
    
    int upticks = 0, downticks = 0;
    
    for(int i = 1; i < tick_count; i++)
    {
        if(ticks[i].last > ticks[i-1].last)
            upticks++;
        else if(ticks[i].last < ticks[i-1].last)
            downticks++;
    }
    
    int total_ticks = upticks + downticks;
    if(total_ticks == 0) return 0.0;
    
    return (double)(upticks - downticks) / total_ticks;
}

//--- Cálculo de pressão do fluxo de ordens
double CalculateOrderFlowPressure()
{
    // Simulação baseada em volume e direção
    MqlRates rates[];
    int rates_count = CopyRates(_Symbol, PERIOD_M1, 0, 10, rates);
    
    if(rates_count < 5) return 0.0;
    
    double volume_pressure = 0.0;
    double total_volume = 0.0;
    
    for(int i = 0; i < rates_count; i++)
    {
        double volume = (double)rates[i].tick_volume;
        double direction = (rates[i].close > rates[i].open) ? 1.0 : -1.0;
        
        volume_pressure += volume * direction;
        total_volume += volume;
    }
    
    return (total_volume > 0) ? volume_pressure / total_volume : 0.0;
}

//--- Cálculo de profundidade de liquidez
double CalculateLiquidityDepth()
{
    // Simular baseado no volume médio
    MqlRates rates[];
    int rates_count = CopyRates(_Symbol, PERIOD_M1, 0, 20, rates);
    
    if(rates_count < 10) return 0.5;
    
    double total_volume = 0.0;
    for(int i = 0; i < rates_count; i++)
    {
        total_volume += (double)rates[i].tick_volume;
    }
    
    double avg_volume = total_volume / rates_count;
    double current_volume = (double)rates[rates_count-1].tick_volume;
    
    return MathMin(current_volume / avg_volume, 3.0) / 3.0;
}

//--- Análise de padrões de candlestick
double AnalyzeCandlestickPatterns(const MqlRates &rates[], int size)
{
    if(size < 5) return 0.5;
    
    double pattern_score = 0.0;
    int patterns_found = 0;
    
    // Verificar últimos 3 candles
    for(int i = size - 3; i < size; i++)
    {
        if(i < 0) continue;
        
        double body = MathAbs(rates[i].close - rates[i].open);
        double upper_shadow = rates[i].high - MathMax(rates[i].open, rates[i].close);
        double lower_shadow = MathMin(rates[i].open, rates[i].close) - rates[i].low;
        double total_range = rates[i].high - rates[i].low;
        
        if(total_range == 0) continue;
        
        // Doji
        if(body < total_range * 0.1)
        {
            pattern_score += 0.3;
            patterns_found++;
        }
        
        // Hammer/Hanging Man
        if(lower_shadow > body * 2 && upper_shadow < body * 0.5)
        {
            pattern_score += (rates[i].close > rates[i].open) ? 0.7 : 0.3;
            patterns_found++;
        }
        
        // Shooting Star
        if(upper_shadow > body * 2 && lower_shadow < body * 0.5)
        {
            pattern_score += (rates[i].close < rates[i].open) ? 0.7 : 0.3;
            patterns_found++;
        }
        
        // Engulfing
        if(i > 0)
        {
            double prev_body = MathAbs(rates[i-1].close - rates[i-1].open);
            if(body > prev_body * 1.5)
            {
                bool bullish_engulf = (rates[i].close > rates[i].open) && (rates[i-1].close < rates[i-1].open);
                bool bearish_engulf = (rates[i].close < rates[i].open) && (rates[i-1].close > rates[i-1].open);
                
                if(bullish_engulf)
                {
                    pattern_score += 0.8;
                    patterns_found++;
                }
                else if(bearish_engulf)
                {
                    pattern_score += 0.2;
                    patterns_found++;
                }
            }
        }
    }
    
    return (patterns_found > 0) ? pattern_score / patterns_found : 0.5;
}

//--- Cálculo de proximidade com níveis de Fibonacci
double CalculateFibonacciProximity(const double &prices[], int size)
{
    if(size < 20) return 0.5;
    
    // Encontrar máximo e mínimo dos últimos 20 períodos
    double highest = prices[size-20];
    double lowest = prices[size-20];
    
    for(int i = size-19; i < size; i++)
    {
        if(prices[i] > highest) highest = prices[i];
        if(prices[i] < lowest) lowest = prices[i];
    }
    
    double range = highest - lowest;
    if(range == 0) return 0.5;
    
    double current_price = prices[size-1];
    
    // Níveis de Fibonacci
    double fib_levels[] = {0.236, 0.382, 0.5, 0.618, 0.786};
    double min_distance = 1.0;
    
    for(int i = 0; i < ArraySize(fib_levels); i++)
    {
        double fib_price = lowest + (range * fib_levels[i]);
        double distance = MathAbs(current_price - fib_price) / range;
        
        if(distance < min_distance)
            min_distance = distance;
    }
    
    return 1.0 - min_distance; // Quanto mais próximo, maior o score
}

//--- Cálculo de suporte e resistência
double CalculateSupportResistance(const double &prices[], int size)
{
    if(size < 50) return 0.5;
    
    double current_price = prices[size-1];
    double support = current_price;
    double resistance = current_price;
    
    // Encontrar níveis de suporte e resistência
    for(int i = size-50; i < size-1; i++)
    {
        if(prices[i] < support) support = prices[i];
        if(prices[i] > resistance) resistance = prices[i];
    }
    
    // Verificar proximidade com níveis
    double range = resistance - support;
    if(range == 0) return 0.5;
    
    double support_distance = MathAbs(current_price - support) / range;
    double resistance_distance = MathAbs(current_price - resistance) / range;
    
    double min_distance = MathMin(support_distance, resistance_distance);
    
    return 1.0 - min_distance;
}

//+------------------------------------------------------------------+
//| IMPLEMENTAÇÃO DAS FUNÇÕES AUXILIARES DA CLASSE                   |
//+------------------------------------------------------------------+

//--- Coleta de dados históricos
bool CAdvancedMLSystem::CollectHistoricalData()
{
    Print("📊 Coletando dados históricos para treinamento...");
    
    // Coletar dados M1 dos últimos 30 dias
    MqlRates rates[];
    int rates_count = CopyRates(_Symbol, PERIOD_M1, 0, 43200, rates); // 30 dias * 24h * 60min
    
    if(rates_count < 1000)
    {
        Print("❌ Dados insuficientes: ", rates_count, " candles");
        return false;
    }
    
    Print("📈 Processando ", rates_count, " candles históricos...");
    
    m_sample_count = 0;
    
    // Processar cada candle
    for(int i = 100; i < rates_count - 1 && m_sample_count < MAX_SAMPLES; i++)
    {
        // Extrair features para este ponto
        AdvancedFeatures features = ExtractHistoricalFeatures(rates, i);
        
        // Determinar label (se movimento futuro foi positivo)
        double current_price = rates[i].close;
        double future_price = rates[i + 1].close;
        double label = (future_price > current_price) ? 1.0 : 0.0;
        
        // Armazenar amostra
        m_training_features[m_sample_count] = features;
        m_training_labels[m_sample_count] = label;
        m_sample_count++;
        
        // Log progresso
        if(m_sample_count % 1000 == 0)
        {
            Print("📊 Processado: ", m_sample_count, "/", MAX_SAMPLES, " amostras");
        }
    }
    
    Print("✅ Coleta concluída - ", m_sample_count, " amostras para treinamento");
    return (m_sample_count > 100);
}

//--- Extração de features históricas
AdvancedFeatures CAdvancedMLSystem::ExtractHistoricalFeatures(const MqlRates &rates[], int index)
{
    AdvancedFeatures features;
    ZeroMemory(features);
    
    if(index < 50) return features;
    
    // Arrays para cálculos
    double prices[50];
    double volumes[50];
    
    // Copiar dados
    for(int i = 0; i < 50; i++)
    {
        int src_index = index - 49 + i;
        if(src_index >= 0)
        {
            prices[i] = rates[src_index].close;
            volumes[i] = (double)rates[src_index].tick_volume;
        }
    }
    
    // Calcular features básicas
    features.volume_ratio = volumes[49] / CalculateMean(volumes, 49);
    features.price_velocity = CalculateVelocity(prices, 49);
    features.price_acceleration = CalculateAcceleration(prices, 49);
    features.volatility_ratio = CalculateVolatilityRatio(rates, index);
    features.trend_strength = CalculateTrendStrength(prices, 49);
    
    // Features técnicas
    features.rsi_value = CalculateRSI(prices, 49, 14);
    features.bollinger_position = CalculateBollingerPosition(prices, 49);
    features.macd_divergence = CalculateMACDDivergence(prices, 49);
    features.stochastic_value = CalculateStochastic(&rates[index-13], 14);
    
    // Features temporais
    MqlDateTime dt;
    TimeToStruct(rates[index].time, dt);
    features.intraday_pattern = CalculateIntradayPattern(dt.hour, dt.min);
    features.weekday_factor = CalculateWeekdayFactor(dt.day_of_week);
    features.month_seasonality = CalculateMonthSeasonality(dt.mon);
    features.hour_volatility = CalculateHourVolatility(dt.hour);
    
    // Features de padrões
    features.candlestick_pattern = AnalyzeCandlestickPatterns(&rates[index-4], 5);
    features.fibonacci_level = CalculateFibonacciProximity(prices, 49);
    features.support_resistance = CalculateSupportResistance(prices, 49);
    
    return features;
}

//--- Cálculo de volatilidade ratio
double CalculateVolatilityRatio(const MqlRates &rates[], int index)
{
    if(index < 20) return 1.0;
    
    double current_vol = rates[index].high - rates[index].low;
    double avg_vol = 0.0;
    
    for(int i = index - 19; i < index; i++)
    {
        avg_vol += rates[i].high - rates[i].low;
    }
    
    avg_vol /= 20.0;
    
    return (avg_vol > 0) ? current_vol / avg_vol : 1.0;
}

//--- Ajuste do normalizador
void CAdvancedMLSystem::FitNormalizer()
{
    if(m_sample_count == 0) return;
    
    // Inicializar arrays
    for(int f = 0; f < MAX_FEATURES; f++)
    {
        m_normalizer.means[f] = 0.0;
        m_normalizer.stds[f] = 0.0;
        m_normalizer.mins[f] = 1e10;
        m_normalizer.maxs[f] = -1e10;
    }
    
    // Calcular estatísticas
    for(int s = 0; s < m_sample_count; s++)
    {
        double features_array[MAX_FEATURES];
        ConvertFeaturesToArray(m_training_features[s], features_array);
        
        for(int f = 0; f < MAX_FEATURES; f++)
        {
            m_normalizer.means[f] += features_array[f];
            
            if(features_array[f] < m_normalizer.mins[f])
                m_normalizer.mins[f] = features_array[f];
            if(features_array[f] > m_normalizer.maxs[f])
                m_normalizer.maxs[f] = features_array[f];
        }
    }
    
    // Finalizar médias
    for(int f = 0; f < MAX_FEATURES; f++)
    {
        m_normalizer.means[f] /= m_sample_count;
    }
    
    // Calcular desvios padrão
    for(int s = 0; s < m_sample_count; s++)
    {
        double features_array[MAX_FEATURES];
        ConvertFeaturesToArray(m_training_features[s], features_array);
        
        for(int f = 0; f < MAX_FEATURES; f++)
        {
            double diff = features_array[f] - m_normalizer.means[f];
            m_normalizer.stds[f] += diff * diff;
        }
    }
    
    for(int f = 0; f < MAX_FEATURES; f++)
    {
        m_normalizer.stds[f] = MathSqrt(m_normalizer.stds[f] / m_sample_count);
        if(m_normalizer.stds[f] == 0) m_normalizer.stds[f] = 1.0;
    }
    
    m_normalizer.is_fitted = true;
    Print("✅ Normalizer ajustado com sucesso");
}

//--- Cross-validation
CrossValidationResult CAdvancedMLSystem::PerformCrossValidation()
{
    CrossValidationResult result;
    ZeroMemory(result);
    
    if(m_sample_count < CROSS_VALIDATION_FOLDS * 20)
    {
        Print("❌ Dados insuficientes para cross-validation");
        return result;
    }
    
    Print("🔄 Executando ", CROSS_VALIDATION_FOLDS, "-fold cross-validation...");
    
    int fold_size = m_sample_count / CROSS_VALIDATION_FOLDS;
    double total_accuracy = 0.0;
    
    for(int fold = 0; fold < CROSS_VALIDATION_FOLDS; fold++)
    {
        Print("📊 Fold ", fold + 1, "/", CROSS_VALIDATION_FOLDS);
        
        // Criar modelo temporário
        NeuralNetwork temp_model;
        ZeroMemory(temp_model);
        temp_model.learning_rate = 0.001;
        temp_model.momentum = 0.9;
        temp_model.l1_regularization = 0.001;
        temp_model.l2_regularization = 0.01;
        
        // Treinar modelo (implementação simplificada)
        TrainNeuralNetwork(temp_model, fold);
        
        // Calcular precisão
        double accuracy = CalculateAccuracy(temp_model);
        result.accuracy_scores[fold] = accuracy;
        total_accuracy += accuracy;
        
        if(accuracy > result.best_fold_accuracy)
        {
            result.best_fold_accuracy = accuracy;
            result.best_fold_index = fold;
        }
        
        Print("✅ Fold ", fold + 1, " - Precisão: ", DoubleToString(accuracy, 2), "%");
    }
    
    result.mean_accuracy = total_accuracy / CROSS_VALIDATION_FOLDS;
    
    // Calcular desvio padrão
    double variance = 0.0;
    for(int fold = 0; fold < CROSS_VALIDATION_FOLDS; fold++)
    {
        double diff = result.accuracy_scores[fold] - result.mean_accuracy;
        variance += diff * diff;
    }
    result.std_accuracy = MathSqrt(variance / CROSS_VALIDATION_FOLDS);
    
    Print("📈 CV Resultado - Média: ", DoubleToString(result.mean_accuracy, 2), 
          "% ± ", DoubleToString(result.std_accuracy, 2), "%");
    
    return result;
}

//--- Salvar modelo avançado
void CAdvancedMLSystem::SaveAdvancedModel()
{
    int file_handle = FileOpen(m_model_file, FILE_WRITE | FILE_BIN);
    if(file_handle == INVALID_HANDLE)
    {
        Print("❌ Erro ao salvar modelo: ", GetLastError());
        return;
    }
    
    // Salvar ensemble
    FileWriteStruct(file_handle, m_ensemble);
    FileWriteStruct(file_handle, m_normalizer);
    FileWriteInteger(file_handle, m_sample_count);
    
    FileClose(file_handle);
    Print("💾 Modelo avançado salvo com sucesso");
}

//--- Carregar modelo avançado
bool CAdvancedMLSystem::LoadAdvancedModel()
{
    int file_handle = FileOpen(m_model_file, FILE_READ | FILE_BIN);
    if(file_handle == INVALID_HANDLE)
    {
        Print("ℹ️ Modelo não encontrado, será criado novo");
        return false;
    }
    
    // Carregar ensemble
    FileReadStruct(file_handle, m_ensemble);
    FileReadStruct(file_handle, m_normalizer);
    m_sample_count = FileReadInteger(file_handle);
    
    FileClose(file_handle);
    
    Print("📂 Modelo avançado carregado - Precisão: ", 
          DoubleToString(m_ensemble.ensemble_accuracy, 2), "%");
    
    return true;
}

//--- Informações do modelo
string CAdvancedMLSystem::GetAdvancedModelInfo()
{
    string info = StringFormat("🧠 SISTEMA ML AVANÇADO\n");
    info += StringFormat("├─ Modelos: %d (Ensemble)\n", ENSEMBLE_MODELS);
    info += StringFormat("├─ Features: %d\n", MAX_FEATURES);
    info += StringFormat("├─ Neurônios: %d\n", HIDDEN_NEURONS);
    info += StringFormat("├─ Amostras: %d\n", m_sample_count);
    info += StringFormat("├─ Precisão: %.2f%%\n", m_ensemble.ensemble_accuracy);
    info += StringFormat("├─ Melhor Modelo: %d\n", m_ensemble.best_model_index);
    info += StringFormat("├─ Ensemble: %s\n", m_use_ensemble ? "SIM" : "NÃO");
    info += StringFormat("├─ Cross-Val: %s\n", m_use_cross_validation ? "SIM" : "NÃO");
    info += StringFormat("└─ Regularização: %s", m_use_regularization ? "SIM" : "NÃO");
    
    return info;
}

//+------------------------------------------------------------------+
//| FUNÇÕES DE UTILIDADE MATEMÁTICA                                  |
//+------------------------------------------------------------------+

//--- Função de ativação sigmoid
double Sigmoid(double x)
{
    return 1.0 / (1.0 + MathExp(-x));
}

//--- Derivada da sigmoid
double SigmoidDerivative(double x)
{
    return x * (1.0 - x);
}

//--- Função de ativação ReLU
double ReLU(double x)
{
    return MathMax(0.0, x);
}

//--- Derivada da ReLU
double ReLUDerivative(double x)
{
    return (x > 0) ? 1.0 : 0.0;
}

//--- Normalização min-max
double MinMaxNormalize(double value, double min_val, double max_val)
{
    if(max_val == min_val) return 0.5;
    return (value - min_val) / (max_val - min_val);
}

//--- Normalização Z-score
double ZScoreNormalize(double value, double mean, double std)
{
    if(std == 0) return 0.0;
    return (value - mean) / std;
}

//--- Clipping de valores
double ClipValue(double value, double min_val, double max_val)
{
    return MathMin(MathMax(value, min_val), max_val);
}

//--- Cálculo de entropia
double CalculateEntropy(const double &probabilities[], int size)
{
    double entropy = 0.0;
    for(int i = 0; i < size; i++)
    {
        if(probabilities[i] > 0)
        {
            entropy -= probabilities[i] * MathLog(probabilities[i]);
        }
    }
    return entropy;
}

//--- Cálculo de correlação
double CalculateCorrelation(const double &x[], const double &y[], int size)
{
    if(size < 2) return 0.0;
    
    double mean_x = CalculateMean(x, size);
    double mean_y = CalculateMean(y, size);
    
    double numerator = 0.0;
    double sum_x_sq = 0.0;
    double sum_y_sq = 0.0;
    
    for(int i = 0; i < size; i++)
    {
        double diff_x = x[i] - mean_x;
        double diff_y = y[i] - mean_y;
        
        numerator += diff_x * diff_y;
        sum_x_sq += diff_x * diff_x;
        sum_y_sq += diff_y * diff_y;
    }
    
    double denominator = MathSqrt(sum_x_sq * sum_y_sq);
    
    return (denominator > 0) ? numerator / denominator : 0.0;
}

//--- Cálculo de coeficiente de determinação (R²)
double CalculateRSquared(const double &actual[], const double &predicted[], int size)
{
    if(size < 2) return 0.0;
    
    double mean_actual = CalculateMean(actual, size);
    
    double ss_res = 0.0; // Soma dos quadrados dos resíduos
    double ss_tot = 0.0; // Soma total dos quadrados
    
    for(int i = 0; i < size; i++)
    {
        ss_res += (actual[i] - predicted[i]) * (actual[i] - predicted[i]);
        ss_tot += (actual[i] - mean_actual) * (actual[i] - mean_actual);
    }
    
    return (ss_tot > 0) ? 1.0 - (ss_res / ss_tot) : 0.0;
}

//--- Cálculo de erro médio absoluto
double CalculateMeanAbsoluteError(const double &actual[], const double &predicted[], int size)
{
    if(size == 0) return 0.0;
    
    double sum_error = 0.0;
    for(int i = 0; i < size; i++)
    {
        sum_error += MathAbs(actual[i] - predicted[i]);
    }
    
    return sum_error / size;
}

//--- Cálculo de erro quadrático médio
double CalculateMeanSquaredError(const double &actual[], const double &predicted[], int size)
{
    if(size == 0) return 0.0;
    
    double sum_error = 0.0;
    for(int i = 0; i < size; i++)
    {
        double error = actual[i] - predicted[i];
        sum_error += error * error;
    }
    
    return sum_error / size;
}

//--- Cálculo de precisão classificação
double CalculateClassificationAccuracy(const double &actual[], const double &predicted[], int size, double threshold = 0.5)
{
    if(size == 0) return 0.0;
    
    int correct = 0;
    for(int i = 0; i < size; i++)
    {
        bool actual_class = actual[i] > threshold;
        bool predicted_class = predicted[i] > threshold;
        
        if(actual_class == predicted_class)
            correct++;
    }
    
    return (double)correct / size * 100.0;
} 