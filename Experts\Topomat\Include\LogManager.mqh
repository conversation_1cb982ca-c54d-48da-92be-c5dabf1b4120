//+------------------------------------------------------------------+
//|                                                   LogManager.mqh |
//|                        Copyright 2024, Topomat Trading Systems   |
//|                                             https://topomat.com   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Topomat Trading Systems"
#property link      "https://topomat.com"

//+------------------------------------------------------------------+
//| SISTEMA DE LOGS ROBUSTO                                           |
//+------------------------------------------------------------------+

enum ENUM_LOG_LEVEL
{
   LOG_LEVEL_ERROR = 0,     // Apenas erros críticos
   LOG_LEVEL_WARNING = 1,   // Alertas e avisos
   LOG_LEVEL_INFO = 2,      // Informações gerais
   LOG_LEVEL_DEBUG = 3,     // Debug detalhado
   LOG_LEVEL_TRACE = 4      // Rastreamento completo
};

// Estrutura para análise de Stop Loss
struct TradeAnalysisData
{
    datetime abertura;
    double preco_entrada;
    double max_drawdown;  // Máximo passeio negativo
    double max_favorable;  // Máximo passeio positivo
    bool is_buy;
    double volume;
    double preco_fechamento;
    double resultado_pontos;
    double resultado_reais;
    string metodos_entrada;
    bool foi_pyramiding;
    datetime fechamento;
};

// Array para armazenar dados de análise
TradeAnalysisData g_trade_analysis[];
string g_current_log_date = "";
int g_file_handle_daily = INVALID_HANDLE;
int g_file_handle_analysis = INVALID_HANDLE;

//+------------------------------------------------------------------+
//| Classe para gerenciamento de logs                                |
//+------------------------------------------------------------------+

class CLogManager
{
private:
   string            m_log_directory;        // Diretório de logs
   string            m_trade_log_file;       // Arquivo log de trades
   string            m_error_log_file;       // Arquivo log de erros
   string            m_debug_log_file;       // Arquivo log de debug
   string            m_execution_log_file;   // Arquivo log completo de execução
   
   int               m_trade_file_handle;    // Handle arquivo trades
   int               m_error_file_handle;    // Handle arquivo erros
   int               m_debug_file_handle;    // Handle arquivo debug
   int               m_execution_file_handle; // Handle arquivo execução
   
   ENUM_LOG_LEVEL    m_log_level;           // Nível de log ativo
   bool              m_initialized;          // Status inicialização
   
   string            m_ea_name;             // Nome do EA
   int               m_magic_number;        // Magic number
   
public:
   //--- Construtor/Destrutor
                     CLogManager(void);
                    ~CLogManager(void);
   
   //--- Inicialização
   bool              Initialize(string ea_name, int magic_number, ENUM_LOG_LEVEL log_level = LOG_LEVEL_INFO);
   void              Shutdown(void);
   
   //--- Métodos de log principais
   void              LogTrade(string operation, string symbol, double price, double volume, 
                             double sl = 0, double tp = 0, string comment = "");
   void              LogError(string function_name, string error_msg, int error_code = 0);
   void              LogWarning(string function_name, string warning_msg);
   void              LogInfo(string info_msg);
   void              LogDebug(string debug_msg);
   void              LogTrace(string trace_msg);
   
   //--- Logs especializados
   void              LogSRAnalysis(int total_points, double strongest_level, int confirmations);
   void              LogPositionUpdate(string action, ulong ticket, double price, double volume, double pnl);
   void              LogRiskCheck(string check_type, bool passed, string details);
   void              LogPerformance(int total_trades, double total_pnl, double win_rate);
   
   //--- Log completo de execução
   void              LogExecution(string module, string action, string details);
   void              LogStrategyState(string state, int positions, double volume, double avg_price);
   void              LogContractsInfo(double today, double total, double max_day, double max_total);
   void              LogSystemStatus(string ea_state, string risk_state, bool trading_allowed);
   
   //--- Configurações
   void              SetLogLevel(ENUM_LOG_LEVEL level) { m_log_level = level; }
   ENUM_LOG_LEVEL    GetLogLevel(void) { return m_log_level; }
   bool              IsInitialized(void) { return m_initialized; }
   int               GetMagicNumber(void) { return m_magic_number; }
   
private:
   //--- Métodos auxiliares
   bool              CreateLogDirectory(void);
   string            GetLogFileName(string log_type);
   string            GetTimestamp(void);
   string            GetDateString(void);
   bool              WriteToFile(int file_handle, string message);
   string            FormatLogMessage(string level, string message);
   void              RotateLogFile(string file_path);
   bool              IsLogLevelEnabled(ENUM_LOG_LEVEL level);
};

//+------------------------------------------------------------------+
//| Construtor                                                        |
//+------------------------------------------------------------------+
CLogManager::CLogManager(void)
{
   m_log_directory = "Files";
   m_trade_file_handle = INVALID_HANDLE;
   m_error_file_handle = INVALID_HANDLE;
   m_debug_file_handle = INVALID_HANDLE;
   m_execution_file_handle = INVALID_HANDLE;
   m_log_level = LOG_LEVEL_INFO;
   m_initialized = false;
   m_ea_name = "";
   m_magic_number = 0;
}

//+------------------------------------------------------------------+
//| Destrutor                                                         |
//+------------------------------------------------------------------+
CLogManager::~CLogManager(void)
{
   Shutdown();
}

//+------------------------------------------------------------------+
//| Inicialização do sistema de logs                                  |
//+------------------------------------------------------------------+
bool CLogManager::Initialize(string ea_name, int magic_number, ENUM_LOG_LEVEL log_level = LOG_LEVEL_INFO)
{
   if(m_initialized)
      return true;
      
   m_ea_name = ea_name;
   m_magic_number = magic_number;
   m_log_level = log_level;
   
   // Criar diretório de logs
   Print("🔧 Criando diretório de logs: ", m_log_directory);
   if(!CreateLogDirectory())
   {
      Print("❌ Erro ao criar diretório de logs");
      return false;
   }
   Print("✅ Diretório de logs criado/verificado com sucesso");
   
   // Definir nomes dos arquivos
   string date_str = GetDateString();
   string time_str = StringSubstr(GetTimestamp(), 11, 8);
   StringReplace(time_str, ":", "");
   
   m_trade_log_file = GetLogFileName("trading_" + date_str);
   m_error_log_file = GetLogFileName("error_" + date_str);
   m_debug_log_file = GetLogFileName("debug_" + date_str);
   m_execution_log_file = GetLogFileName("execution_" + date_str + "_" + time_str);
   
   // Abrir arquivos de log
   Print("🔧 Tentando abrir arquivos de log...");
   Print("   - Trade log: ", m_trade_log_file);
   Print("   - Error log: ", m_error_log_file);
   Print("   - Execution log: ", m_execution_log_file);
   
   m_trade_file_handle = FileOpen(m_trade_log_file, FILE_WRITE | FILE_TXT | FILE_ANSI);
   if(m_trade_file_handle == INVALID_HANDLE)
   {
      int error = GetLastError();
      Print("❌ Erro ao abrir arquivo de trade: ", error);
      return false;
   }
   
   m_error_file_handle = FileOpen(m_error_log_file, FILE_WRITE | FILE_TXT | FILE_ANSI);
   if(m_error_file_handle == INVALID_HANDLE)
   {
      int error = GetLastError();
      Print("❌ Erro ao abrir arquivo de erro: ", error);
      return false;
   }
   
   m_execution_file_handle = FileOpen(m_execution_log_file, FILE_WRITE | FILE_TXT | FILE_ANSI);
   if(m_execution_file_handle == INVALID_HANDLE)
   {
      int error = GetLastError();
      Print("❌ Erro ao abrir arquivo de execução: ", error);
      return false;
   }
   
   if(m_log_level >= LOG_LEVEL_DEBUG)
   {
      m_debug_file_handle = FileOpen(m_debug_log_file, FILE_WRITE | FILE_TXT | FILE_ANSI);
      if(m_debug_file_handle == INVALID_HANDLE)
      {
         int error = GetLastError();
         Print("⚠️ Erro ao abrir arquivo de debug: ", error);
      }
   }
   
   Print("✅ Arquivos de log abertos com sucesso!");
   
   m_initialized = true;
   
   // Log inicial
   LogInfo("=== " + m_ea_name + " INICIADO ===");
   LogInfo("Magic Number: " + IntegerToString(m_magic_number));
   LogInfo("Log Level: " + EnumToString(m_log_level));
   LogInfo("Símbolo: " + _Symbol);
   LogInfo("Conta: " + IntegerToString(AccountInfoInteger(ACCOUNT_LOGIN)));
   LogInfo("Servidor: " + AccountInfoString(ACCOUNT_SERVER));
   
   // Log de execução inicial
   LogExecution("SYSTEM", "STARTUP", "TOPOMAT EA v3.0 iniciado com sucesso");
   LogExecution("CONFIG", "SYMBOL", "Símbolo: " + _Symbol);
   LogExecution("CONFIG", "ACCOUNT", "Conta: " + IntegerToString(AccountInfoInteger(ACCOUNT_LOGIN)));
   LogExecution("CONFIG", "BALANCE", "Saldo: " + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2));
   
   return true;
}

//+------------------------------------------------------------------+
//| Finalização do sistema de logs                                    |
//+------------------------------------------------------------------+
void CLogManager::Shutdown(void)
{
   if(!m_initialized)
      return;
      
   LogInfo("=== " + m_ea_name + " FINALIZADO ===");
   
   // Fechar arquivos
   if(m_trade_file_handle != INVALID_HANDLE)
   {
      FileClose(m_trade_file_handle);
      m_trade_file_handle = INVALID_HANDLE;
   }
   
   if(m_error_file_handle != INVALID_HANDLE)
   {
      FileClose(m_error_file_handle);
      m_error_file_handle = INVALID_HANDLE;
   }
   
   if(m_debug_file_handle != INVALID_HANDLE)
   {
      FileClose(m_debug_file_handle);
      m_debug_file_handle = INVALID_HANDLE;
   }
   
   if(m_execution_file_handle != INVALID_HANDLE)
   {
      FileClose(m_execution_file_handle);
      m_execution_file_handle = INVALID_HANDLE;
   }
   
   m_initialized = false;
}

//+------------------------------------------------------------------+
//| Log de operações de trading                                       |
//+------------------------------------------------------------------+
void CLogManager::LogTrade(string operation, string symbol, double price, double volume, 
                          double sl = 0, double tp = 0, string comment = "")
{
   if(!m_initialized || !IsLogLevelEnabled(LOG_LEVEL_INFO))
      return;
      
   string message = StringFormat("TRADE | %s | %s | Price: %.5f | Vol: %.2f | SL: %.5f | TP: %.5f | %s",
                                operation, symbol, price, volume, sl, tp, comment);
   
   WriteToFile(m_trade_file_handle, FormatLogMessage("TRADE", message));
   
   // Log também no terminal se debug estiver ativo
   if(m_log_level >= LOG_LEVEL_DEBUG)
      Print("📊 " + message);
}

//+------------------------------------------------------------------+
//| Log de erros                                                      |
//+------------------------------------------------------------------+
void CLogManager::LogError(string function_name, string error_msg, int error_code = 0)
{
   if(!m_initialized)
      return;
      
   string message = StringFormat("ERROR | %s | %s | Code: %d", 
                                function_name, error_msg, error_code);
   
   WriteToFile(m_error_file_handle, FormatLogMessage("ERROR", message));
   
   // Sempre imprimir erros no terminal
   Print("❌ " + message);
}

//+------------------------------------------------------------------+
//| Log de avisos                                                     |
//+------------------------------------------------------------------+
void CLogManager::LogWarning(string function_name, string warning_msg)
{
   if(!m_initialized || !IsLogLevelEnabled(LOG_LEVEL_WARNING))
      return;
      
   string message = StringFormat("WARNING | %s | %s", function_name, warning_msg);
   
   WriteToFile(m_error_file_handle, FormatLogMessage("WARNING", message));
   
   if(m_log_level >= LOG_LEVEL_DEBUG)
      Print("⚠️ " + message);
}

//+------------------------------------------------------------------+
//| Log de informações gerais                                         |
//+------------------------------------------------------------------+
void CLogManager::LogInfo(string info_msg)
{
   if(!m_initialized || !IsLogLevelEnabled(LOG_LEVEL_INFO))
      return;
      
   WriteToFile(m_trade_file_handle, FormatLogMessage("INFO", info_msg));
   
   if(m_log_level >= LOG_LEVEL_DEBUG)
      Print("ℹ️ " + info_msg);
}

//+------------------------------------------------------------------+
//| Log de debug                                                      |
//+------------------------------------------------------------------+
void CLogManager::LogDebug(string debug_msg)
{
   if(!m_initialized || !IsLogLevelEnabled(LOG_LEVEL_DEBUG))
      return;
      
   if(m_debug_file_handle != INVALID_HANDLE)
      WriteToFile(m_debug_file_handle, FormatLogMessage("DEBUG", debug_msg));
      
   Print("🐛 " + debug_msg);
}

//+------------------------------------------------------------------+
//| Log de rastreamento                                               |
//+------------------------------------------------------------------+
void CLogManager::LogTrace(string trace_msg)
{
   if(!m_initialized || !IsLogLevelEnabled(LOG_LEVEL_TRACE))
      return;
      
   if(m_debug_file_handle != INVALID_HANDLE)
      WriteToFile(m_debug_file_handle, FormatLogMessage("TRACE", trace_msg));
}

//+------------------------------------------------------------------+
//| Log especializado para análise S&R                               |
//+------------------------------------------------------------------+
void CLogManager::LogSRAnalysis(int total_points, double strongest_level, int confirmations)
{
   if(!m_initialized || !IsLogLevelEnabled(LOG_LEVEL_INFO))
      return;
      
   string message = StringFormat("SR_ANALYSIS | Total Points: %d | Strongest: %.5f | Confirmations: %d",
                                total_points, strongest_level, confirmations);
   
   LogInfo(message);
}

//+------------------------------------------------------------------+
//| Log de atualização de posições                                   |
//+------------------------------------------------------------------+
void CLogManager::LogPositionUpdate(string action, ulong ticket, double price, double volume, double pnl)
{
   if(!m_initialized || !IsLogLevelEnabled(LOG_LEVEL_INFO))
      return;
      
   string message = StringFormat("POSITION | %s | Ticket: %d | Price: %.5f | Vol: %.2f | PnL: %.2f",
                                action, ticket, price, volume, pnl);
   
   LogTrade(action, _Symbol, price, volume, 0, 0, "Ticket: " + IntegerToString(ticket) + " PnL: " + DoubleToString(pnl, 2));
}

//+------------------------------------------------------------------+
//| Log de verificação de risco                                       |
//+------------------------------------------------------------------+
void CLogManager::LogRiskCheck(string check_type, bool passed, string details)
{
   if(!m_initialized || !IsLogLevelEnabled(LOG_LEVEL_INFO))
      return;
      
   string status = passed ? "PASSED" : "FAILED";
   string message = StringFormat("RISK_CHECK | %s | %s | %s", check_type, status, details);
   
   if(passed)
      LogInfo(message);
   else
      LogWarning("RiskManager", message);
}

//+------------------------------------------------------------------+
//| Log de performance                                               |
//+------------------------------------------------------------------+
void CLogManager::LogPerformance(int total_trades, double total_pnl, double win_rate)
{
   if(!m_initialized || !IsLogLevelEnabled(LOG_LEVEL_INFO))
      return;
      
   string message = StringFormat("PERFORMANCE | Trades: %d | PnL: %.2f | Win Rate: %.1f%%",
                                total_trades, total_pnl, win_rate);
   
   LogInfo(message);
}

//+------------------------------------------------------------------+
//| MÉTODOS AUXILIARES PRIVADOS                                       |
//+------------------------------------------------------------------+

bool CLogManager::CreateLogDirectory(void)
{
   if(!FolderCreate(m_log_directory, 0))
   {
      int error = GetLastError();
      if(error != 5019) // 5019 = pasta já existe
      {
         Print("ERRO: Falha ao criar pasta de logs: ", error);
         return false;
      }
   }
   return true;
}

string CLogManager::GetLogFileName(string log_type)
{
   return m_log_directory + "/" + m_ea_name + "_" + log_type + ".log";
}

string CLogManager::GetTimestamp(void)
{
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   return StringFormat("%04d-%02d-%02d %02d:%02d:%02d", 
                      dt.year, dt.mon, dt.day, dt.hour, dt.min, dt.sec);
}

string CLogManager::GetDateString(void)
{
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   return StringFormat("%04d-%02d-%02d", dt.year, dt.mon, dt.day);
}

bool CLogManager::WriteToFile(int file_handle, string message)
{
   if(file_handle == INVALID_HANDLE)
   {
      Print("ERRO LogManager: Handle de arquivo inválido!");
      return false;
   }
      
   string full_message = message + "\n";
   uint bytes_written = FileWriteString(file_handle, full_message);
   FileFlush(file_handle);
   
   if(bytes_written == 0)
   {
      int error = GetLastError();
      Print("ERRO LogManager: Falha ao escrever no arquivo. Erro: ", error);
      return false;
   }
   
   return true;
}

string CLogManager::FormatLogMessage(string level, string message)
{
   return StringFormat("[%s] %s: %s", GetTimestamp(), level, message);
}

bool CLogManager::IsLogLevelEnabled(ENUM_LOG_LEVEL level)
{
   return level <= m_log_level;
}

//+------------------------------------------------------------------+
//| FUNÇÕES DE LOG DE EXECUÇÃO COMPLETO                              |
//+------------------------------------------------------------------+

void CLogManager::LogExecution(string module, string action, string details)
{
   if(!m_initialized || m_execution_file_handle == INVALID_HANDLE)
      return;
      
   string message = StringFormat("[%s] %s::%s | %s", GetTimestamp(), module, action, details);
   WriteToFile(m_execution_file_handle, message);
}

void CLogManager::LogStrategyState(string state, int positions, double volume, double avg_price)
{
   if(!m_initialized)
      return;
      
   string details = StringFormat("Estado: %s | Posições: %d | Volume: %.2f | Preço Médio: %.5f", 
                                state, positions, volume, avg_price);
   LogExecution("STRATEGY", "STATE_UPDATE", details);
}

void CLogManager::LogContractsInfo(double today, double total, double max_day, double max_total)
{
   if(!m_initialized)
      return;
      
   string details = StringFormat("Hoje: %.1f/%.1f | Total: %.1f/%.1f", 
                                today, max_day, total, max_total);
   LogExecution("CONTRACTS", "UPDATE", details);
}

void CLogManager::LogSystemStatus(string ea_state, string risk_state, bool trading_allowed)
{
   if(!m_initialized)
      return;
      
   string status = trading_allowed ? "PERMITIDO" : "BLOQUEADO";
   string details = StringFormat("EA: %s | Risco: %s | Trading: %s", 
                                ea_state, risk_state, status);
   LogExecution("SYSTEM", "STATUS", details);
}

//+------------------------------------------------------------------+
//| INSTÂNCIA GLOBAL DO LOG MANAGER                                   |
//+------------------------------------------------------------------+

CLogManager g_log_manager; 

//+------------------------------------------------------------------+
//| Inicializa sistema de logs diários                               |
//+------------------------------------------------------------------+
bool InitializeDailyLogs()
{
    datetime now = TimeCurrent();
    string date_str = TimeToString(now, TIME_DATE);
    StringReplace(date_str, ".", "_");
    
    // Criar pasta Files se não existir
    Print("🔧 Criando pasta Files para logs diários...");
    if(!FolderCreate("Files", 0))
    {
        int error = GetLastError();
        if(error != 5019) // 5019 = pasta já existe
        {
            Print("❌ Erro ao criar pasta Files: ", error);
            return false;
        }
        else
        {
            Print("✅ Pasta Files já existia");
        }
    }
    else
    {
        Print("✅ Pasta Files criada com sucesso");
    }
    
    // Arquivo de log diário detalhado na pasta Files
    string daily_log_filename = "Files\\ScalperEsgotamento_" + date_str + ".txt";
    Print("🔧 Abrindo arquivo de log diário: ", daily_log_filename);
    g_file_handle_daily = FileOpen(daily_log_filename, FILE_WRITE|FILE_TXT|FILE_ANSI);
    
    if(g_file_handle_daily == INVALID_HANDLE)
    {
        Print("❌ ERRO: Não foi possível criar arquivo de log diário: ", daily_log_filename);
        int error = GetLastError();
        Print("❌ Detalhes do erro: ", error);
        return false;
    }
    Print("✅ Arquivo de log diário aberto com sucesso");
    
    // Arquivo de análise para Stop Loss na pasta Files
    string analysis_filename = "Files\\StopLoss_Analysis_" + date_str + ".txt";
    Print("🔧 Abrindo arquivo de análise: ", analysis_filename);
    g_file_handle_analysis = FileOpen(analysis_filename, FILE_WRITE|FILE_TXT|FILE_ANSI);
    
    if(g_file_handle_analysis == INVALID_HANDLE)
    {
        Print("❌ ERRO: Não foi possível criar arquivo de análise: ", analysis_filename);
        int error = GetLastError();
        Print("❌ Detalhes do erro: ", error);
        FileClose(g_file_handle_daily);
        return false;
    }
    Print("✅ Arquivo de análise aberto com sucesso");
    
    // Headers dos arquivos
    WriteToDaily("═══════════════════════════════════════════════════════════════");
    WriteToDaily("SCALPER ESGOTAMENTO EA - LOG DIÁRIO DETALHADO");
    WriteToDaily("Data: " + TimeToString(now, TIME_DATE|TIME_SECONDS));
    WriteToDaily("Símbolo: " + _Symbol);
    WriteToDaily("═══════════════════════════════════════════════════════════════");
    WriteToDaily("");
    
    WriteToAnalysis("═══════════════════════════════════════════════════════════════");
    WriteToAnalysis("ANÁLISE PARA IMPLEMENTAÇÃO DE STOP LOSS");
    WriteToAnalysis("Data: " + TimeToString(now, TIME_DATE|TIME_SECONDS));
    WriteToAnalysis("Objetivo: Coletar dados de passeio negativo para calibrar SL");
    WriteToAnalysis("═══════════════════════════════════════════════════════════════");
    WriteToAnalysis("FORMATO: [Hora] Operação | Entrada | Max.Negativo | Max.Positivo | Resultado");
    WriteToAnalysis("");
    
    g_current_log_date = date_str;
    return true;
}

//+------------------------------------------------------------------+
//| Escreve no log diário                                            |
//+------------------------------------------------------------------+
void WriteToDaily(string message)
{
    if(g_file_handle_daily != INVALID_HANDLE)
    {
        string timestamp = TimeToString(TimeCurrent(), TIME_SECONDS);
        string full_message = "[" + timestamp + "] " + message + "\n";
        uint bytes_written = FileWriteString(g_file_handle_daily, full_message);
        FileFlush(g_file_handle_daily);
        
        // Debug: verificar se escreveu corretamente
        if(bytes_written == 0)
        {
            int error = GetLastError();
            Print("ERRO ao escrever log diário: ", error, " - Mensagem: ", StringSubstr(message, 0, 50));
        }
    }
    else
    {
        Print("ERRO: Handle de arquivo diário inválido! Mensagem: ", StringSubstr(message, 0, 50));
    }
}

//+------------------------------------------------------------------+
//| Escreve no arquivo de análise                                    |
//+------------------------------------------------------------------+
void WriteToAnalysis(string message)
{
    if(g_file_handle_analysis != INVALID_HANDLE)
    {
        string full_message = message + "\n";
        uint bytes_written = FileWriteString(g_file_handle_analysis, full_message);
        FileFlush(g_file_handle_analysis);
        
        // Debug: verificar se escreveu corretamente
        if(bytes_written == 0)
        {
            int error = GetLastError();
            Print("ERRO ao escrever análise: ", error, " - Mensagem: ", StringSubstr(message, 0, 50));
        }
    }
    else
    {
        Print("ERRO: Handle de arquivo análise inválido! Mensagem: ", StringSubstr(message, 0, 50));
    }
}

//+------------------------------------------------------------------+
//| Inicia análise de um novo trade                                  |
//+------------------------------------------------------------------+
void StartTradeAnalysis(double entry_price, bool is_buy_trade, double trade_volume, string entry_methods, bool is_pyramiding = false)
{
    int size = ArraySize(g_trade_analysis);
    ArrayResize(g_trade_analysis, size + 1);
    
    g_trade_analysis[size].abertura = TimeCurrent();
    g_trade_analysis[size].preco_entrada = entry_price;
    g_trade_analysis[size].max_drawdown = 0.0;
    g_trade_analysis[size].max_favorable = 0.0;
    g_trade_analysis[size].is_buy = is_buy_trade;
    g_trade_analysis[size].volume = trade_volume;
    g_trade_analysis[size].metodos_entrada = entry_methods;
    g_trade_analysis[size].foi_pyramiding = is_pyramiding;
    
    WriteToDaily("╔═══ NOVA OPERAÇÃO INICIADA ═══");
    WriteToDaily("║ Tipo: " + (is_buy_trade ? "COMPRA" : "VENDA"));
    WriteToDaily("║ Preço Entrada: " + DoubleToString(entry_price, 0));
    WriteToDaily("║ Volume: " + DoubleToString(trade_volume, 1) + " contratos");
    WriteToDaily("║ Métodos: " + entry_methods);
    WriteToDaily("║ Pyramiding: " + (is_pyramiding ? "SIM" : "NÃO"));
    WriteToDaily("╚═══════════════════════════════");
}

//+------------------------------------------------------------------+
//| Atualiza análise durante o trade ativo                           |
//+------------------------------------------------------------------+
void UpdateTradeAnalysis()
{
    int size = ArraySize(g_trade_analysis);
    if(size == 0) return;
    
    double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    for(int i = size - 1; i >= 0; i--)
    {
        if(g_trade_analysis[i].fechamento == 0) // Trade ainda ativo
        {
            double current_price = g_trade_analysis[i].is_buy ? current_bid : current_ask;
            double entry_price = g_trade_analysis[i].preco_entrada;
            
            double movimento_pontos = 0;
            if(g_trade_analysis[i].is_buy)
                movimento_pontos = current_price - entry_price;
            else
                movimento_pontos = entry_price - current_price;
            
            // Atualizar máximos
            if(movimento_pontos > g_trade_analysis[i].max_favorable)
                g_trade_analysis[i].max_favorable = movimento_pontos;
                
            if(movimento_pontos < g_trade_analysis[i].max_drawdown)
                g_trade_analysis[i].max_drawdown = movimento_pontos;
        }
    }
}

//+------------------------------------------------------------------+
//| Finaliza análise de um trade                                     |
//+------------------------------------------------------------------+
void FinishTradeAnalysis(double close_price, double result_points, double result_reais)
{
    int size = ArraySize(g_trade_analysis);
    if(size == 0) return;
    
    // Encontrar o último trade ativo
    for(int i = size - 1; i >= 0; i--)
    {
        if(g_trade_analysis[i].fechamento == 0) // Trade ativo
        {
            g_trade_analysis[i].fechamento = TimeCurrent();
            g_trade_analysis[i].preco_fechamento = close_price;
            g_trade_analysis[i].resultado_pontos = result_points;
            g_trade_analysis[i].resultado_reais = result_reais;
            
            // Log detalhado de fechamento
            WriteToDaily("╔═══ OPERAÇÃO FINALIZADA ═══");
            WriteToDaily("║ Duração: " + IntegerToString((int)(g_trade_analysis[i].fechamento - g_trade_analysis[i].abertura) / 60) + " minutos");
            WriteToDaily("║ Preço Fechamento: " + DoubleToString(close_price, 0));
            WriteToDaily("║ Resultado: " + DoubleToString(result_points, 1) + " pontos | R$ " + DoubleToString(result_reais, 2));
            WriteToDaily("║ Max Favorável: +" + DoubleToString(g_trade_analysis[i].max_favorable, 1) + " pontos");
            WriteToDaily("║ Max Negativo: " + DoubleToString(g_trade_analysis[i].max_drawdown, 1) + " pontos");
            WriteToDaily("╚═══════════════════════════════");
            
            // Log para análise de Stop Loss
            string analysis_line = "[" + TimeToString(g_trade_analysis[i].abertura, TIME_MINUTES) + "] ";
            analysis_line += (g_trade_analysis[i].is_buy ? "COMPRA" : "VENDA") + " | ";
            analysis_line += DoubleToString(g_trade_analysis[i].preco_entrada, 0) + " | ";
            analysis_line += DoubleToString(g_trade_analysis[i].max_drawdown, 1) + " | ";
            analysis_line += "+" + DoubleToString(g_trade_analysis[i].max_favorable, 1) + " | ";
            analysis_line += DoubleToString(result_points, 1) + " pts";
            
            if(g_trade_analysis[i].foi_pyramiding)
                analysis_line += " (PYRAMID)";
                
            WriteToAnalysis(analysis_line);
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Gera relatório de análise para Stop Loss                         |
//+------------------------------------------------------------------+
void GenerateStopLossReport()
{
    int total_trades = 0;
    double soma_drawdown = 0;
    double max_drawdown_absoluto = 0;
    double media_drawdown = 0;
    int trades_negativos = 0;
    
    for(int i = 0; i < ArraySize(g_trade_analysis); i++)
    {
        if(g_trade_analysis[i].fechamento > 0) // Trade finalizado
        {
            total_trades++;
            soma_drawdown += MathAbs(g_trade_analysis[i].max_drawdown);
            
            if(g_trade_analysis[i].max_drawdown < max_drawdown_absoluto)
                max_drawdown_absoluto = g_trade_analysis[i].max_drawdown;
                
            if(g_trade_analysis[i].max_drawdown < 0)
                trades_negativos++;
        }
    }
    
    if(total_trades > 0)
    {
        media_drawdown = soma_drawdown / total_trades;
        
        WriteToAnalysis("");
        WriteToAnalysis("═══════════════════════════════════════════════════════════════");
        WriteToAnalysis("RELATÓRIO DE ANÁLISE PARA STOP LOSS");
        WriteToAnalysis("═══════════════════════════════════════════════════════════════");
        WriteToAnalysis("Total de Trades Analisados: " + IntegerToString(total_trades));
        WriteToAnalysis("Trades com Drawdown Negativo: " + IntegerToString(trades_negativos) + " (" + DoubleToString((double)trades_negativos/total_trades*100, 1) + "%)");
        WriteToAnalysis("Passeio Negativo Médio: " + DoubleToString(media_drawdown, 1) + " pontos");
        WriteToAnalysis("Máximo Passeio Negativo: " + DoubleToString(max_drawdown_absoluto, 1) + " pontos");
        WriteToAnalysis("");
        WriteToAnalysis("RECOMENDAÇÕES PARA STOP LOSS:");
        WriteToAnalysis("• Stop Loss Conservador: " + DoubleToString(MathAbs(max_drawdown_absoluto) + 5, 0) + " pontos");
        WriteToAnalysis("• Stop Loss Moderado: " + DoubleToString(media_drawdown * 2, 0) + " pontos");
        WriteToAnalysis("• Stop Loss Agressivo: " + DoubleToString(media_drawdown * 1.5, 0) + " pontos");
        WriteToAnalysis("═══════════════════════════════════════════════════════════════");
    }
}

//+------------------------------------------------------------------+
//| Fecha arquivos de log diários                                    |
//+------------------------------------------------------------------+
void CloseDailyLogs()
{
    GenerateStopLossReport();
    
    if(g_file_handle_daily != INVALID_HANDLE)
    {
        WriteToDaily("");
        WriteToDaily("═══════════════════════════════════════════════════════════════");
        WriteToDaily("FIM DO LOG DIÁRIO - " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));
        WriteToDaily("═══════════════════════════════════════════════════════════════");
        
        FileClose(g_file_handle_daily);
        g_file_handle_daily = INVALID_HANDLE;
    }
    
    if(g_file_handle_analysis != INVALID_HANDLE)
    {
        FileClose(g_file_handle_analysis);
        g_file_handle_analysis = INVALID_HANDLE;
    }
}

//+------------------------------------------------------------------+
//| Verifica se precisa trocar arquivo (mudança de dia)              |
//+------------------------------------------------------------------+
bool CheckDayChange()
{
    datetime now = TimeCurrent();
    string current_date = TimeToString(now, TIME_DATE);
    StringReplace(current_date, ".", "_");
    
    if(current_date != g_current_log_date)
    {
        CloseDailyLogs();
        return InitializeDailyLogs();
    }
    
    return true;
} 